using Sacrra.Membership.Database.Enums;

namespace Sacrra.Membership.Business.Resources.Camunda.Task
{
    public class TaskListResource
    {
        public string Id { get; set; }
        public string TaskName { get; set; }
        public string MemberRegisteredName { get; set; }
        public string RequestType { get; set; }
        public int MemberId { get; set; }
        public string TaskDefinitionKey { get; set; }
        public int SRNId { get; set; }
        public string ALGLeader { get; set; }
        public string TradingName { get; set; }
        public string SRNNumber { get; set; }
        public string StakeHolderManager { get; set; }
        public int FileSubmissionRequestId { get; set; }
        public bool IsPossibleTradingNameDuplicate { get; set; }
        public string Created { get; set; }
        public SRNStatusFileTypes FileType { get; set; }
        public int SRNStatusUpdateHistoryId { get; set; }
        public int SRNUpdateType { get; set; }
        public IndustryClassifications? SacrraIndustryCategory { get; set; }
        public MembershipTypes MembershipType { get; set; }
        public string? pastelOrAccountNumber { get; set; }
        public string? invoiceDate { get; set; }
        public string? invoiceNumber { get; set; }
        public string? invoiceAmount { get; set; }
        public string? commentOrRefrence { get; set; }
    }

    public class DWExceptionTaskItemResource : DWExceptionGetResource
    {
        public string TaskId { get; set; }
        public string TaskName { get; set; }
    }
}
