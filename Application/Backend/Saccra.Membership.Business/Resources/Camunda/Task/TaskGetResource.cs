using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;

namespace Sacrra.Membership.Business.Resources.Camunda.Task
{
    public class TaskGetResource : TaskBaseResource
    {
        public string RequestType { get; set; }
        public string TradingName { get; set; }
        public string SRNNumber { get; set; }
        public string StakeHolderManager { get; set; }
        public bool IsPossibleTradingNameDuplicate { get; set; }
        public int FileSubmissionRequestId { get; set; }
        public string FileType { get; set; }
        public int SRNStatusUpdateHistoryId { get; set; }
        public int SRNUpdateType { get; set; }
        public string Member { get; set; }
        public string ALGLeader { get; set; }
        public IndustryClassifications? SacrraIndustryCategory { get; set; }
        public MembershipTypes MembershipType { get; set; }
        public string? pastelOrAccountNumber { get; set; }
        public string? invoiceDate { get; set; }
        public string? invoiceNumber { get; set; }
        public string? invoiceAmount { get; set; }
        public string? commentOrRefrence { get; set; }
    }
}
