using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Migrations;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class LookupsRepository
    {
        private readonly AppDbContext _dbContext;
        private readonly IConfiguration _configuration;
        public IConfiguration Configuration { get; }
        public IMapper _mapper { get; }

        public LookupsRepository(AppDbContext dbContext, IMapper mapper, IConfiguration configuration)
        {
            _dbContext = dbContext;
            _configuration = configuration;
            this._mapper = mapper;
        }

        public IdValuePairResource GetEnumIdValuePair<T>(int id)
        {
            return EnumHelper.GetEnumIdValuePair<T>(id);
        }

        public List<IdValuePairResource> GetEnumIdValuePairs<T>()
        {
            return EnumHelper.GetEnumIdValuePairs<T>();
        }

        public async Task<List<IdValuePairResource>> GetMemberStatusReasons()
        {
            var getAllStatusReason = await _dbContext.Set<MemberStatusReason>().ToListAsync();
            var itemsToReturn = _mapper.Map<List<IdValuePairResource>>(getAllStatusReason);

            return itemsToReturn;
        }

        public async Task<List<IdValuePairResource>> GetSRNStatusReasons()
        {
            var getAllStatusReason = await _dbContext.Set<SRNStatusReason>().ToListAsync();
            var itemsToReturn = _mapper.Map<List<IdValuePairResource>>(getAllStatusReason);

            return itemsToReturn;
        }

        public async Task<List<IdValuePairResource>> GetMemberTypesByUserRole<T>(ClaimsPrincipal currentUser)
        {
            var types = EnumHelper.GetEnumIdValuePairs<T>();
            if (types != null)
            {
                var user = await Helpers.Helpers.GetLoggedOnUser(_dbContext, currentUser);
                if (user != null)
                {
                    if (user.RoleId != Database.Enums.UserRoles.FinancialAdministrator
                        && user.RoleId != Database.Enums.UserRoles.SACRRAAdministrator
                        && user.RoleId != Database.Enums.UserRoles.StakeHolderAdministrator
                        && user.RoleId != Database.Enums.UserRoles.StakeHolderManager
                        && user.RoleId != Database.Enums.UserRoles.ALGLeader)
                    {
                        types = types.Where(i => i.Value == "Full Member" || i.Value == "Non Member").ToList();
                    }
                    else if (user.RoleId == Database.Enums.UserRoles.FinancialAdministrator
                        || user.RoleId == Database.Enums.UserRoles.SACRRAAdministrator
                        || user.RoleId == Database.Enums.UserRoles.StakeHolderAdministrator
                        || user.RoleId == Database.Enums.UserRoles.StakeHolderManager)
                    {
                        types = types.Where(i => i.Value == "Full Member" || i.Value == "Non Member" || i.Value == "ALG Client").ToList();
                    }
                    else if (user.RoleId == Database.Enums.UserRoles.ALGLeader)
                    {
                        types = types.Where(i => i.Value == "ALG Client").ToList();
                    }

                    return types;
                }
            }
            return null;
        }

        public string GetCronitorRumKey()
        {
            var key = _configuration["Cronitor:RumKey"];
            return string.IsNullOrWhiteSpace(key) ? null : key;
        }

        public async Task<List<IdValuePairResource>> GetAnnualTurnOver()
        {
            var getAllTurnovers = await _dbContext.AnnualTurnoverConfig.ToListAsync();

            var result = getAllTurnovers
                .Select(t => new IdValuePairResource
                {
                    Id = t.Id,
                    Value = t.Name 
                })
                .ToList();

            return result;
        }


    }
}
