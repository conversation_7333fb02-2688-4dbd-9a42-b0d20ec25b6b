using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SPGroup;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.SRNContact;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database.publicShared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using RestSharp;
using Microsoft.AspNetCore.Mvc;

namespace Sacrra.Membership.Business.Repositories
{
    public class SRNRepository
    {
        private readonly AppDbContext _dbContext;
        private readonly SRNStatusRepository _statusRepository;
        private readonly ConfigSettings _configSettings;

        public IMapper _mapper { get; }
        private LookupsRepository _lookupsRepo;
        private readonly UserRepository _userRepository;
        private readonly SRNExtensions _srnExtensions;
        private readonly SRNStatusReasonRepository _statusReasonRepository;

        public SRNRepository(AppDbContext dbContext, IMapper mapper, SRNStatusRepository statusRepository,
            IOptions<ConfigSettings> configSettings, LookupsRepository lookupsRepo,
            UserRepository userRepository, SRNExtensions srnExtensions, SRNStatusReasonRepository statusReasonRepository)
        {
            _mapper = mapper;
            _statusRepository = statusRepository;
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _lookupsRepo = lookupsRepo;
            _userRepository = userRepository;
            _srnExtensions = srnExtensions;
            _statusReasonRepository = statusReasonRepository;
        }

        public async Task<SRNGetResource> Get(int id, SRNStatusFileTypes? fileType = null)
        {
            var selectRecord = await _dbContext.SRNs
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.Contacts)
                        .ThenInclude(i => i.ContactType)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Include(i => i.SRNStatusUpdates)
                    .Include(i => i.SRNStatusReason)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == id);

            if (selectRecord == null)
                return null;
            if (selectRecord.Id <= 0)
                return null;

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!selectRecord.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            var changeStatus = await _dbContext.MemberChangeRequests
                .FirstOrDefaultAsync(m => m.Type == ChangeObjectType.SRN && m.ObjectId == id);

            var returnRecord = _mapper.Map<SRNGetResource>(selectRecord);
            returnRecord.ChangeRequestStatus = (changeStatus != null) ? changeStatus.Status.ToString() : "No Change Request";
            PopulateSRNStatusUpdateHistory(returnRecord, selectRecord.SRNStatusUpdates, fileType);

            DefaultValueHelper<SRNGetResource>.GetDefaultValue(returnRecord);

            return returnRecord;
        }

        public async Task<SRNGetV2Resource> GetV2(int id, SRNStatusFileTypes? fileType = null)
        {
            var selectRecord = await _dbContext.SRNs
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.Contacts)
                        .ThenInclude(i => i.ContactType)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Include(i => i.SRNStatusUpdates)
                    .Include(i => i.SRNStatusReason)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == id);

            if (selectRecord == null)
                return null;
            if (selectRecord.Id <= 0)
                return null;

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!selectRecord.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            var changeStatus = await _dbContext.MemberChangeRequests
                .FirstOrDefaultAsync(m => m.Type == ChangeObjectType.SRN && m.ObjectId == id);

            var returnRecord = _mapper.Map<SRNGetV2Resource>(selectRecord);
            returnRecord.ChangeRequestStatus = (changeStatus != null) ? changeStatus.Status.ToString() : "No Change Request";
            PopulateSRNStatusUpdateHistoryV2(returnRecord, selectRecord.SRNStatusUpdates, fileType);

            DefaultValueHelper<SRNGetV2Resource>.GetDefaultValue(returnRecord);

            return returnRecord;
        }

        public async Task<PagedList<SRNGetResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                        .Include(i => i.Member)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .AsQueryable();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                query = query.Where(x => x.Member.Users.Any(i => i.UserId == user.Id));
            }

            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Member.RegisteredName.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Member.RegisteredName);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Member.RegisteredName);
                        break;
                }
            }

            var count = await query.CountAsync();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<SRNGetResource>>(queryItems).ToList();

            foreach(var item in itemsToReturn)
            {
                DefaultValueHelper<SRNGetResource>.GetDefaultValue(item);
            }

            itemsToReturn = itemsToReturn.OrderBy(x => x.Member.Value).ThenBy(x => x.SRNNumber).ToList();

            return new PagedList<SRNGetResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }

        public async Task<IEnumerable<SRNRolloutStatusResource>> ListSRNRolloutStatuses()
        {
            try
            {
                var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                using (var conn = _dbContext.Database.GetDbConnection())
                {

                    var sql = @"
                   SELECT 
                        [i].[Id], [DailyFileDevelopmentStartDate], [DailyFileDevelopmentEndDate], 
                        [DailyFileTestStartDate], [DailyFileTestEndDate], [DailyFileGoLiveDate], 
                        [MonthlyFileDevelopmentStartDate], [MonthlyFileDevelopmentEndDate], [MonthlyFileTestStartDate], 
                        [MonthlyFileTestEndDate], [MonthlyFileGoLiveDate],[SRNId], [StatusLastUpdatedAt],
                        [i.SRN].[SRNNumber], [i.SRN].[TradingName],
                        [i.SRN.Member].[RegisteredName], [i.SRN.Member].[RegisteredNumber],[i.SRN.Member].[IndustryClassificationId],
                        [i.SRN.Member.StakeholderManager].[FirstName] + ' ' + [i.SRN.Member.StakeholderManager].[LastName] AS StakeholderManager,
                        [i.SRN.ALGLeader].[RegisteredName] AS [ALGLeader],
                        [i.SRN.SPGroup].[SPNumber],
                        (CASE WHEN [i].[FileType] = 1 THEN 'Daily' 
							WHEN [i].[FileType] = 2 THEN 'Monthly'
							WHEN [i].[FileType] = 3 THEN 'Daily & Monthly'
						END) AS FileType,
                        RolloutStatus.[Name] AS RolloutStatus,
                        (CASE WHEN [i.SRN.Member].[IndustryClassificationId] = 1 THEN 'Furniture Retail' 
							WHEN [i.SRN.Member].[IndustryClassificationId] = 2 THEN 'Insurance'
							WHEN [i.SRN.Member].[IndustryClassificationId] = 3 THEN 'Retail Apparel'
							WHEN [i.SRN.Member].[IndustryClassificationId] = 4 THEN 'Secured'
							WHEN [i.SRN.Member].[IndustryClassificationId] = 5 THEN 'Unsecured'
							WHEN [i.SRN.Member].[IndustryClassificationId] = 6 THEN 'Telecoms'
							WHEN [i.SRN.Member].[IndustryClassificationId] = 7 THEN 'Other'
						END) AS IndustryClassification, 
						StatusReason.[Name] AS SRNStatusReason,
						[i].Comments AS Comment,
                        [i].[SignoffDate]
                    FROM
                    (
                        SELECT ROW_NUMBER() OVER (PARTITION BY SRNId ORDER BY DateCreated DESC) AS RowNumber,
                        [Id], [DailyFileDevelopmentStartDate], [DailyFileDevelopmentEndDate], [DailyFileTestStartDate], 
                        [DailyFileTestEndDate], [DailyFileGoLiveDate], [MonthlyFileDevelopmentStartDate], [MonthlyFileDevelopmentEndDate],
                        [MonthlyFileTestStartDate], [MonthlyFileTestEndDate], [MonthlyFileGoLiveDate], [IsDailyFileLive], 
                        [IsMonthlyFileLive], [IsLiveFileSubmissionsSuspended], [Comments], [BureauInstruction], [LastSubmissionDate], 
                        [DateCreated], [SRNId], [ProcessInstanceId], [IsComple], [DateCompleted], [FileType], [UpdateType], [SRNStatusId], 
                        [SRNStatusReasonId], [UpdateNumber], [RolloutStatusId], [SignoffDate]
                        FROM dbo.SRNStatusUpdateHistory
                    ) AS [i]
                    INNER JOIN [SRNs] AS [i.SRN] ON [i].[SRNId] = [i.SRN].[Id]
                    INNER JOIN [Members] AS [i.SRN.Member] ON [i.SRN].[MemberId] = [i.SRN.Member].[Id]
                    LEFT JOIN [Users] AS [i.SRN.Member.StakeholderManager] 
                        ON [i.SRN.Member].[StakeholderManagerId] = [i.SRN.Member.StakeholderManager].[Id]
                    LEFT JOIN [Members] AS [i.SRN.ALGLeader] ON [i.SRN].[ALGLeaderId] = [i.SRN.ALGLeader].[Id]
                    LEFT JOIN [SPGroups] AS [i.SRN.SPGroup] ON [i.SRN].[SPGroupId] = [i.SRN.SPGroup].[Id]
                    INNER JOIN [SRNStatuses] AS [i.SRNStatus] ON [i].[SRNStatusId] = [i.SRNStatus].[Id]
                    LEFT JOIN [RolloutStatuses] AS RolloutStatus ON [i].[RolloutStatusId] = RolloutStatus.Id
					LEFT JOIN [SRNStatusReasons] AS StatusReason on [i].[SRNStatusReasonId] = StatusReason.Id";

                    var sqlFilter = @" WHERE [i].RowNumber IN (1,2)
                        AND ([i.SRNStatus].[Name] = N'Test' 
                            OR [i.SRNStatus].[Name] = N'Test - DTH user info to be updated'
                            OR ([i.SRNStatus].[Name] = 'Live' AND [i].[DateCompleted] >= '2021-05-01')
						    OR ([i.SRNStatus].[Name] = 'Live - Missing information' AND [i].[DateCompleted] >= '2021-05-01'))";

                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        if(user.RoleId != UserRoles.Bureau)
                        {
                            sqlFilter += @" AND [i.SRN.Member].[Id] IN (SELECT MemberId FROM MemberUsers WHERE UserId = " + user.Id + ")";
                        }

                        //Get SRNs that are managed by the current leader only
                        if(user.RoleId == UserRoles.ALGLeader)
                        {

                            sqlFilter += @" AND [i.SRN].[ALGLeaderId] IN (SELECT MemberId 
												  FROM MemberUsers MU
												  INNER JOIN Members M ON MU.MemberId = M.Id
												  WHERE M.MembershipTypeId = 5
												  AND MU.UserId = " + user.Id + ")";
                        }
                    }

                    var sqlOrder = @" ORDER BY[i].SRNId, [i].RowNumber";

                    var result = await conn.QueryAsync<SRNRolloutStatusResource>(sql + sqlFilter + sqlOrder);

                    var rolloutSchedule = _mapper.Map<IEnumerable<SRNRolloutStatusResource>>(result).ToList();

                    return rolloutSchedule;
                }
            }
            catch(Exception ex)
            {
                Helpers.Helpers.LogError(_dbContext, ex, "Failed to list SRN Rollout Statuses.");
            }

            return null;
        }

        public async Task<IEnumerable<SRNRolloutStatusResource>> ExportSRNRolloutStatuses()
        {
            return await ListSRNRolloutStatuses();
        }

        public async Task<List<SRNSummaryResourceSimple>> ListSRNSummary()
        {
            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Comments = m.Comments,
                        CreationDate = m.CreationDate,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.ALGLeader.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        BureauInstruction = m.BureauInstruction
                    })
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

                var queryItems = query.ToList();
                var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryResourceSimple>>(queryItems).ToList();

                foreach (var item in itemsToReturn)
                {
                    DefaultValueHelper<SRNSummaryResourceSimple>.GetDefaultValue(item);
                }

                itemsToReturn = itemsToReturn.OrderBy(x => x.Member.RegisteredName).ThenBy(x => x.SRNNumber).ToList();

                return itemsToReturn;
            }

            else if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (user.RoleId == UserRoles.Member)
                {
                    var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(x => x.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Where(i => i.SRNStatus.Name != "Rejected" && i.Member.Users.Any(x => x.UserId == user.Id))
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.Member.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        BureauInstruction = m.BureauInstruction
                    })
                    .AsQueryable();

                    var queryItems = query.ToList();
                    var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryResourceSimple>>(queryItems).ToList();

                    foreach (var item in itemsToReturn)
                    {
                        DefaultValueHelper<SRNSummaryResourceSimple>.GetDefaultValue(item);
                    }

                    itemsToReturn = itemsToReturn.OrderBy(x => x.Member.RegisteredName).ThenBy(x => x.SRNNumber).ToList();

                    return itemsToReturn;
                }

                //Only return SRNs that are managed by the current ALG leader
                else if(user.RoleId == UserRoles.ALGLeader)
                {
                    var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.Member.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        BureauInstruction = m.BureauInstruction
                    })
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

                    var algLeader = await _dbContext.MemberUsers
                        .FirstOrDefaultAsync(i => i.UserId == user.Id 
                            && i.Member.MembershipTypeId == MembershipTypes.ALGLeader);

                    if(algLeader != null)
                    {
                        query = query.Where(x => x.ALGLeaderId == algLeader.MemberId);
                    }

                    var queryItems = query.ToList();
                    var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryResourceSimple>>(queryItems).ToList();

                    foreach (var item in itemsToReturn)
                    {
                        DefaultValueHelper<SRNSummaryResourceSimple>.GetDefaultValue(item);
                    }

                    itemsToReturn = itemsToReturn.OrderBy(x => x.Member.RegisteredName).ThenBy(x => x.SRNNumber).ToList();

                    return itemsToReturn;
                }

                else if (user.RoleId == UserRoles.Bureau)
                {
                    var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.Member.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        BureauInstruction = m.BureauInstruction
                    })
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

                    var algLeader = await _dbContext.MemberUsers
                        .FirstOrDefaultAsync(i => i.UserId == user.Id
                            && i.Member.MembershipTypeId == MembershipTypes.ALGLeader);

                    if (algLeader != null)
                    {
                        query = query.Where(x => x.ALGLeaderId == algLeader.MemberId);
                    }

                    var queryItems = query.ToList();
                    var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryResourceSimple>>(queryItems).ToList();

                    foreach (var item in itemsToReturn)
                    {
                        DefaultValueHelper<SRNSummaryResourceSimple>.GetDefaultValue(item);
                    }

                    itemsToReturn = itemsToReturn.OrderBy(x => x.Member.RegisteredName).ThenBy(x => x.SRNNumber).ToList();

                    return itemsToReturn;
                }
            }

            return new List<SRNSummaryResourceSimple>();
        }

        public async Task<List<SRNSummaryExportResource>> ExportSRNSummary()
        {
            var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                        .Include(i => i.Member)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Include(i => i.Contacts)
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            var realItems = new List<SRNSummaryExportResource>();

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (user.RoleId != UserRoles.Bureau)
                {
                    query = query.Where(x => x.Member.Users.Any(i => i.UserId == user.Id));
                }
            }

            var queryItems = query.ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryResource>>(queryItems).ToList();

            foreach (var item in itemsToReturn)
            {
                DefaultValueHelper<SRNSummaryResource>.GetDefaultValue(item);
                var exportItem = new SRNSummaryExportResource()
                {
                    ALGLeader = item.ALGLeader.Value,
                    CompanyRegNumber = item.Member.RegisteredNumber,
                    DataContributor = item.Member.RegisteredName,
                    SACRRAIndustryClassification = item.Member.IndustryClassification.Value,
                    SPNumber = item.SPGroup.Value,
                    SRNAccountType = item.AccountType.Value,
                    SRNNumber = item.SRNNumber,
                    SRNReportingAccountTypeClassification = item.NCRReportingAccountTypeClassification.Value,
                    SRNStatus = item.SRNStatus.Name,
                    StatusLastUpdatedAt = item.StatusLastUpdatedAt,
                    BureauClosureDate = item.BureauClosureDate,
                    SRNTradingName = item.TradingName,
                    StakeholderManager = item.StakeholderManager.Value
                };

                foreach (var contact in item.Contacts)
                {
                    if (contact.ContactTypeId == 5)
                    {
                        exportItem.DataContactCellNumber = contact.CellNumber;
                        exportItem.DataContactEmail = contact.Email;
                        exportItem.DataContactFirstName = contact.FirstName;
                        exportItem.DataContactOfficeNumber = contact.OfficeTelNumber;
                        exportItem.DataContactSurname = contact.Surname;
                    }
                }
                
                realItems.Add(exportItem);
            }

            realItems = realItems.OrderBy(x => x.SRNNumber).ToList();

            return realItems;
        }

        public async Task<SRNGetResource> Update(SRNUpdateResource modelForUpdate)
        {
            var srn = await _dbContext.SRNs
                .Include(x => x.Member)
                    .ThenInclude(x => x.Users)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == modelForUpdate.Id);

            //Do not update these fields
            modelForUpdate.MemberId = srn.MemberId;
            modelForUpdate.SRNStatusId = srn.SRNStatusId;
            modelForUpdate.ALGLeaderId = (modelForUpdate.ALGLeaderId > 0) ? modelForUpdate.ALGLeaderId : srn.ALGLeaderId;

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!srn.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();

                int changeRequestId = 0;
                bool isApprovalRequired = false;

                if (await DoSRNChangesRequireApproval(srn, modelForUpdate))
                {
                    isApprovalRequired = true;
                    changeRequestId = await CreateSRNChangeRequest(srn, modelForUpdate);
                }
                else
                {
                    await ApplySRNChanges(srn.Id, modelForUpdate);
                }

                await StartSRNUpdateWorkflow(srn, isApprovalRequired, changeRequestId);
            }
            else if (Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                await ApplySRNChanges(srn.Id, modelForUpdate);
            }

            return await Get(modelForUpdate.Id);
        }

        private async Task<bool> DoSRNChangesRequireApproval(SRN srn, SRNUpdateResource modelForUpdate)
        {
            bool isApprovalRequired = false;

            var fieldSettings = await _dbContext.SRNFieldUpdateSettings.ToListAsync();

            if (srn != null && modelForUpdate != null)
            {
                if (modelForUpdate != null)
                {
                    var updateModelType = modelForUpdate.GetType();
                    var srnModelType = srn.GetType();
                    IList<PropertyInfo> updatedProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                    IList<PropertyInfo> oldProperties = new List<PropertyInfo>(srnModelType.GetProperties());

                    foreach (PropertyInfo updatedProp in updatedProperties)
                    {
                        //If SRN dates from the linking table
                        if(updatedProp.DeclaringType == typeof(SRNDatesShared))
                        {
                            object updatedPropValue = updatedProp.GetValue(modelForUpdate, null);
                            var oldStatusUpdateProperty = oldProperties.FirstOrDefault(i => i.PropertyType == typeof(ICollection<SRNStatusUpdateHistory>));
                            var statusUpdateType = new SRNStatusUpdateHistory().GetType();
                            var oldStatusUpdateProperties = statusUpdateType.GetProperties();

                            var oldProp = oldStatusUpdateProperties.FirstOrDefault(i => i.Name == updatedProp.Name);

                            if (oldProp != null)
                            {
                                SRNStatusUpdateHistory recentSRNStatusUpdate = null;

                                //If there are no previous updates in the SRN Status Update History, create approval request
                                if(srn.SRNStatusUpdates.Count <= 0 && updatedPropValue != null)
                                {
                                    isApprovalRequired = true;
                                    break;
                                }
                                else
                                {
                                    recentSRNStatusUpdate = srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault();
                                }

                                if(recentSRNStatusUpdate != null)
                                {
                                    object oldPropValue = oldProp.GetValue(recentSRNStatusUpdate, null);

                                    if (oldPropValue != null)
                                    {
                                        var propType = oldPropValue.GetType();

                                        if (propType == typeof(DateTime) || propType == typeof(DateTime?))
                                        {
                                            if (updatedPropValue == null && propType == typeof(DateTime?))
                                                updatedPropValue = "";

                                            if (oldPropValue == null && propType == typeof(DateTime?))
                                                oldPropValue = "";

                                            if (updatedPropValue.ToString() != oldPropValue.ToString())
                                            {
                                                var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                                if (setting != null)
                                                {
                                                    if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                    {
                                                        isApprovalRequired = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            object updatedPropValue = updatedProp.GetValue(modelForUpdate, null);

                            var oldProp = oldProperties.FirstOrDefault(i => i.Name == updatedProp.Name);
                            if (oldProp != null)
                            {
                                object oldPropValue = oldProp.GetValue(srn, null);

                                if (oldPropValue != null)
                                {
                                    var propType = oldPropValue.GetType();
                                    if (propType.IsPrimitive || propType == (typeof(System.String)))
                                    {
                                        if (updatedPropValue == null && propType == (typeof(int)))
                                            updatedPropValue = 0;

                                        if (oldPropValue == null && propType == (typeof(int)))
                                            oldPropValue = 0;

                                        if (updatedPropValue.ToString() != oldPropValue.ToString())
                                        {
                                            var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                            if (setting != null)
                                            {
                                                if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                {
                                                    isApprovalRequired = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    else if (propType.IsEnum)
                                    {
                                        if ((int)updatedPropValue != (int)oldPropValue)
                                        {
                                            var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                            if (setting != null)
                                            {
                                                if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                {
                                                    isApprovalRequired = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return isApprovalRequired;
        }
        public async Task<List<int>> Create(List<SRNCreateResource> modelForCreate, string auth0Id)
        {
            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if(user == null)
                throw new InvalidUserException();

            int userId = user.Id;

            var srnStatusUpdateHistoryModel = _mapper.Map<List<SRNStatusUpdateHistory>>(modelForCreate);

            var model = _mapper.Map<List<SRN>>(modelForCreate);
            var status = await _statusRepository.GetByName("Requested");
            var statusReason = await _statusReasonRepository.GetByName("New development");

            var rolloutStatus = await _dbContext.RolloutStatuses
                .FirstOrDefaultAsync(i => i.Name == "Requested");

            List<int> srnIds = new List<int>();
            int? algLeaderId = null;

            foreach (var srn in model)
            {
                if(user.RoleId == UserRoles.ALGLeader)
                {
                    //Get the member that is managed by this user
                    var algLeader = await _dbContext.Members
                        .Include(x => x.Users)
                        .Where(i=>i.MembershipTypeId == MembershipTypes.ALGLeader)
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Users.Any(i => i.UserId == user.Id));

                    if (algLeader == null)
                        throw new InvalidSRNCreateNoALGLeader();

                    //Set the ALG Leader of the SRN to be the Member that is managed by the current user
                    if (algLeader != null)
                    {
                        srn.ALGLeaderId = algLeader.Id;
                        algLeaderId = algLeader.Id;
                    }
                }
                else
                {
                    algLeaderId = srn.ALGLeaderId;
                }
                srn.SRNStatusId = (status != null) ? status.Id : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;
                srn.LoanManagementSystemVendorId = (srn.LoanManagementSystemVendorId > 0) ? srn.LoanManagementSystemVendorId : null;

                foreach(var statusUpdate in srn.SRNStatusUpdates)
                {
                    statusUpdate.SRNStatusId = (status != null) ? status.Id : srn.SRNStatusId;
                    statusUpdate.SRNStatusReasonId = (statusReason != null) ? statusReason.Id : srn.SRNStatusReasonId;

                    statusUpdate.RolloutStatusId = (rolloutStatus != null) ? (int?)rolloutStatus.Id : null;
                }

                await _dbContext.Set<SRN>().AddAsync(srn);
            }

            List<string> processInstanceIds = new List<string>();

            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    await _dbContext.SaveChangesAsync();
                    
                    foreach (var srn in model)
                    {
                        srnIds.Add(srn.Id);

                        foreach (var update in srn.SRNStatusUpdates)
                        {
                            var testEndDate = "";
                            var goLiveDate = "";

                            if (update.FileType == SRNStatusFileTypes.DailyFile)
                            {
                                //If date is in the furture, subtract 3 days from the date
                                if (update.DailyFileTestStartDate > DateTime.Now)
                                {
                                    testEndDate = update.DailyFileTestStartDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    testEndDate = (update.DailyFileTestStartDate != null) ? update.DailyFileTestStartDate.Value.ToString("yyyy-MM-dd") : "";
                                }


                                if (update.DailyFileGoLiveDate > DateTime.Now)
                                {
                                    goLiveDate = update.DailyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    goLiveDate = (update.DailyFileGoLiveDate != null) ? update.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                                }
                            }
                            else if (update.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                //If date is in the furture, subtract 3 days from the date
                                if (update.MonthlyFileTestStartDate > DateTime.Now)
                                {
                                    testEndDate = update.MonthlyFileTestStartDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    testEndDate = (update.MonthlyFileTestStartDate != null) ? update.MonthlyFileTestStartDate.Value.ToString("yyyy-MM-dd") : "";
                                }


                                if (update.MonthlyFileGoLiveDate > DateTime.Now)
                                {
                                    goLiveDate = update.MonthlyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    goLiveDate = (update.DailyFileTestStartDate != null) ? update.DailyFileTestStartDate.Value.ToString("yyyy-MM-dd") : "";
                                }
                            }

                            var result = await StartNewSRNApplicationProcess(srn, update.FileType, testEndDate, goLiveDate);
                            if (result != null)
                            {
                                update.ProcessInstanceId = result.Id;
                                processInstanceIds.Add(result.Id);
                            }
                        }
                    }

                    await _dbContext.SaveChangesAsync();

                    foreach (var entity in modelForCreate)
                    {
                        var stagingChangeLog = new MemberStagingChangeLogResource();
                        entity.LoanManagementSystemVendorId = (entity.LoanManagementSystemVendorId > 0) ? entity.LoanManagementSystemVendorId : null;
                        entity.SoftwareVendorId = (entity.SoftwareVendorId > 0) ? entity.SoftwareVendorId : null;
                        entity.ALGLeaderId = algLeaderId == null ? entity.ALGLeaderId : algLeaderId;

                        var srn = model.FirstOrDefault(i => i.TradingName == entity.TradingName
                            && i.AccountTypeId == entity.AccountTypeId
                            && i.NumberOfActiveAccounts == entity.NumberOfActiveAccounts
                            && i.LoanManagementSystemVendorId == entity.LoanManagementSystemVendorId
                            && i.SoftwareVendorId == entity.SoftwareVendorId
                            && i.NCRReportingAccountTypeClassificationId == entity.NCRReportingAccountTypeClassificationId
                            && i.BillingCycleDay == entity.BillingCycleDay);

                        if (srn != null)
                        {
                            await CreateEventLog(entity, srn, stagingChangeLog);

                            var entityBlob = JsonConvert.SerializeObject(entity);
                            var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog);

                            await Helpers.Helpers.CreateEventLog(_dbContext, userId, "SRN Create", entity.TradingName, entityBlob, stagingBlob, srn.Id, "SRN");
                        }
                    }

                    transaction.Commit();

                    return srnIds;
                }
                catch
                {
                    if (processInstanceIds.Count > 0)
                    {
                        foreach(var id in processInstanceIds)
                        {
                            await DeleteProcessInstance(id);
                        }
                    }

                    throw new SRNCreationException();
                }
            }
        }
        public async Task Delete(int id)
        {
            var entity = await _dbContext.Set<SRN>().FindAsync(id);

            _dbContext.Set<SRN>().Remove(entity);
            await _dbContext.SaveChangesAsync();
        }
        public async Task CreateSRNNumber(int srnId)
        {
            var foundModel = await _dbContext.Set<SRN>()
                        .FirstOrDefaultAsync(i => i.Id == srnId);

            //If no SRN number created yet, create it. Otherwise, skip this step
            if (string.IsNullOrWhiteSpace(foundModel.SRNNumber))
            {
                var settings = _dbContext.SRNSettings.FirstOrDefault();

                if (settings == null)
                    throw new Exception("Unable to create SRN number. No SRN settings found");

                var newlyGeneratedSRN = SRNNumberGenerator.GenerateSRNNumber(settings);
                var newSRNNumber = newlyGeneratedSRN.Item1;

                if (newlyGeneratedSRN != null)
                {
                    var srn = await _dbContext.Set<SRN>()
                    .FirstOrDefaultAsync(i => i.SRNNumber == newSRNNumber);

                    var isSRNNumberAllowed = true;
                    if (!string.IsNullOrEmpty(settings.Exclusions))
                    {
                        var srnExclusions = settings.Exclusions.Split(',');
                        if (srnExclusions.Contains(newSRNNumber))
                            isSRNNumberAllowed = false;
                    }

                    while (srn != null || !isSRNNumberAllowed)
                    {
                        settings.LastGeneratedNumber += settings.Increment;
                        newlyGeneratedSRN = SRNNumberGenerator.GenerateSRNNumber(settings);
                        newSRNNumber = newlyGeneratedSRN.Item1;

                        srn = await _dbContext.Set<SRN>()
                            .FirstOrDefaultAsync(i => i.SRNNumber == newSRNNumber);

                        isSRNNumberAllowed = true;
                        if (!string.IsNullOrEmpty(settings.Exclusions))
                        {
                            var srnExclusions = settings.Exclusions.Split(',');
                            if (srnExclusions.Contains(newSRNNumber))
                                isSRNNumberAllowed = false;
                        }
                    }

                    foundModel.SRNNumber = newSRNNumber;
                    foundModel.CreationDate = DateTime.Now;
                    await _dbContext.SaveChangesAsync();
                }
            }
        }
        
        protected static CamundaRepository GetCamundaRepository(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<CamundaRepository>();
        }
        
        protected static async Task<int> GetIntegerVariable(ExternalTaskResource task, string varibaleName, IServiceScope serviceScope)
        {
            var srnTask = await task.Get();
            var srnVariables = await GetCamundaRepository(serviceScope).GetVariables(srnTask.ProcessInstanceId);
            var srnVariable = srnVariables.FirstOrDefault(i => i.Name == varibaleName);
            var srnId = (srnVariable != null) ? srnVariable.Value : "0";

            return Convert.ToInt32(srnId);
        }
        
        protected static async Task<string> GetGenericVariable(ExternalTaskResource task, string varibaleName, IServiceScope serviceScope)
        {
            var variableTask = await task.Get();
            var taskVariables = await GetCamundaRepository(serviceScope).GetMemberVariables(variableTask.ProcessInstanceId);
            var genericVariable = taskVariables.FirstOrDefault(i => i.Name == varibaleName);
            var variableValue = (genericVariable != null) ? genericVariable.Value : "0";

            return variableValue;
        }

        private async Task CreateMonthlyFileTestingWorkflow(SRN selectedSrn, Member member, ExternalTaskResource task, IServiceScope serviceScope, int fileType)
        {
            var testEndDate = DateTime.Parse(await GetGenericVariable(task, "testEndDate", serviceScope));
            var goLiveDate = DateTime.Parse(await GetGenericVariable(task, "goLiveDate", serviceScope));
            using var client = new HttpClient();
            var monthlyFileDevelopmentStartDate = DateTime.Parse(await GetGenericVariable(task, "MonthlyFileDevelopmentStartDate", serviceScope));
            var monthlyFileDevelopmentEndDate = DateTime.Parse(await GetGenericVariable(task, "MonthlyFileDevelopmentEndDate", serviceScope));
            var monthlyFileTestStartDate = DateTime.Parse(await GetGenericVariable(task, "MonthlyFileTestStartDate", serviceScope));
            var monthlyFileTestEndDate = DateTime.Parse(await GetGenericVariable(task, "MonthlyFileTestEndDate", serviceScope));
            var monthlyFileGoLiveDate = DateTime.Parse(await GetGenericVariable(task, "MonthlyFileGoLiveDate", serviceScope));
            var convertedTestEndDate = new DateTimeOffset(testEndDate.ToUniversalTime()).ToString("yyyy-MM-ddTHH:mm:ssZ");
            var variables = new
            {
                variables = new
                {
                    SRNId = new { value = selectedSrn.Id, type = "Long" },
                    srnNumber = new { value = selectedSrn.SRNNumber, type = "String" },
                    newSrn = new { value = true, type = "boolean" },
                    SRNUpdateType = new { value = 1, type = "Long" },
                    testEndDate = new { value = convertedTestEndDate, type = "String" },
                    stakeHolderManagerAssignee = new { value = member.StakeholderManagerId.ToString(), type = "String" },
                    fileType = new { value = fileType },
                    goLiveDate = new { value = goLiveDate },
                    MonthlyFileDevelopmentStartDate = new { value = monthlyFileDevelopmentStartDate },
                    MonthlyFileDevelopmentEndDate = new { value = monthlyFileDevelopmentEndDate },
                    MonthlyFileTestStartDate = new { value = monthlyFileTestStartDate },
                    MonthlyFileTestEndDate = new { value = monthlyFileTestEndDate },
                    MonthlyFileGoLiveDate = new { value = monthlyFileGoLiveDate }
                },
                businessKey = selectedSrn.SRNNumber
            };
            
            var json = JsonConvert.SerializeObject(variables);
            var uri = $"{_configSettings.CamundaBaseAddress}/process-definition/key/SRN-Status-Update-To-Test/start";
            var restClient = new RestClient(uri);
            var request = new RestRequest();
            request.AddJsonBody(json);
            restClient.Post(request);
        }
        
        private async Task CreateDailyFileTestingWorkflow(SRN selectedSrn, Member member, ExternalTaskResource task, IServiceScope serviceScope, int fileType)
        {
            var testEndDate = DateTime.Parse(await GetGenericVariable(task, "testEndDate", serviceScope));
            var goLiveDate = DateTime.Parse(await GetGenericVariable(task, "goLiveDate", serviceScope));
            using var client = new HttpClient();
            var dailyFileDevelopmentStartDate = DateTime.Parse(await GetGenericVariable(task, "DailyFileDevelopmentStartDate", serviceScope));
            var dailyFileDevelopmentEndDate = DateTime.Parse(await GetGenericVariable(task, "DailyFileDevelopmentEndDate", serviceScope));
            var dailyFileTestStartDate = DateTime.Parse(await GetGenericVariable(task, "DailyFileTestStartDate", serviceScope));
            var dailyFileTestEndDate = DateTime.Parse(await GetGenericVariable(task, "DailyFileTestEndDate", serviceScope));
            var dailyFileGoLiveDate = DateTime.Parse(await GetGenericVariable(task, "DailyFileGoLiveDate", serviceScope));
            var convertedTestEndDate = new DateTimeOffset(testEndDate.ToUniversalTime()).ToString("yyyy-MM-ddTHH:mm:ssZ");
            var variables = new
            {
                variables = new
                {
                    SRNId = new { value = selectedSrn.Id, type = "Long" },
                    srnNumber = new { value = selectedSrn.SRNNumber, type = "String" },
                    newSrn = new { value = true, type = "boolean" },
                    SRNUpdateType = new { value = 1, type = "Long" },
                    testEndDate = new { value = convertedTestEndDate, type = "String" },
                    stakeHolderManagerAssignee = new { value = member.StakeholderManagerId.ToString(), type = "String" },
                    fileType = new { value = fileType },
                    goLiveDate = new { value = goLiveDate },
                    DailyFileDevelopmentStartDate = new { value = dailyFileDevelopmentStartDate },
                    DailyFileDevelopmentEndDate = new { value = dailyFileDevelopmentEndDate },
                    DailyFileTestStartDate = new { value = dailyFileTestStartDate },
                    DailyFileTestEndDate = new { value = dailyFileTestEndDate },
                    DailyFileGoLiveDate = new { value = dailyFileGoLiveDate }
                },
                businessKey = selectedSrn.SRNNumber
            };
            
            var json = JsonConvert.SerializeObject(variables);
            var uri = $"{_configSettings.CamundaBaseAddress}/process-definition/key/SRN-Status-Update-To-Test/start";
            var restClient = new RestClient(uri);
            var request = new RestRequest();
            request.AddJsonBody(json);
            restClient.Post(request);
        }
        
        public async Task CallFileTestSubprocess(ExternalTaskResource task, IServiceScope serviceScope)
        {
            var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
            var selectedSrn = await _dbContext.SRNs
                .FirstOrDefaultAsync(i => i.Id == srnId);
            var member = _dbContext.Members
                .FirstOrDefault(m => m.Id == selectedSrn.MemberId);

            switch (selectedSrn.FileType)
            {
                case SRNStatusFileTypes.DailyFile:
                {
                    await CreateDailyFileTestingWorkflow(selectedSrn, member, task, serviceScope, (int)SRNStatusFileTypes.DailyFile);
                    break;
                }

                case SRNStatusFileTypes.MonthlyFile:
                {
                    await CreateMonthlyFileTestingWorkflow(selectedSrn, member, task, serviceScope, (int)SRNStatusFileTypes.MonthlyFile);
                    break;
                }
                
                case SRNStatusFileTypes.MonthlyAndDailyFile:
                { 
                    await CreateDailyFileTestingWorkflow(selectedSrn, member, task, serviceScope, (int)SRNStatusFileTypes.DailyFile);
                    await CreateMonthlyFileTestingWorkflow(selectedSrn, member, task, serviceScope, (int)SRNStatusFileTypes.MonthlyFile);
                    break;
                }
            }
        }

        public void UpdateSrnStatus(int srnId, string processInstanceId)
        {
            var selectedSrn = _dbContext.SRNs.FirstOrDefault(i => i.Id == srnId);
            var selectedSrnFile = _dbContext.vwSRNWithUpdateHistories
                .Where(i => i.ProcessInstanceId == processInstanceId)
                .FirstOrDefault(x => x.IsLatestHistory == 1);
            var selectedSrnFileList = _dbContext.vwSRNWithUpdateHistories
                .Where(i => i.SRNId == srnId)
                .ToList();

            if (selectedSrn == null)
                throw new Exception("Specified SRN not found.");
            
            if (selectedSrnFile == null)
                throw new Exception("Specified SRN file not found.");
            
            if (selectedSrnFileList == null)
                throw new Exception("Specified SRN file list not found.");
            
            // Scenario 1A
            // https://enterpriseworx.atlassian.net/wiki/spaces/S/pages/40632411/SRN+Statuses
            if (selectedSrn.SRNStatusId == 4
                && selectedSrnFileList.Count == 1
                && selectedSrnFile.HistoryFileType == (int)SRNStatusFileTypes.DailyFile
                && selectedSrnFile.IsLiveFileSubmissionsSuspended == false)
            {
                
            }
        }
          
        public async Task UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0, string processInstanceId = null, bool isNewSRN = false)
        {
            var srn = await _dbContext.Set<SRN>()
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusUpdates)
                .FirstOrDefaultAsync(i => i.Id == srnId);

            var newSRNStatus = await _statusRepository.GetByName(newStatus);
            var srnStatus = srn.SRNStatus;

            //This is a hack to get the current/old status
            //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
            if (srnStatus == null && srn.SRNStatusId > 0)
            {
                srnStatus = await _dbContext.SRNStatuses
                    .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
            }

            var oldStatus = (srnStatus != null) ? srnStatus.Name : "";

            var stagingChange = new StagingChange
            {
                Name = "SRN Status",
                OldValue = oldStatus,
                NewValue = newStatus
            };

            var rolloutStatus = await _dbContext.RolloutStatuses
                                .FirstOrDefaultAsync(i => i.Name == newStatus);

            if (srn != null && srn.SRNStatusUpdates.Count > 0 && !string.IsNullOrEmpty(processInstanceId))
            {
                var recentStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == processInstanceId);
                if(recentStatusUpdate != null)
                {
                    if (recentStatusUpdate.IsLiveFileSubmissionsSuspended || isNewSRN)
                    {
                        //If there are 2 files(daily and monthly)
                        if (isNewSRN && srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId != recentStatusUpdate.SRNStatusId)
                        {
                            recentStatusUpdate.SRNStatusId = newSRNStatus.Id;
                            recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;

                            await _dbContext.SaveChangesAsync();
                        }
                        else if(isNewSRN && (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile) 
                            || recentStatusUpdate.IsLiveFileSubmissionsSuspended 
                            || (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentStatusUpdate.SRNStatusId))
                        {
                            //This is a hack to get the current/old status
                            //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
                            if (srnStatus == null && srn.SRNStatusId > 0)
                            {
                                srnStatus = await _dbContext.SRNStatuses
                                    .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
                            }

                            srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                            srn.StatusLastUpdatedAt = DateTime.Now;

                            _dbContext.Attach(srn);
                            _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                            _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                            if (recentStatusUpdate != null)
                            {
                                recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                                recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;
                            }
                                
                            await _dbContext.SaveChangesAsync();
                        }
                        else
                        {
                            recentStatusUpdate.SRNStatusId = newSRNStatus.Id;
                            recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;

                            await _dbContext.SaveChangesAsync();
                        }
                    }
                    else if (recentStatusUpdate.IsLiveFileSubmissionsSuspended && !isNewSRN)
                    {
                        srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                        srn.StatusLastUpdatedAt = DateTime.Now;

                        _dbContext.Attach(srn);
                        _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                        _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                        if (recentStatusUpdate != null)
                        {
                            recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                            recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;
                        }

                        await _dbContext.SaveChangesAsync();

                    }
                    else if (!recentStatusUpdate.IsLiveFileSubmissionsSuspended)
                    {
                        recentStatusUpdate.SRNStatusId = newSRNStatus.Id;

                        //This will need to be changed in future.
                        //We need to check the file type that was selected before changing the 
                        //main SRN status.
                        if(newSRNStatus.Name == "Live" || newSRNStatus.Name == "Running Down" || newSRNStatus.Name == "Dormant")
                        {
                            srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                            srn.StatusLastUpdatedAt = DateTime.Now;

                            _dbContext.Attach(srn);
                            _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                            _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;


                            if (recentStatusUpdate != null && rolloutStatus != null)
                            {
                                recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                                recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;
                            }
                        }

                        await _dbContext.SaveChangesAsync();
                    }

                    if (srn.SRNStatusId != 2)
                    {
                        var newSrnStatusEntry = new SrnStatusHistory()
                        {
                            SrnId = srn.Id,
                            StatusId = srn.SRNStatusId,
                            StatusDate = DateTime.Now,
                            StatusReasonId = srn.SRNStatusReasonId
                        };

                        _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    }
                    
                    _dbContext.SaveChanges();

                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    stagingChangeLog.Changes.Add(stagingChange);

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                }

                else if (recentStatusUpdate == null 
                    && (newStatus == "Dormant" || newStatus == "Live" || newStatus == "Running Down" 
                    || newStatus == "Closure Pending" || newStatus == "Closed"))
                {
                    //This is a hack to get the current/old status
                    //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
                    if (srnStatus == null && srn.SRNStatusId > 0)
                    {
                        srnStatus = await _dbContext.SRNStatuses
                            .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
                    }

                    srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                    srn.StatusLastUpdatedAt = DateTime.Now;

                    _dbContext.Attach(srn);
                    _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                    _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                    if (srn.SRNStatusId != 2)
                    {
                        var newSrnStatusEntry = new SrnStatusHistory()
                        {
                            SrnId = srn.Id,
                            StatusId = srn.SRNStatusId,
                            StatusDate = DateTime.Now,
                            StatusReasonId = srn.SRNStatusReasonId
                        };

                        _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    }

                    _dbContext.SaveChanges();
                    
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    stagingChangeLog.Changes.Add(stagingChange);

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                }
            }

            else if (srn != null || (srn != null && isNewSRN))
            {
                //This is a hack to get the current/old status
                //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
                if (srnStatus == null && srn.SRNStatusId > 0)
                {
                    srnStatus = await _dbContext.SRNStatuses
                        .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
                }

                srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.Attach(srn);
                _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                var recentStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == processInstanceId);
                if (recentStatusUpdate != null)
                {
                    recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                    recentStatusUpdate.RolloutStatusId = (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;
                }

                if (srn.SRNStatusId != 2)
                {
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = srn.Id,
                        StatusId = srn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = srn.SRNStatusReasonId
                    };

                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                }

                await _dbContext.SaveChangesAsync();

                var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                var stagingChangeLog = new MemberStagingChangeLogResource();

                stagingChangeLog.Changes.Add(stagingChange);

                var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
            }
        }

        public async Task UpdateSRNStatus(int srnId, int statusId)
        {
            var srn = await _dbContext.Set<SRN>()
                        .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                srn.SRNStatusId = (statusId > 0) ? statusId : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.Attach(srn);
                _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                await _dbContext.SaveChangesAsync();
            }
        }

        public async Task UpdateSRNStatus(string newStatus, SRN srn)
        {
            if (srn != null)
            {
                var status = await _statusRepository.GetByName(newStatus);
                srn.SRNStatusId = (status != null) ? status.Id : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.Attach(srn);
                _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                await _dbContext.SaveChangesAsync();
            }
        }
        private async Task<ProcessInstanceInfoResource> StartNewSRNApplicationProcess(SRN srn, SRNStatusFileTypes? fileType, string testEndDate, string goLiveDate)
        {
            using (var client = new HttpClient())
            {
                var variables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "SRNId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", srn.Id.ToString() },
                                        { "type", "Long" }
                                    }
                                },
                                {
                                    "MemberId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", srn.MemberId.ToString() },
                                        { "type", "Long" }
                                    }
                                },
                                {
                                    "fileType",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", fileType.ToString() },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "testEndDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", testEndDate },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "goLiveDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", goLiveDate },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(variables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/New-SRN-Application/start";
                var result = await client.PostAsync(uri, content);

                result.EnsureSuccessStatusCode();

                var jsonResult = await result.Content.ReadAsStringAsync();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult);

                return processInfo;
            }
        }

        public async Task<List<SRNContactGetResource>> Contacts(int id)
        {
            var model = await _dbContext.Set<SRNContact>()
                .AsNoTracking()
                .Include(i => i.ContactType)
                .Include(i => i.SRN)
                    .ThenInclude(i => i.Member)
                        .ThenInclude(i => i.Users)
                .Where(i => i.SRNId == id)
                .ToListAsync();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if(model != null)
            {
                if(model.Count > 0)
                {
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                        model = model.Where(x => x.SRN.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                }
            }

            var contacts = _mapper.Map<List<SRNContactGetResource>>(model);

            return contacts;
        }
        public async Task<SRNContactGetResource> UpdateContact(int id, SRNContactUpdateResource modelForUpdate)
        {
            var currentContact = await _dbContext.SRNContacts
                .Include(i => i.SRN)
                    .ThenInclude(i => i.Member)
                        .ThenInclude(i => i.Users)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == id);

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!currentContact.SRN.Member.Users.Any(i => i.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            if (currentContact == null)
                throw new Exception("Unable to update contact");

            var model = _mapper.Map<SRNContact>(modelForUpdate);
            _dbContext.Set<SRNContact>().Update(model);
            await _dbContext.SaveChangesAsync();

            var updatedContact = await _dbContext.SRNContacts
                .FirstOrDefaultAsync(i => i.Id == id);

            var getReource = _mapper.Map<SRNContactGetResource>(updatedContact);
            return getReource;
        }
        public async Task<List<string>> GetTradingNames(int memberId)
        {
            var srns = await _dbContext.Set<SRN>()
                .Include(x => x.Member)
                    .ThenInclude(x => x.Users)
                .AsNoTracking()
                .Where(i => i.MemberId == memberId)
                .ToListAsync();

            if(srns != null)
            {
                if(srns.Count > 0)
                {
                    var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        srns = srns.Where(x => x.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                    }
                }
            }

            List<string> tradingNames = new List<string>();
            foreach (var srn in srns)
            {
                tradingNames.Add(srn.TradingName);
            }

            return tradingNames;
        }
        public async Task<List<IdValuePairResource>> CreateSPNumber(int memberId, ClaimsPrincipal user)
        {
            var selectRecord = await _dbContext.Members
                .Include(i => i.Users)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);

            var authUser = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(authUser))
            {
                if (!selectRecord.Users.Any(i => i.UserId == authUser.Id))
                    throw new UnauthorizedException();
            }

            if (selectRecord != null)
            {
                var spNumber = SPNumberGenerator.GenerateSPNumber();

                var sp = await _dbContext.Set<SPGroup>()
                    .FirstOrDefaultAsync(i => i.SPNumber == spNumber);

                //Lets also do a search by a combination of SPNumber and MemberId
                if(sp == null)
                {
                    sp = await _dbContext.Set<SPGroup>()
                    .FirstOrDefaultAsync(i => i.SPNumber == spNumber && i.MemberId == memberId);
                }
                
                while (sp != null)
                {
                    spNumber = SPNumberGenerator.GenerateSPNumber();
                    sp = await _dbContext.Set<SPGroup>()
                        .FirstOrDefaultAsync(i => i.SPNumber == spNumber);

                    //Lets also do a search by a combination of SPNumber and MemberId
                    if (sp == null)
                    {
                        sp = await _dbContext.Set<SPGroup>()
                        .FirstOrDefaultAsync(i => i.SPNumber == spNumber && i.MemberId == memberId);
                    }
                }

                var SPGroup = new SPGroup
                {
                    SPNumber = spNumber,
                    MemberId = memberId,
                    DateCreated = DateTime.Now
                };

                if (selectRecord.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    var algLeader = await _dbContext.ALGClientLeaders
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.ClientId == memberId);

                    if(algLeader != null)
                        SPGroup.MemberId = algLeader.LeaderId;
                }

                if (!string.IsNullOrWhiteSpace(spNumber))
                {
                    await _dbContext.Set<SPGroup>().AddAsync(SPGroup);
                    await _dbContext.SaveChangesAsync();
                }
                else
                {
                    throw new SPCreationException();
                }
                
                var localUser = await _dbContext.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Auth0Id == user.Identity.Name);

                if(localUser != null)
                {
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    var stagingChange = new StagingChange
                    {
                        Name = "SP Group Create",
                        OldValue = "",
                        NewValue = SPGroup.SPNumber
                    };
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    await Helpers.Helpers.CreateEventLog(_dbContext, localUser.Id, "SP Group Create", SPGroup.SPNumber, "", changeBlob, SPGroup.Id, "SP Group");
                }

                return await GetSPNumbers(SPGroup.MemberId);
            }

            return null;
        }

        public async Task<List<SPGroupGetResource>> GetSPGroups(int memberId)
        {
            var sps = await _dbContext.Set<SPGroup>()
                    .Include(i => i.SRNs)
                        .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.Users)
                    .AsNoTracking()
                    .Where(i => i.MemberId == memberId)
                    .ToListAsync();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if(sps != null)
            {
                if(sps.Count > 0)
                {
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        sps = sps.Where(x => x.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                    }
                }
            }

            var spsResource = _mapper.Map<List<SPGroupGetResource>>(sps);

            spsResource.Insert(0, new SPGroupGetResource { Id = 0, SPNumber = "None" });
            return spsResource;
        }

        public async Task<List<IdValuePairResource>> GetSPNumbers(int memberId)
        {
            var srnStatus = await _statusRepository.GetByName("Rejected");

            var member = await _dbContext.Members
                .Include(i => i.Users)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!member.Users.Any(i => i.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            var allSPGroups = new List<SPGroup>();

            if (member != null)
            {
                if (member.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    //Display SP numbers for all ALG leaders of the client
                    if (Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        var algLeaders = await _dbContext.ALGClientLeaders
                            .AsNoTracking()
                            .Where(i => i.ClientId == memberId)
                            .Select(b => new { b.LeaderId })
                            .ToListAsync();

                        foreach (var leader in algLeaders)
                        {
                            var sp = await _dbContext.Set<SPGroup>()
                                .Include(i => i.SRNs)
                                .Where(i => i.MemberId == leader.LeaderId)
                                .ToListAsync();

                            allSPGroups.AddRange(sp);
                        }
                    }
                    //Display SP numbers of the logged in user (ALG Leader)
                    else
                    {
                        var algLeaders = await _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(x => x.Users)
                            .AsNoTracking()
                            .Where(i => i.ClientId == memberId)
                            .ToListAsync();

                        if(algLeaders != null)
                        {
                            if(algLeaders.Count > 0)
                            {
                                var currentLeader = algLeaders
                                    .FirstOrDefault(i => i.Leader.Users.Any(x => x.UserId == user.Id));

                                if(currentLeader != null)
                                {
                                    allSPGroups = await _dbContext.Set<SPGroup>()
                                        .Include(i => i.SRNs)
                                        .AsNoTracking()
                                        .Where(i => i.MemberId == currentLeader.LeaderId)
                                        .ToListAsync();
                                }
                            }
                        }
                    }
                }
                else
                {
                    allSPGroups = await _dbContext.Set<SPGroup>()
                        .Include(i => i.SRNs)
                        .AsNoTracking()
                        .Where(i => i.MemberId == memberId)
                        .ToListAsync();
                }
            }

            var activeSPs = new List<SPGroup>();

            foreach (var sp in allSPGroups)
            {
                if (sp.SRNs.Count > 0)
                {
                    var numberOfActiveSRNs = 0;
                    foreach (var srn in sp.SRNs)
                    {
                        if (srn.SRNStatusId != srnStatus.Id)
                            numberOfActiveSRNs++;
                    }
                    if (numberOfActiveSRNs > 0)
                        activeSPs.Add(sp);
                }
                else
                {
                    activeSPs.Add(sp);
                }
            }

            var spsResource = _mapper.Map<List<IdValuePairResource>>(activeSPs);
            spsResource.Insert(0, new IdValuePairResource { Id = 0, Value = "None" });

            return spsResource;
        }

        public async Task<List<IdValuePairResource>> GetBranchLocations(int memberId)
        {
            var selectRecord = _dbContext.Set<BranchLocation>()
                    .Include(i => i.SRN)
                        .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.Users)
                    .Where(i => i.SRN.MemberId == memberId)
                    .AsNoTracking()
                    .ToList()
                    .GroupBy(i => i.Name).Select(i => i.First()) //To select distinct location names
                    .ToList();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if(selectRecord != null)
            {
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    selectRecord = selectRecord.Where(x => x.SRN.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                }
            }

            var returnRecord = _mapper.Map<List<IdValuePairResource>>(selectRecord);

            return returnRecord;
        }

        private async Task<MemberStagingChangeLogResource> CreateSRNChangeLog(SRNUpdateResource modelForUpdate, SRN srn, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var srnModelType = srn.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (srnProp != null)
                    {
                        //Primary keys don't get updated
                        if(srnProp.Name != "MemberId" && srnProp.Name != "SRNId")
                        {
                            object srnPropValue = srnProp.GetValue(srn, null);

                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (srnPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = srnPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        var propGroup = GetPropertyGroup(srnProp);

                                        //Foreign Keys
                                        if (propGroup == "ForeignKey")
                                        {
                                            if (!string.IsNullOrEmpty(oldValue))
                                                oldValue = await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)srnPropValue, srnProp.Name);
                                            if (!string.IsNullOrEmpty(newValue))
                                                newValue = await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)updatePropValue, srnProp.Name);
                                        }
                                        else
                                        {
                                            oldValue = srnPropValue.ToString();
                                            newValue = (updatePropValue != null)? updatePropValue.ToString() : "";
                                        }

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType.IsEnum)
                                {
                                    if ((int)updatePropValue != (int)srnPropValue)
                                    {
                                        var newValue = Helpers.Helpers.GetEnumValue(srnProp.Name, (int)updatePropValue);
                                        var oldValue = Helpers.Helpers.GetEnumValue(srnProp.Name, (int)srnPropValue);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }

        private async Task ApplySRNChanges(int srnId, SRNUpdateResource modelForUpdate)
        {
            var srn = await _dbContext.SRNs
                    .Include(i => i.Member)
                    .Include(i => i.Contacts)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.SRNStatusUpdates)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == srnId);

            MemberStagingChangeLogResource stagingChangeLog = new MemberStagingChangeLogResource();

            await UpdateSRNContacts(srn, modelForUpdate, stagingChangeLog);
            await UpdateSRNBranchLocations(srn, modelForUpdate, stagingChangeLog);

            modelForUpdate.LoanManagementSystemVendorId = (modelForUpdate.LoanManagementSystemVendorId > 0) ? modelForUpdate.LoanManagementSystemVendorId : null;
            modelForUpdate.SoftwareVendorId = (modelForUpdate.SoftwareVendorId > 0) ? modelForUpdate.SoftwareVendorId : null;

            if(srn.SRNStatusUpdates.Count > 0)
            {
                SRNStatusUpdateHistory updateHistory = null;
                updateHistory = srn.SRNStatusUpdates
                    .OrderByDescending(i => i.DateCreated)
                    .FirstOrDefault(i => i.FileType == modelForUpdate.FileType);

                if(updateHistory != null)
                {
                    CreateSRNStatusUpdateHistoryEventLog(modelForUpdate, updateHistory, stagingChangeLog);
                    
                    UpdateSRNStatusHistory(updateHistory, modelForUpdate);

                    //Update camunda variables
                    if (!updateHistory.IsComple)
                    {
                        var testEndDate = "";
                        var goLiveDate = "";

                        if (updateHistory.FileType == SRNStatusFileTypes.DailyFile)
                        {
                            if (updateHistory.DailyFileTestEndDate > DateTime.Now)
                            {
                                testEndDate = updateHistory.DailyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                goLiveDate = updateHistory.DailyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                            }
                            else
                            {
                                testEndDate = (updateHistory.DailyFileTestEndDate != null) ? updateHistory.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                goLiveDate = (updateHistory.DailyFileGoLiveDate != null) ? updateHistory.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                            }
                        }
                        else if (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            if (updateHistory.MonthlyFileTestEndDate > DateTime.Now)
                            {
                                testEndDate = updateHistory.MonthlyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                goLiveDate = updateHistory.MonthlyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                            }
                            else
                            {
                                testEndDate = (updateHistory.MonthlyFileTestEndDate != null) ? updateHistory.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                goLiveDate = (updateHistory.MonthlyFileGoLiveDate != null) ? updateHistory.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                            }
                        }

                        await UpdateCamundaVariables(updateHistory.ProcessInstanceId, "testEndDate", testEndDate, "String");

                        await UpdateCamundaVariables(updateHistory.ProcessInstanceId, "goLiveDate", goLiveDate, "String");
                    }
                }
            }

            var model = _mapper.Map<SRN>(modelForUpdate);

            if (modelForUpdate.UserId > 0)
            {
                var currentUser = _userRepository.Get(modelForUpdate.UserId);
                if(currentUser != null)
                {
                    if(currentUser.RoleId == UserRoles.StakeHolderManager)
                    {
                        if (srn.SRNStatus != null)
                        {
                            if (srn.SRNStatus.Name == "Live - Missing information")
                            {
                                var newStatus = await _dbContext.SRNStatuses
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Name == "Live");

                                if (newStatus != null)
                                {
                                    model.SRNStatusId = newStatus.Id;
                                }
                            }
                            else
                            {
                                model.SRNStatusId = srn.SRNStatusId;
                            }
                        }
                        else
                        {
                            model.SRNStatusId = srn.SRNStatusId;
                        }
                    }
                    else
                    {
                        model.SRNStatusId = srn.SRNStatusId;
                    }
                }
            }
            else
            {
                model.SRNStatusId = srn.SRNStatusId;
            }

            //DO NOT update these fields
            model.SRNNumber = srn.SRNNumber;
            model.MemberId = srn.MemberId;
            model.SRNStatusReasonId = srn.SRNStatusReasonId;

            Helpers.Helpers.PrepareSRNForUpdate(_dbContext, model);
            _dbContext.Set<SRN>().Update(model);

            await _dbContext.SaveChangesAsync();

            await CreateSRNChangeLog(modelForUpdate, srn, stagingChangeLog);

            var updateDetailsBlob = JsonConvert.SerializeObject(modelForUpdate);
            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                await Helpers.Helpers
                    .CreateEventLog(_dbContext, modelForUpdate.UserId, "SRN Update", srn.TradingName, updateDetailsBlob, stagingDetailsBlob, srn.Id, "SRN");
            }
        }

        private string GetPropertyGroup(PropertyInfo prop)
        {
            var groupName = "";
            var attributes = prop.GetCustomAttributes(typeof(DisplayAttribute),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    groupName = attributes.Cast<DisplayAttribute>().Single().GroupName;
                }
            }

            return groupName;
        }
        private async Task<int> CreateSRNChangeRequest(SRN srn, SRNUpdateResource modelForUpdate)
        {
            int changeRequestId = 0;
            if (srn != null && modelForUpdate != null)
            {
                var srnChangeRequest = await _dbContext.Set<ChangeRequestStaging>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.ObjectId == srn.Id);

                if (srnChangeRequest != null)
                {
                    changeRequestId = srnChangeRequest.Id;

                    var changedModel = _mapper.Map(modelForUpdate, srnChangeRequest);
                    srnChangeRequest.Status = ChangeRequestStatus.NotActioned;
                    srnChangeRequest.Id = changeRequestId;
                    srnChangeRequest.DateCreated = DateTime.Now;

                    var oldDetails = _mapper.Map<SRNUpdateResource>(srn);
                    srnChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                    srnChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(modelForUpdate);
                    srnChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(await CreateStagingChangeLog(srn, modelForUpdate));

                    _dbContext.Set<ChangeRequestStaging>().Update(srnChangeRequest);
                    await _dbContext.SaveChangesAsync();
                }
                else
                {
                    var newChangeRequest = new ChangeRequestStaging
                    {
                        Type = ChangeObjectType.SRN,
                        Status = ChangeRequestStatus.NotActioned,
                        ObjectId = srn.Id,
                        DateCreated = DateTime.Now,
                        UserId = modelForUpdate.UserId
                    };

                    var oldDetails = _mapper.Map<SRNUpdateResource>(srn);
                    newChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                    newChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(modelForUpdate);
                    newChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(await CreateStagingChangeLog(srn, modelForUpdate));

                    await _dbContext.Set<ChangeRequestStaging>().AddAsync(newChangeRequest);
                    await _dbContext.SaveChangesAsync();
                    changeRequestId = newChangeRequest.Id;
                }
            }
            return changeRequestId;
        }

        private async Task StartSRNUpdateWorkflow(SRN srn, bool isApprovalRequired, int changeRequestId)
        {
            string requireApproval = isApprovalRequired ? "yes" : "no";

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "SRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srn.Id.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "requireApproval",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requireApproval },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "ChangeRequestId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", changeRequestId.ToString()},
                                            { "type", "Long" }
                                        }
                                    }
                                }
                            }
                        };

            using (var client = new HttpClient())
            {
                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Update-Details/start";
                var result = await client.PostAsync(uri, content);
                result.EnsureSuccessStatusCode();
            }
        }

        private async Task UpdateSRNContacts(SRN srn, SRNUpdateResource modelForUpdate, MemberStagingChangeLogResource stagingChangeLog)
        {
            //Add new trading names and update existing ones
            var lstExistingContacts = new List<SRNContact>();
            if (modelForUpdate != null)
            {
                if (modelForUpdate.Contacts != null)
                {
                    var newContacts = new List<SRNContactUpdateResource>();
                    foreach (var contact in modelForUpdate.Contacts)
                    {
                        if (!srn.Contacts.Any(i => i.Id == contact.Id))
                        {
                            newContacts.Add(contact);
                            contact.SRNId = srn.Id;

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact First Name",
                                OldValue = "",
                                NewValue = contact.FirstName
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact Surname",
                                OldValue = "",
                                NewValue = contact.Surname
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact Job Title",
                                OldValue = "",
                                NewValue = contact.JobTitle
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact - Contact Type",
                                OldValue = "",
                                NewValue = contact.ContactTypeId.ToString()
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact Email",
                                OldValue = "",
                                NewValue = contact.Email
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact Cell Number",
                                OldValue = "",
                                NewValue = contact.CellNumber
                            });

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Contact Office Tel Number",
                                OldValue = "",
                                NewValue = contact.OfficeTelNumber
                            });

                        }

                        else
                        {
                            var existingContact = srn.Contacts
                                .FirstOrDefault(i => i.Id == contact.Id);

                            if (existingContact.FirstName != contact.FirstName)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact First Name",
                                    OldValue = existingContact.FirstName,
                                    NewValue = contact.FirstName
                                });
                            }
                            existingContact.FirstName = contact.FirstName;

                            if (existingContact.Surname != contact.Surname)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact Surname",
                                    OldValue = existingContact.Surname,
                                    NewValue = contact.Surname
                                });
                            }

                            existingContact.Surname = contact.Surname;

                            if (existingContact.CellNumber != contact.CellNumber)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact Cell Number",
                                    OldValue = existingContact.CellNumber,
                                    NewValue = contact.CellNumber
                                });
                            }

                            existingContact.CellNumber = contact.CellNumber;

                            if (existingContact.Email != contact.Email)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact Email",
                                    OldValue = existingContact.Email,
                                    NewValue = contact.Email
                                });
                            }

                            existingContact.Email = contact.Email;

                            if (existingContact.OfficeTelNumber != contact.OfficeTelNumber)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact Office Tel Number",
                                    OldValue = existingContact.OfficeTelNumber,
                                    NewValue = contact.OfficeTelNumber
                                });
                            }

                            existingContact.OfficeTelNumber = contact.OfficeTelNumber;

                            if (existingContact.JobTitle != contact.JobTitle)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact Destination",
                                    OldValue = existingContact.JobTitle,
                                    NewValue = contact.JobTitle
                                });
                            }

                            existingContact.JobTitle = contact.JobTitle;

                            if (existingContact.ContactTypeId != contact.ContactTypeId)
                            {
                                var oldType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == existingContact.ContactTypeId);
                                var newType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == contact.ContactTypeId);

                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "Contact - Contact Type",
                                    OldValue = oldType.Name,
                                    NewValue = newType.Name
                                });
                            }

                            existingContact.ContactTypeId = contact.ContactTypeId;

                            lstExistingContacts.Add(existingContact);

                            Helpers.Helpers.PrepareSRNContactForUpdate(_dbContext, existingContact);
                            EntityEntry<SRNContact> entity = _dbContext.Set<SRNContact>().Update(existingContact);
                            await _dbContext.SaveChangesAsync();

                            entity.State = EntityState.Detached;

                        }
                    }

                    if(newContacts.Count > 0)
                    {
                        var srnContacts = _mapper.Map<List<SRNContact>>(newContacts);
                        _dbContext.SRNContacts.AddRange(srnContacts);
                        await _dbContext.SaveChangesAsync();               
                    }

                    modelForUpdate.Contacts = newContacts;
                }
            }
        }

        private async Task UpdateSRNBranchLocations(SRN srn, SRNUpdateResource modelForUpdate, MemberStagingChangeLogResource stagingChangeLog)
        {
            
            if (modelForUpdate != null)
            {
                if (modelForUpdate.BranchLocations != null)
                {
                    //Add new branch locations
                    var newBranchLocations = new List<BranchLocationUpdateResource>();
                    foreach (var branch in modelForUpdate.BranchLocations)
                    {
                        if (!srn.BranchLocations.Any(i => i.Id == branch.Id && i.Id > 0))
                        {
                            newBranchLocations.Add(branch);

                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "SRN Branch Location",
                                OldValue = "",
                                NewValue = branch.Value
                            });

                            srn.BranchLocations.Add(new BranchLocation { Name = branch.Value });
                        }

                        //update existing one
                        else
                        {
                            var existingBranch = srn.BranchLocations
                                .FirstOrDefault(i => i.Id == branch.Id);

                            if (existingBranch.Name != branch.Value)
                            {
                                stagingChangeLog.Changes.Add(new StagingChange
                                {
                                    Name = "SRN Branch Location",
                                    OldValue = existingBranch.Name,
                                    NewValue = branch.Value
                                });
                            }
                            existingBranch.Name = branch.Value;
                        }
                    }
                }
            }

            //Delete all branch locations that were removed from the update recource
            var deletedBranchLocations = new List<BranchLocation>();

            foreach (var location in srn.BranchLocations.Where(i => i.Id > 0))
            {
                var locationFound = modelForUpdate.BranchLocations
                    .FirstOrDefault(i => i.Id == location.Id);

                if (locationFound == null)
                    deletedBranchLocations.Add(location);
            }

            if (deletedBranchLocations.Count > 0)
            {
                _dbContext.BranchLocations.RemoveRange(deletedBranchLocations);
            }

            await _dbContext.SaveChangesAsync();
        }

        public async Task<List<SRNGetResource>> ListByMemberId(int memberId, bool withSrnNumberOnly = false, bool? isActivityAllowedWhileInProcess = null)
        {
            var query = await _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Where(i => i.MemberId == memberId || i.ALGLeaderId == memberId)
                    .ToListAsync();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (query != null)
            {
                if(query.Count > 0)
                {
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        if (user.RoleId == UserRoles.ALGLeader)
                        {
                            var algLeader = await _dbContext.ALGClientLeaders
                                .Include(i => i.Leader)
                                .FirstOrDefaultAsync(i => i.Leader.Users.Any(x => x.UserId == user.Id)
                                    && i.Leader.MembershipTypeId == MembershipTypes.ALGLeader);

                            if (algLeader == null)
                                query = new List<SRN>();

                            query = query.Where(x => x.ALGLeaderId == algLeader.LeaderId).ToList();
                        }
                        else
                        {
                            query = query.Where(x => x.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                        }
                    }
                }
            }

            if (withSrnNumberOnly)
                query = query.Where(i => !string.IsNullOrEmpty(i.SRNNumber)).ToList();

            if(isActivityAllowedWhileInProcess != null)
            {
                if((bool)isActivityAllowedWhileInProcess)
                    query = query.Where(i => i.SRNStatus.IsActivityAllowedWhileInProcess).ToList();
                else
                    query = query.Where(i => !i.SRNStatus.IsActivityAllowedWhileInProcess).ToList();
            }

            var itemsToReturn = _mapper.Map<List<SRNGetResource>>(query);

            foreach(var item in itemsToReturn)
            {
                DefaultValueHelper<SRNGetResource>.GetDefaultValue(item);
            }

            return itemsToReturn;
        }

        public async Task<List<SRNGetResource>> ListByMemberId(int memberId, string status = null)
        {
            var query = await _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.LoanManagementSystemVendor)
                    .Include(i => i.SoftwareVendor)
                    .Include(i => i.BranchLocations)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Where(i => i.MemberId == memberId || i.ALGLeaderId == memberId)
                    .ToListAsync();

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (query != null)
            {
                if (query.Count > 0)
                {
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        query = query.Where(x => x.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                    }
                }
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(i => i.SRNStatus.Name == status).ToList();
            }

            var itemsToReturn = _mapper.Map<List<SRNGetResource>>(query);

            foreach (var item in itemsToReturn)
            {
                DefaultValueHelper<SRNGetResource>.GetDefaultValue(item);
            }
            return itemsToReturn;
        }

        public async Task<MemberStagingChangeLogResource> GetStagingChangeRequest(int srnId)
        {
            var changeRequest = await _dbContext.MemberChangeRequests
                    .FirstOrDefaultAsync(i => i.ObjectId == srnId && i.Type == ChangeObjectType.SRN);

            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (changeRequest != null)
            {
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (changeRequest.UserId != user.Id)
                        throw new UnauthorizedException();
                }
            }

            if (changeRequest != null)
            {
                var stagingDetailsBlob = JsonConvert.DeserializeObject<MemberStagingChangeLogResource>(changeRequest.StagingDetailsBlob);

                return stagingDetailsBlob;
            }

            return null;
        }

        public async Task RequestSRNMerge(SRNMergeRequestResource request)
        {
            if (request != null)
            {
                await _srnExtensions.RequestSRNMerge(request);
            }
        }
        public async Task RequestSRNSplit(SRNSplitRequestResource request)
        {
            if (request != null)
            {
                await _srnExtensions.RequestSRNSplit(request);
            }
        }
        public async Task RequestSRNSale(SRNSaleRequestResource request)
        {
            if (request != null)
            {
                await _srnExtensions.RequestSRNSale(request);
            }
        }

        private async Task<MemberStagingChangeLogResource> CreateStagingChangeLog(SRN oldModel, SRNUpdateResource updatedModel)
        {
            if (oldModel != null && updatedModel != null)
            {
                var updateModelType = updatedModel.GetType();
                var oldModelType = oldModel.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> oldProperties = new List<PropertyInfo>(oldModelType.GetProperties());
                MemberStagingChangeLogResource memberStagingChangeLog = new MemberStagingChangeLogResource();
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    if (updateProp.DeclaringType == typeof(SRNDatesShared))
                    {
                        object updatePropValue = updateProp.GetValue(updatedModel, null);
                        var oldStatusUpdateProperty = oldProperties.FirstOrDefault(i => i.PropertyType == typeof(ICollection<SRNStatusUpdateHistory>));
                        var statusUpdateType = new SRNStatusUpdateHistory().GetType();
                        var oldStatusUpdateProperties = statusUpdateType.GetProperties();

                        var oldProp = oldStatusUpdateProperties.FirstOrDefault(i => i.Name == updateProp.Name);

                        if (oldProp != null)
                        {
                            SRNStatusUpdateHistory recentSRNStatusUpdate = null;

                            //If there are no previous updates in the SRN Status Update History
                            if (oldModel.SRNStatusUpdates.Count > 0)
                            {
                                recentSRNStatusUpdate = oldModel.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault();
                            }

                            if (recentSRNStatusUpdate != null)
                            {
                                object oldPropValue = oldProp.GetValue(recentSRNStatusUpdate, null);

                                if (oldPropValue != null)
                                {
                                    var propType = oldPropValue.GetType();

                                    if (propType == typeof(DateTime) || propType == typeof(DateTime?))
                                    {
                                        var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                        var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                        if (updateValue != oldDbValue)
                                        {
                                            var stagingChange = new StagingChange
                                            {
                                                Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                                OldValue = oldDbValue,
                                                NewValue = updateValue
                                            };

                                            stagingChangeList.Add(stagingChange);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if(updatePropValue != null)
                                {
                                    var stagingChange = new StagingChange
                                    {
                                        Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                        OldValue = "",
                                        NewValue = updatePropValue.ToString()
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                    else
                    {
                        object updatePropValue = updateProp.GetValue(updatedModel, null);

                        var oldProp = oldProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                        if (oldProp != null)
                        {
                            object oldPropValue = oldProp.GetValue(oldModel, null);

                            if (oldPropValue != null)
                            {
                                var propType = oldPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                    var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                    if (updateValue != oldDbValue)
                                    {
                                        string oldValue = "";
                                        string newValue = "";

                                        if (Helpers.Helpers.IsPropertyForeignKey(oldProp))
                                        {
                                            oldValue = (oldPropValue != null) ? await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)oldPropValue, oldProp.Name) : "";
                                            newValue = (updatePropValue != null) ? await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name) : "";
                                        }

                                        else
                                        {
                                            oldValue = oldPropValue.ToString();
                                            newValue = updatePropValue.ToString();
                                        }

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType.IsEnum)
                                {
                                    if ((int)updatePropValue != (int)oldPropValue)
                                    {
                                        var newValue = Helpers.Helpers.GetPropertyDisplayName(updateProp);
                                        var oldValue = Helpers.Helpers.GetPropertyDisplayName(oldProp);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes = stagingChangeList;
                return memberStagingChangeLog;
            }

            return null;
        }

        private async Task CreateEventLog(SRNCreateResource modelForCreate, SRN srn, MemberStagingChangeLogResource stagingChangeLog)
        {
            CreateContactsEventLog(modelForCreate.Contacts, stagingChangeLog);
            CreateBranchLocationsEventLog(modelForCreate.BranchLocations, stagingChangeLog);

            var modelForUpdate = _mapper.Map<SRNUpdateResource>(modelForCreate);

            await CreateSRNEventLog(modelForUpdate, srn, stagingChangeLog);
        }
        private void CreateContactsEventLog(List<SRNContactCreateResource> contacts, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var contact in contacts)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact First Name",
                        OldValue = "",
                        NewValue = contact.FirstName
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Surname",
                        OldValue = "",
                        NewValue = contact.Surname
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Cell Number",
                        OldValue = "",
                        NewValue = contact.CellNumber
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Email",
                        OldValue = "",
                        NewValue = contact.Email
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Office Tel Number",
                        OldValue = "",
                        NewValue = contact.OfficeTelNumber
                    });

                var newType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == contact.ContactTypeId);

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Contact Type",
                        OldValue = "",
                        NewValue = newType.Name
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Contact Job Title",
                        OldValue = "",
                        NewValue = contact.JobTitle
                    });
            }
        }

        private void CreateBranchLocationsEventLog(List<BranchLocationSRNCreateResource> branchLocations, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var location in branchLocations)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "SRN Branch Location",
                        OldValue = "",
                        NewValue = location.Value
                    });
            }
        }

        private async Task<MemberStagingChangeLogResource> CreateSRNEventLog(SRNUpdateResource modelForUpdate, SRN srn, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var srnModelType = srn.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (srnProp != null)
                    {
                        object srnPropValue = srnProp.GetValue(srn, null);

                        if (srnPropValue != null)
                        {
                            var propType = srnPropValue.GetType();
                            if (propType.IsPrimitive || propType == (typeof(System.String)))
                            {
                                string oldValue = "";
                                string newValue = "";

                                if (Helpers.Helpers.IsPropertyForeignKey(srnProp))
                                {
                                    if(updatePropValue != null)
                                    {
                                        newValue = await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name);
                                    }
                                }
                                else
                                {
                                    newValue = updatePropValue.ToString();
                                }

                                var stagingChange = new StagingChange
                                {
                                    Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                            else if (propType.IsEnum)
                            {
                                var newValue = Helpers.Helpers.GetEnumValue(srnProp.Name, (int)updatePropValue);
                                var oldValue = "";

                                var stagingChange = new StagingChange
                                {
                                    Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }

        public async Task<bool> DoesTradingNameExist(string tradingName)
        {
            var tradingNameExists = false;

            if (!string.IsNullOrEmpty(tradingName))
            {
                tradingName = tradingName.Trim();

                //Search for this trading name from the list of SRNs
                var srn = await _dbContext.SRNs
                    .FirstOrDefaultAsync(i => i.TradingName == tradingName || i.TradingName == tradingName);

                //If trading name is found, set value to 'true'
                if (srn != null)
                    tradingNameExists = true;
                else
                {
                    tradingNameExists = false;
                    return tradingNameExists;
                }

                var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                // Check if the trading name belongs to the current ALG Leader
                if (user != null)
                {
                    var algLeader = await _dbContext.Members
                        .Include(i => i.Users)
                        .Select(i => new { i.Id, i.Users})
                        .FirstOrDefaultAsync(x => x.Users.Any(i => i.UserId == user.Id));

                    if (algLeader != null && srn != null)
                    {
                        if (srn.ALGLeaderId == algLeader.Id)
                            tradingNameExists = false;
                        else
                            tradingNameExists = true;
                    }
                }
            }

            return tradingNameExists;
        }

        public bool DoesSimilarTradingNameExist(string tradingName)
        {
            var tradingNameExists = false;

            if (!string.IsNullOrEmpty(tradingName))
            {
                tradingName = tradingName.Trim();

                var similarTradingNames = _dbContext.SRNs
                    .Where(x => x.TradingName.ToUpper().Replace("LTD", "").Replace("LIMITED", "").Replace(" ", "") == tradingName.ToUpper().Replace("LTD", "").Replace("LIMITED", "").Replace(" ", ""));

                if (similarTradingNames != null)
                {
                    if (similarTradingNames.Count() > 0)
                        tradingNameExists = true;
                }
                else
                {
                    tradingNameExists = false;
                    return tradingNameExists;
                }
            }

            return tradingNameExists;
        }

        public bool IsPossibleDuplicateTradingName(int srnId, string tradingName)
        {
            var tradingNameExists = false;

            if (!string.IsNullOrEmpty(tradingName))
            {
                tradingName = tradingName.Trim();

                //Search for this trading name from the list of SRNs
                var srns = _dbContext.SRNs
                    .Where(i => i.TradingName == tradingName)
                    .Select(x => new SRN { TradingName = x.TradingName })
                    .AsQueryable();

                if(!srns.Any())
                {
                    return false;
                }
                else
                {
                    //Search for this trading name from other SRNs
                    var otherMemberSRNs = _dbContext.SRNs
                        .Where(x => x.Id != srnId)
                        .Select(x => new SRN { TradingName = x.TradingName })
                        .AsEnumerable();

                    if (otherMemberSRNs.Any())
                        tradingNameExists = otherMemberSRNs.Any(i => i.TradingName == tradingName);
                }
            }

            return tradingNameExists;
        }

        public async Task RequestSRNStatusUpdate(List<SRNStatusUpdateRequestResource> requests)
        {
            if (requests != null)
            {
                await _srnExtensions.RequestSRNStatusUpdate(requests);
            }
        }

        private void PopulateSRNStatusUpdateHistory(SRNGetResource srnGetResource, ICollection<SRNStatusUpdateHistory> srnStatusUpdates, SRNStatusFileTypes? fileType = null)
        {
            if(srnGetResource != null && srnStatusUpdates != null)
            {
                if(srnStatusUpdates.Count > 0)
                {
                    SRNStatusUpdateHistory recentStatusUpdate = null;

                    if (fileType == null)
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).First();
                    }
                    else
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault(i => i.FileType == fileType);
                    }

                    if(recentStatusUpdate != null)
                    {
                        srnGetResource.DailyFileDevelopmentStartDate = recentStatusUpdate.DailyFileDevelopmentStartDate;
                        srnGetResource.DailyFileDevelopmentEndDate = recentStatusUpdate.DailyFileDevelopmentEndDate;
                        srnGetResource.DailyFileTestStartDate = recentStatusUpdate.DailyFileTestStartDate;
                        srnGetResource.DailyFileTestEndDate = recentStatusUpdate.DailyFileTestEndDate;
                        srnGetResource.DailyFileGoLiveDate = recentStatusUpdate.DailyFileGoLiveDate;

                        srnGetResource.MonthlyFileDevelopmentStartDate = recentStatusUpdate.MonthlyFileDevelopmentStartDate;
                        srnGetResource.MonthlyFileDevelopmentEndDate = recentStatusUpdate.MonthlyFileDevelopmentEndDate;
                        srnGetResource.MonthlyFileTestStartDate = recentStatusUpdate.MonthlyFileTestStartDate;
                        srnGetResource.MonthlyFileTestEndDate = recentStatusUpdate.MonthlyFileTestEndDate;
                        srnGetResource.MonthlyFileGoLiveDate = recentStatusUpdate.MonthlyFileGoLiveDate;
                        srnGetResource.Comments = recentStatusUpdate.Comments;
                        srnGetResource.IsLiveFileSubmissionsSuspended = recentStatusUpdate.IsLiveFileSubmissionsSuspended;
                        srnGetResource.IsDailyFileLive = recentStatusUpdate.IsDailyFileLive;
                        srnGetResource.IsMonthlyFileLive = recentStatusUpdate.IsMonthlyFileLive;
                        srnGetResource.FileType = (srnGetResource.FileType != null) ? _lookupsRepo.GetEnumIdValuePair<SRNStatusFileTypes>((int)recentStatusUpdate.FileType) : new IdValuePairResource { Id = 0, Value = "N/A" };
                        srnGetResource.UpdateType = (recentStatusUpdate.UpdateType != null) ? _lookupsRepo.GetEnumIdValuePair<SRNStatusTypes>((int)recentStatusUpdate.UpdateType) : new IdValuePairResource { Id = 0, Value = "N/A" };
                    }
                }
            }
        }

        private void PopulateSRNStatusUpdateHistoryV2(SRNGetV2Resource srnGetResource, ICollection<SRNStatusUpdateHistory> srnStatusUpdates, SRNStatusFileTypes? fileType = null)
        {
            if (srnGetResource != null && srnStatusUpdates != null)
            {
                if (srnStatusUpdates.Count > 0)
                {
                    SRNStatusUpdateHistory recentStatusUpdate = null;

                    if (fileType == null)
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).First();
                    }
                    else
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault(i => i.FileType == fileType);
                    }

                    if (recentStatusUpdate != null)
                    {
                        srnGetResource.DailyFileDevelopmentStartDate = recentStatusUpdate.DailyFileDevelopmentStartDate;
                        srnGetResource.DailyFileDevelopmentEndDate = recentStatusUpdate.DailyFileDevelopmentEndDate;
                        srnGetResource.DailyFileTestStartDate = recentStatusUpdate.DailyFileTestStartDate;
                        srnGetResource.DailyFileTestEndDate = recentStatusUpdate.DailyFileTestEndDate;
                        srnGetResource.DailyFileGoLiveDate = recentStatusUpdate.DailyFileGoLiveDate;

                        srnGetResource.MonthlyFileDevelopmentStartDate = recentStatusUpdate.MonthlyFileDevelopmentStartDate;
                        srnGetResource.MonthlyFileDevelopmentEndDate = recentStatusUpdate.MonthlyFileDevelopmentEndDate;
                        srnGetResource.MonthlyFileTestStartDate = recentStatusUpdate.MonthlyFileTestStartDate;
                        srnGetResource.MonthlyFileTestEndDate = recentStatusUpdate.MonthlyFileTestEndDate;
                        srnGetResource.MonthlyFileGoLiveDate = recentStatusUpdate.MonthlyFileGoLiveDate;
                        srnGetResource.Comments = recentStatusUpdate.Comments;
                        srnGetResource.IsLiveFileSubmissionsSuspended = recentStatusUpdate.IsLiveFileSubmissionsSuspended;
                        srnGetResource.IsDailyFileLive = recentStatusUpdate.IsDailyFileLive;
                        srnGetResource.IsMonthlyFileLive = recentStatusUpdate.IsMonthlyFileLive;
                        srnGetResource.FileType = (srnGetResource.FileType != null) ? _lookupsRepo.GetEnumIdValuePair<SRNStatusFileTypes>((int)recentStatusUpdate.FileType) : new IdValuePairResource { Id = 0, Value = "N/A" };
                        srnGetResource.UpdateType = (recentStatusUpdate.UpdateType != null) ? _lookupsRepo.GetEnumIdValuePair<SRNStatusTypes>((int)recentStatusUpdate.UpdateType) : new IdValuePairResource { Id = 0, Value = "N/A" };
                    }
                }
            }
        }

        private void CreateSRNStatusUpdateHistoryEventLog(SRNUpdateResource modelForUpdate, SRNStatusUpdateHistory updateHistory, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var srnModelType = updateHistory.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (srnProp != null)
                    {
                        //Primary keys don't get updated
                        if (srnProp.Name != "MemberId" && srnProp.Name != "SRNId")
                        {
                            object srnPropValue = srnProp.GetValue(updateHistory, null);

                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (srnProp.DeclaringType == typeof(SRNDatesShared))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (srnPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = srnPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                
                                        oldValue = srnPropValue.ToString();
                                        newValue = updatePropValue.ToString();

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
            }
        }

        private void UpdateSRNStatusHistory(SRNStatusUpdateHistory updateHistory, SRNUpdateResource modelForUpdate)
        {
            if(updateHistory != null && modelForUpdate != null)
            {
                updateHistory.DailyFileDevelopmentStartDate = modelForUpdate.DailyFileDevelopmentStartDate;
                updateHistory.DailyFileDevelopmentEndDate = modelForUpdate.DailyFileDevelopmentEndDate;
                updateHistory.DailyFileTestStartDate = modelForUpdate.DailyFileTestStartDate;
                updateHistory.DailyFileTestEndDate = modelForUpdate.DailyFileTestEndDate;
                updateHistory.DailyFileGoLiveDate = modelForUpdate.DailyFileGoLiveDate;

                updateHistory.MonthlyFileDevelopmentStartDate = modelForUpdate.MonthlyFileDevelopmentStartDate;
                updateHistory.MonthlyFileDevelopmentEndDate = modelForUpdate.MonthlyFileDevelopmentEndDate;
                updateHistory.MonthlyFileTestStartDate = modelForUpdate.MonthlyFileTestStartDate;
                updateHistory.MonthlyFileTestEndDate = modelForUpdate.MonthlyFileTestEndDate;
                updateHistory.MonthlyFileGoLiveDate = modelForUpdate.MonthlyFileGoLiveDate;

                _dbContext.Update(updateHistory);
            }
        }

        private async Task UpdateCamundaVariables(string taskId, string name, string value, string type)
        {
            using (var client = new HttpClient())
            {
                var tasks = await GetTasksByProcessInstanceId(taskId);

                if(tasks != null)
                {
                    if(tasks.Count > 0)
                    {
                        var variableValue = new { value = value, type = type };

                        var json = JsonConvert.SerializeObject(variableValue);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + tasks[0].Id + "/variables/" + name;
                        var result = await client.PutAsync(uri, content);
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        private async Task<List<TaskGetResource>> GetTasksByProcessInstanceId(string processInstanceId)
        {
            using (var client = new HttpClient())
            {
                var uri = _configSettings.CamundaBaseAddress + "/task?processInstanceId=" + processInstanceId + "&processDefinitionKey=New-SRN-Application";
                var result = await client.GetAsync(uri);
                result.EnsureSuccessStatusCode();

                var resultString = await result.Content.ReadAsStringAsync();
                var taskResource = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                return taskResource;
            }
        }

        public async Task DeleteProcessInstance(string processInstanceId)
        {
            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-instance/" + processInstanceId;
                var result = await client.DeleteAsync(uri);
                result.EnsureSuccessStatusCode();
            }
        }
            public async Task<Dictionary<string, bool>> IsSRNUpdatable()
        {
            var fieldSettings = await _dbContext.SRNFieldUpdateSettings.ToListAsync();

            var result = fieldSettings.ToDictionary(
                x => x.FieldUpdateName,
                 x => x.IsUpdatable);

            return result;

        }
    }
}
