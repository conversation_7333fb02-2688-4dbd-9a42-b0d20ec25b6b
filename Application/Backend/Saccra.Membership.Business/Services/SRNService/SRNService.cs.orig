using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Saccra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.SRNReTestingDTOs;
using Sacrra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Net;
using Serilog.Core;
using Serilog;
using Sacrra.Membership.Database.Migrations;
using Microsoft.VisualBasic.FileIO;
using Newtonsoft.Json.Linq;
using System.Xml.Linq;
using System.IO;
using Saccra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.SRNReTestingDTOs;
using Sacrra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Repositories;
using Microsoft.AspNetCore.Mvc;
using Saccra.Membership.Business.DTOs.FullSrnDetailsDTO;

namespace Sacrra.Membership.Business.Services
{
    public class SRNService
    {
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;
        private readonly ConfigSettings _configSettings;
        private readonly SRNServiceHelper _SRNHelper;

        private readonly MemberExtensions _memberExtensions;
        private readonly NCRReportingAccountTypeClassificationsService _ncrAccountTypeClassificationsService;
        private readonly CreditInformationClassificationService _creditInfoClassificationsService;
        private readonly ContactTypeRepository _contactTypeRepository;

        protected static readonly ILogger _log = Log.ForContext<SRNService>();


        public async Task<SRNGetOutputDTO> GetSRN(int srnId, User user, string taskId, SRNStatusFileTypes? fileType = null)
        {
            var selectRecord = _dbContext.SRNs
                   .Include(i => i.Member)
                       .ThenInclude(i => i.StakeholderManager)
                   .Include(i => i.Member)
                       .ThenInclude(i => i.Users)
                   .Include(i => i.AccountType)
                   .Include(i => i.LoanManagementSystemVendor)
                   .Include(i => i.SoftwareVendor)
                   .Include(i => i.BranchLocations)
                   .Include(i => i.SRNStatus)
                   .Include(i => i.NCRReportingAccountTypeClassification)
                   .Include(i => i.Contacts)
                       .ThenInclude(i => i.ContactType)
                   .Include(i => i.SPGroup)
                   .Include(i => i.ALGLeader)
                   .Include(i => i.SRNStatusUpdates)
                   .Include(i => i.SRNStatusReason)
                   .AsNoTracking()
                   .FirstOrDefault(i => i.Id == srnId);

            if (selectRecord == null)
                return null;

            if (selectRecord.Id <= 0)
                return null;

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (!selectRecord.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            var changeStatus = _dbContext.MemberChangeRequests
                .FirstOrDefault(m => m.Type == ChangeObjectType.SRN && m.ObjectId == srnId);

            var returnRecord = _mapper.Map<SRNGetOutputDTO>(selectRecord);

            returnRecord.ChangeRequestStatus = (changeStatus != null) ? changeStatus.Status.ToString() : "No Change Request";
            _SRNHelper.PopulateSRNStatusUpdateHistory(returnRecord, fileType);
            _globalHelper.GetDefaultValue(returnRecord);

            if (taskId != null)
            {
                using var httpClient = new HttpClient();
                var webRequest = new HttpRequestMessage(HttpMethod.Get, _configSettings.CamundaBaseAddress + "/task/" + taskId + "/variables");
                var httpResult = await httpClient.SendAsync(webRequest);
                var taskVariables = (JObject)JsonConvert.DeserializeObject<dynamic>(httpResult.Content.ReadAsStringAsync().Result);

<<<<<<< HEAD
                // Daily file dates
                returnRecord.DailyFileDevelopmentStartDate = GetTaskVariableAsFormattedDate(taskVariables, "DailyFileDevelopmentStartDate", "dailyFileDevelopmentStartDate");
                returnRecord.DailyFileDevelopmentEndDate = GetTaskVariableAsFormattedDate(taskVariables, "DailyFileDevelopmentEndDate", "dailyFileDevelopmentEndDate");
                returnRecord.DailyFileTestStartDate = GetTaskVariableAsFormattedDate(taskVariables, "DailyFileTestStartDate", "dailyFileTestStartDate");
                returnRecord.DailyFileTestEndDate = GetTaskVariableAsFormattedDate(taskVariables, "DailyFileTestEndDate", "dailyFileTestEndDate");
                returnRecord.DailyFileGoLiveDate = GetTaskVariableAsFormattedDate(taskVariables, "DailyFileGoLiveDate", "dailyFileGoLiveDate");

                // Monthly file dates
                returnRecord.MonthlyFileDevelopmentStartDate = GetTaskVariableAsFormattedDate(taskVariables, "MonthlyFileDevelopmentStartDate", "monthlyFileDevelopmentStartDate");
                returnRecord.MonthlyFileDevelopmentEndDate = GetTaskVariableAsFormattedDate(taskVariables, "MonthlyFileDevelopmentEndDate", "monthlyFileDevelopmentEndDate");
                returnRecord.MonthlyFileTestStartDate = GetTaskVariableAsFormattedDate(taskVariables, "MonthlyFileTestStartDate", "monthlyFileTestStartDate");
                returnRecord.MonthlyFileTestEndDate = GetTaskVariableAsFormattedDate(taskVariables, "MonthlyFileTestEndDate", "monthlyFileTestEndDate");
                returnRecord.MonthlyFileGoLiveDate = GetTaskVariableAsFormattedDate(taskVariables, "MonthlyFileGoLiveDate", "monthlyFileGoLiveDate");
            }
=======
                returnRecord.DailyFileDevelopmentStartDate = taskVariables["DailyFileDevelopmentStartDate"]?["value"].ToString();
                returnRecord.DailyFileDevelopmentEndDate = taskVariables["DailyFileDevelopmentEndDate"]?["value"].ToString();
                returnRecord.DailyFileTestStartDate = taskVariables["DailyFileTestStartDate"]?["value"].ToString();
                returnRecord.DailyFileTestEndDate = taskVariables["DailyFileTestEndDate"]?["value"].ToString();
                returnRecord.DailyFileGoLiveDate = taskVariables["DailyFileGoLiveDate"]?["value"].ToString();
                returnRecord.MonthlyFileDevelopmentStartDate = taskVariables["MonthlyFileDevelopmentStartDate"]?["value"].ToString();
                returnRecord.MonthlyFileDevelopmentEndDate = taskVariables["MonthlyFileDevelopmentEndDate"]?["value"].ToString();
                returnRecord.MonthlyFileTestStartDate = taskVariables["MonthlyFileTestStartDate"]?["value"].ToString();
                returnRecord.MonthlyFileTestEndDate = taskVariables["MonthlyFileTestEndDate"]?["value"].ToString();
                returnRecord.MonthlyFileGoLiveDate = taskVariables["MonthlyFileGoLiveDate"]?["value"].ToString();
            }

>>>>>>> origin/SA-4848
            return returnRecord;
        }


        public async Task<FullSRNDetailsDTO> GetFullSRNDetails(int memberId)
        {
            var member = await _memberExtensions.GetMember(_dbContext, memberId);

            var accountTypes = await _dbContext.AccountTypes.ToListAsync();
            var ncrReportingClassifications =  _ncrAccountTypeClassificationsService.GetNCRReportingAccountTypeClassifications();
            var loanVendors = await _dbContext.LoanManagementSystemVendors.ToListAsync();
            var softwareVendors = await _dbContext.SoftwareVendors.ToListAsync();
            var creditInfoClassifications =  _creditInfoClassificationsService.GetAll();


            var contactTypesByMembershipType =  _contactTypeRepository.List(
                "SRNRegistration",
                member.MembershipTypeId 
            );

            var result = new FullSRNDetailsDTO
            {
                Member = member,
                AccountTypes = accountTypes,
                NCRReportingAccountTypeClassifications = ncrReportingClassifications,
                LoanManagementSystemVendors = loanVendors,
                SoftwareVendors = softwareVendors,
                CreditInformationClassifications = creditInfoClassifications,
                ContactTypesByMembershipType = contactTypesByMembershipType,
                
            };

            return result;
        }

        public SRNGetOutputDTO GetSRNForSRNUpdateTask(int srnId, SRNStatusFileTypes? fileType = null)
        {
            var changeRequest = _dbContext.MemberChangeRequests
                .FirstOrDefault(m => m.Type == ChangeObjectType.SRN && m.ObjectId == srnId);

            if (changeRequest != null)
            {
                var modelForUpdate = JsonConvert.DeserializeObject<SRNUpdateInputDTO>(changeRequest.UpdatedDetailsBlob);

                if (modelForUpdate == null)
                    return null;

                if (modelForUpdate.Id <= 0)
                    return null;

                var data = _mapper.Map<SRNGetOutputDTO>(modelForUpdate);

                ICollection<SRNStatusUpdateHistory> srnStatusUpdates = _dbContext.SRNStatusUpdateHistory
                    .Where(i => i.SRNId == srnId)
                    .ToList();

                _SRNHelper.PopulateSRNStatusUpdateHistory(data);
                _globalHelper.GetDefaultValue(data);

                return data;
            }

            return null;

        }

        public async Task UpdateSRNDetails(SRNDetailsDTO srnDetailsDTO, User user)
        {
            if (srnDetailsDTO.Id <= 0)
                throw new ArgumentException("Invalid SRN ID provided.", nameof(srnDetailsDTO.Id));

            var existingSRN = await _dbContext.SRNs
                .Include(x => x.BranchLocations)
                .FirstOrDefaultAsync(x => x.Id == srnDetailsDTO.Id);

            if (existingSRN == null)
                throw new Exception($"SRN with ID {srnDetailsDTO.Id} not found.");

            var pendingSRNChangeRequest = await _dbContext.Set<ChangeRequestStaging>()
                .FirstOrDefaultAsync(i => i.ObjectId == srnDetailsDTO.Id
                    && i.Status == ChangeRequestStatus.NotActioned);

            if (pendingSRNChangeRequest != null && !_globalHelper.IsInternalSACRRAUser(user))
            {
                throw new SRNUpdateNotAllowedException(0, "SRN changes not allowed for this user.", null);
            }

            await _SRNHelper.ApplySRNDetailsChanges(existingSRN.Id, srnDetailsDTO, user);
        }

        public async Task<SRNUpdateMessageOutputDTO> UpdateSRN(SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {

            var pendingSRNChangeRequest = await _dbContext.Set<ChangeRequestStaging>()
                            .FirstOrDefaultAsync(i => i.ObjectId == srnUpdateInputDTO.Id
                                && i.Status == ChangeRequestStatus.NotActioned);

            if (pendingSRNChangeRequest != null && !_globalHelper.IsInternalSACRRAUser(user))
            {
                throw new SRNUpdateNotAllowedException(0, "SRN Changes not allowed for this user.", null);
            }

            var isApprovalRequired = false;

            var existingSRN = _dbContext.SRNs
                .Include(x => x.Member)
                    .ThenInclude(x => x.Users)
                .Include(i => i.SRNStatusUpdates)
                .Include(i => i.Contacts)
                .Include(i => i.SRNStatus)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == srnUpdateInputDTO.Id);

            if (existingSRN == null)
            {
                throw new Exception($"SRN with ID {srnUpdateInputDTO.Id} not found.");
            }

            if (existingSRN.SRNStatus == null)
            {
                throw new Exception($"SRNStatus not found for SRN with ID {srnUpdateInputDTO.Id}.");
            }

            if (!existingSRN.SRNStatus.IsActivityAllowedWhileInProcess)
            {
                throw new SRNUpdateNotAllowedException(0, "SRN already has a process that is in progress.", null);
            }

            //Do not update these fields
            srnUpdateInputDTO.ALGLeaderId = (srnUpdateInputDTO.ALGLeaderId > 0) ? srnUpdateInputDTO.ALGLeaderId : existingSRN.ALGLeaderId;

            if (srnUpdateInputDTO.IsDailyFile && !srnUpdateInputDTO.IsMonthlyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.DailyFile;
            }

            if (srnUpdateInputDTO.IsMonthlyFile && !srnUpdateInputDTO.IsDailyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.MonthlyFile;
            }


            if (srnUpdateInputDTO.IsDailyFile && srnUpdateInputDTO.IsMonthlyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
            }


            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (existingSRN.Member?.Users == null || !existingSRN.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();

                var changeRequestId = 0;


                //If the SRN has no SRN dates that were captured before, Trigger an approval request
                if (existingSRN.SRNStatusUpdates.Count == 0
                    && (srnUpdateInputDTO.FileType == SRNStatusFileTypes.DailyFile
                    || srnUpdateInputDTO.FileType == SRNStatusFileTypes.MonthlyFile
                    || srnUpdateInputDTO.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                    && (srnUpdateInputDTO.DailyFileTestStartDate != null || srnUpdateInputDTO.MonthlyFileTestStartDate != null))
                {
                    isApprovalRequired = true;
                    changeRequestId = _SRNHelper.CreateSRNChangeRequest(existingSRN, srnUpdateInputDTO, user);
                }

                else if (_SRNHelper.DoSRNChangesRequireApproval(existingSRN, srnUpdateInputDTO))
                {
                    isApprovalRequired = true;
                    changeRequestId = _SRNHelper.CreateSRNChangeRequest(existingSRN, srnUpdateInputDTO, user);
                }
                else
                {
                    await _SRNHelper.ApplySRNChanges(existingSRN.Id, srnUpdateInputDTO, user);
                }

                await _SRNHelper.StartSRNUpdateWorkflow(existingSRN, changeRequestId);
            }
            else if (_globalHelper.IsInternalSACRRAUser(user))
            {
                await _SRNHelper.ApplySRNChanges(existingSRN.Id, srnUpdateInputDTO, user);
            }

            return new SRNUpdateMessageOutputDTO { IsApprovalRequired = isApprovalRequired };
        }

        public async Task<SRNUpdateMessageOutputDTO> UpdateSRNContact(SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {
            var pendingSRNChangeRequest = await _dbContext.Set<ChangeRequestStaging>()
                    .FirstOrDefaultAsync(i => i.ObjectId == srnUpdateInputDTO.Id
                        && i.Status == ChangeRequestStatus.NotActioned);

            if (pendingSRNChangeRequest != null && !_globalHelper.IsInternalSACRRAUser(user))
            {
                throw new SRNUpdateNotAllowedException(0, "SRN Contact changes not allowed for this user.", null);
            }

            var isApprovalRequired = false;

            var existingSRN = _dbContext.SRNs
                .Include(x => x.Member)
                    .ThenInclude(x => x.Users)
                .Include(i => i.Contacts)
                .Include(i => i.SRNStatus)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == srnUpdateInputDTO.Id);

            if (existingSRN == null)
            {
                throw new Exception($"SRN with ID {srnUpdateInputDTO.Id} not found.");
            }

            if (existingSRN.SRNStatus == null)
            {
                throw new Exception($"SRNStatus not found for SRN with ID {srnUpdateInputDTO.Id}.");
            }

            if (!existingSRN.SRNStatus.IsActivityAllowedWhileInProcess)
            {
                throw new SRNUpdateNotAllowedException(0, "SRN already has a process that is in progress.", null);
            }

            if (srnUpdateInputDTO.Contacts.Count == 0)
            {
                throw new Exception("No contacts provided.");
            }

            var missingContacts = new List<string>();

            if (srnUpdateInputDTO.Contacts.FirstOrDefault(x => x.ContactTypeId == 1) == null) // Main Contact
            {
                missingContacts.Add("Main Contact");
            }

            if (srnUpdateInputDTO.Contacts.FirstOrDefault(x => x.ContactTypeId == 2) == null) // Alternate Contact
            {
                missingContacts.Add("Alternate Contact");
            }

            if (srnUpdateInputDTO.Contacts.FirstOrDefault(x => x.ContactTypeId == 3) == null) // Financial Contact
            {
                missingContacts.Add("Financial Contact");
            }

            if (missingContacts.Any())
            {
                throw new Exception($"{string.Join(", ", missingContacts)} {(missingContacts.Count == 1 ? "is" : "are")} required.");
            }

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (existingSRN.Member?.Users == null || !existingSRN.Member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();

                var changeRequestId = 0;

                // Check if contact changes require approval
                if (_SRNHelper.DoSRNContactChangesRequireApproval(existingSRN, srnUpdateInputDTO))
                {
                    isApprovalRequired = true;
                    changeRequestId = _SRNHelper.CreateSRNContactChangeRequest(existingSRN, srnUpdateInputDTO, user);
                }
                else
                {
                    await _SRNHelper.ApplySRNContactChanges(existingSRN.Id, srnUpdateInputDTO, user);
                }

                // Start SRN Update workflow for contact changes
                await _SRNHelper.StartSRNUpdateWorkflow(existingSRN, changeRequestId, isApprovalRequired);
            }
            else if (_globalHelper.IsInternalSACRRAUser(user))
            {
                await _SRNHelper.ApplySRNContactChanges(existingSRN.Id, srnUpdateInputDTO, user);
            }

            return new SRNUpdateMessageOutputDTO { IsApprovalRequired = isApprovalRequired };
        }

        #region: Update SRN Dates
        public SRNUpdateDatesOutputDTO UpdateSRNDates(SRNUpdateDatesInputDTO srnUpdateDatesInputDTO, User ActionedByUser)
        {
            try
            {
                var existingSRN = _dbContext.SRNs
                 .Include(x => x.Member)
                     .ThenInclude(x => x.Users)
                 .Include(i => i.SRNStatusUpdates)
                 .Include(i => i.SRNStatus)
                 .AsNoTracking()
                 .Single(i => i.Id == srnUpdateDatesInputDTO.Id);

                if (!_globalHelper.IsInternalSACRRAUser(ActionedByUser))
                {
                    return new SRNUpdateDatesOutputDTO { Message = $"{ActionedByUser.Email} is not allowed to change the dates of this SRN", ErrorCode = 404 };
                }
                else
                {
                    var pendingSRNChangeRequest = _dbContext.Set<ChangeRequestStaging>().FirstOrDefault(x => x.ObjectId == srnUpdateDatesInputDTO.Id && x.Status == ChangeRequestStatus.NotActioned);

                    if (pendingSRNChangeRequest != null && !_globalHelper.IsInternalSACRRAUser(ActionedByUser))
                    {
                        return new SRNUpdateDatesOutputDTO { Message = $"{ActionedByUser.Email} is not allowed to change the dates of this SRN", ErrorCode = 404 };
                    }
                    else
                    {
                        if (existingSRN == null)
                        {
                            throw new Exception("SRN could not be found");
                        }
                        else if (existingSRN.SRNStatus == null)
                        {
                            throw new Exception("SRN status could not be found");
                        }
                        else if (!existingSRN.SRNStatus.IsActivityAllowedWhileInProcess)
                        {
                            throw new SRNUpdateNotAllowedException(0, "SRN already has a process that is in progress.", null);
                        }
                        else
                        {
                            if (srnUpdateDatesInputDTO.IsDailyFile && !srnUpdateDatesInputDTO.IsMonthlyFile)
                            {
                                srnUpdateDatesInputDTO.FileType = SRNStatusFileTypes.DailyFile;
                            }
                            else if (!srnUpdateDatesInputDTO.IsDailyFile && srnUpdateDatesInputDTO.IsMonthlyFile)
                            {
                                srnUpdateDatesInputDTO.FileType = SRNStatusFileTypes.MonthlyFile;
                            }
                            else if (srnUpdateDatesInputDTO.IsDailyFile && srnUpdateDatesInputDTO.IsMonthlyFile)
                            {
                                srnUpdateDatesInputDTO.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
                            }

                            var changeRequestId = 0;

                            var srnUpdateInputDTO = _mapper.Map<SRNUpdateInputDTO>(existingSRN);
                            srnUpdateInputDTO.FileType = srnUpdateDatesInputDTO.FileType;
                            srnUpdateInputDTO.IsDailyFile = srnUpdateDatesInputDTO.IsDailyFile;
                            srnUpdateInputDTO.IsMonthlyFile = srnUpdateDatesInputDTO.IsMonthlyFile;

                            if (srnUpdateDatesInputDTO.FileType == SRNStatusFileTypes.DailyFile || srnUpdateDatesInputDTO.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                            {
                                //Daily File Dates
                                srnUpdateInputDTO.DailyFileDevelopmentStartDate = srnUpdateDatesInputDTO.DailyFileDevelopmentStartDate;
                                srnUpdateInputDTO.DailyFileDevelopmentEndDate = srnUpdateDatesInputDTO.DailyFileDevelopmentEndDate;
                                srnUpdateInputDTO.DailyFileTestStartDate = srnUpdateDatesInputDTO.DailyFileTestStartDate;
                                srnUpdateInputDTO.DailyFileTestEndDate = srnUpdateDatesInputDTO.DailyFileTestEndDate;
                                srnUpdateInputDTO.DailyFileGoLiveDate = srnUpdateDatesInputDTO.DailyFileGoLiveDate;
                            }

                            if (srnUpdateDatesInputDTO.FileType == SRNStatusFileTypes.MonthlyFile || srnUpdateDatesInputDTO.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                            {
                                //Monthly File Dates
                                srnUpdateInputDTO.MonthlyFileDevelopmentStartDate = srnUpdateDatesInputDTO.MonthlyFileDevelopmentStartDate;
                                srnUpdateInputDTO.MonthlyFileDevelopmentEndDate = srnUpdateDatesInputDTO.MonthlyFileGoLiveDate;
                                srnUpdateInputDTO.MonthlyFileTestStartDate = srnUpdateDatesInputDTO.MonthlyFileTestStartDate;
                                srnUpdateInputDTO.MonthlyFileTestEndDate = srnUpdateDatesInputDTO.MonthlyFileTestEndDate;
                                srnUpdateInputDTO.MonthlyFileGoLiveDate = srnUpdateDatesInputDTO.MonthlyFileGoLiveDate;
                            }

                            if (ActionedByUser.RoleId == UserRoles.SACRRAAdministrator && existingSRN.SRNStatus.Name.Contains("Live", StringComparison.CurrentCultureIgnoreCase))
                            {
                                _SRNHelper.ApplySRNChanges(existingSRN.Id, srnUpdateInputDTO, ActionedByUser).Wait();
                            }
                            else if (_globalHelper.IsInternalSACRRAUser(ActionedByUser) && existingSRN.SRNStatus.Name.Contains("Test", StringComparison.InvariantCultureIgnoreCase))
                            {
                                _SRNHelper.ApplySRNChanges(existingSRN.Id, srnUpdateInputDTO, ActionedByUser).Wait();
                                _SRNHelper.StartSRNUpdateWorkflow(existingSRN, changeRequestId).Wait();
                            }
                            else
                            {
                                return new SRNUpdateDatesOutputDTO { Message = $"This SRN can not have changes be made at this stage: {existingSRN.SRNStatus.Name}" };
                            }
                        }

                        return new SRNUpdateDatesOutputDTO { Message = "Updates to the SRN file dates has been made" };
                    }

                }
            }
            catch (Exception ex)
            {
                return new SRNUpdateDatesOutputDTO { Message = $"Error updating SRN dates: {ex.Message}" };
            }

        }
        #endregion

        public List<IdValuePairResource> GetSPNumbers(int memberId, User user)
        {
            var srnStatus = _globalHelper.GetByName("Rejected");

            var member = _dbContext.Members
                .Include(i => i.Users)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == memberId);

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (!member.Users.Any(i => i.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            var allSPGroups = new List<SPGroup>();

            if (member != null)
            {
                if (member.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    //Display SP numbers for all ALG leaders of the client
                    if (_globalHelper.IsInternalSACRRAUser(user))
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .AsNoTracking()
                            .Where(i => i.ClientId == memberId)
                            .Select(b => new { b.LeaderId })
                            .ToList();

                        foreach (var leader in algLeaders)
                        {
                            var sp = _dbContext.Set<SPGroup>()
                                .Include(i => i.SRNs)
                                .Where(i => i.MemberId == leader.LeaderId)
                                .ToList();

                            allSPGroups.AddRange(sp);
                        }
                    }
                    //Display SP numbers of the logged in user (ALG Leader)
                    else
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(x => x.Users)
                            .AsNoTracking()
                            .Where(i => i.ClientId == memberId)
                            .ToList();

                        if (algLeaders != null)
                        {
                            if (algLeaders.Count > 0)
                            {
                                var currentLeader = algLeaders
                                    .FirstOrDefault(i => i.Leader.Users.Any(x => x.UserId == user.Id));

                                if (currentLeader != null)
                                {
                                    allSPGroups = _dbContext.Set<SPGroup>()
                                        .Include(i => i.SRNs)
                                        .AsNoTracking()
                                        .Where(i => i.MemberId == currentLeader.LeaderId)
                                        .ToList();
                                }
                            }
                        }
                    }
                }
                else
                {
                    allSPGroups = _dbContext.Set<SPGroup>()
                        .Include(i => i.SRNs)
                        .AsNoTracking()
                        .Where(i => i.MemberId == memberId)
                        .ToList();
                }
            }

            var activeSPs = new List<SPGroup>();

            foreach (var sp in allSPGroups)
            {
                if (sp.SRNs.Count > 0)
                {
                    var numberOfActiveSRNs = 0;
                    foreach (var srn in sp.SRNs)
                    {
                        if (srn.SRNStatusId != srnStatus.Id)
                            numberOfActiveSRNs++;
                    }
                    if (numberOfActiveSRNs > 0)
                        activeSPs.Add(sp);
                }
                else
                {
                    activeSPs.Add(sp);
                }
            }

            var spsResource = _mapper.Map<List<IdValuePairResource>>(activeSPs);

            return spsResource;
        }

        public List<SRNViewOutputDTO> ListSRNsByMemberId(int memberId, User user, bool withSrnNumberOnly = false, bool? isActivityAllowedWhileInProcess = null)
        {
            var query = _dbContext.SRNs
                .Include(i => i.BranchLocations)
                .Include(i => i.SRNStatusUpdates)
                .Include(i => i.Contacts)
                .Include(i => i.SRNStatus)
                .Include(i => i.CreditInformationClassification)
                .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                .Where(i => i.MemberId == memberId || i.ALGLeaderId == memberId)
                .Select(m => new SRN
                {
                    Id = m.Id,
                    SRNNumber = m.SRNNumber,
                    TradingName = m.TradingName,
                    SRNStatusId = m.SRNStatusId,
                    SRNStatusReasonId = m.SRNStatusReasonId,
                    MemberId = m.MemberId,
                    CreditInformationClassification = m.CreditInformationClassification,
                    BureauInstruction = m.BureauInstruction,
                    StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                    SoftwareVendorId = m.SoftwareVendorId,
                    SoftwareVendor = m.SoftwareVendor,
                    LastSubmissionDate = m.LastSubmissionDate,
                    ALGLeaderId = m.ALGLeaderId,
                    BranchLocations = m.BranchLocations.Select(b => new BranchLocation
                    {
                        Id = b.Id,
                        Name = b.Name
                    }).ToList(),
                    AccountTypeId = m.AccountTypeId,
                    NumberOfActiveAccounts = m.NumberOfActiveAccounts,
                    BillingCycleDay = m.BillingCycleDay,
                    NCRReportingAccountTypeClassificationId = m.NCRReportingAccountTypeClassificationId,
                    LoanManagementSystemVendorId = m.LoanManagementSystemVendorId,
                    SPGroupId = m.SPGroupId,
                    FileType = m.FileType,
                    SRNStatusUpdates = m.SRNStatusUpdates.Select(b => new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = b.DailyFileDevelopmentStartDate,
                        DailyFileDevelopmentEndDate = b.DailyFileDevelopmentEndDate,
                        DailyFileTestStartDate = b.DailyFileTestStartDate,
                        DailyFileTestEndDate = b.DailyFileTestEndDate,
                        DailyFileGoLiveDate = b.DailyFileGoLiveDate,

                        MonthlyFileDevelopmentStartDate = b.MonthlyFileDevelopmentStartDate,
                        MonthlyFileDevelopmentEndDate = b.MonthlyFileDevelopmentEndDate,
                        MonthlyFileTestStartDate = b.MonthlyFileTestStartDate,
                        MonthlyFileTestEndDate = b.MonthlyFileTestEndDate,
                        MonthlyFileGoLiveDate = b.MonthlyFileGoLiveDate,

                        Comments = b.Comments,
                        IsLiveFileSubmissionsSuspended = b.IsLiveFileSubmissionsSuspended,
                        UpdateType = b.UpdateType,
                        IsDailyFileLive = b.IsDailyFileLive,
                        IsMonthlyFileLive = b.IsMonthlyFileLive,
                        DateCreated = b.DateCreated,
                        FileType = b.FileType
                    }).ToList(),
                    Contacts = m.Contacts.Select(c => new SRNContact
                    {
                        Id = c.Id,
                        ContactTypeId = c.ContactTypeId,
                        FirstName = c.FirstName,
                        Surname = c.Surname,
                        CellNumber = c.CellNumber,
                        Email = c.Email,
                        OfficeTelNumber = c.OfficeTelNumber,
                        JobTitle = c.JobTitle
                    }).ToList(),
                    SRNStatus = new SRNStatus
                    {
                        Id = m.SRNStatus.Id,
                        Name = m.SRNStatus.Name,
                        IsActive = m.SRNStatus.IsActive,
                        IsActivityAllowedWhileInProcess = m.SRNStatus.IsActivityAllowedWhileInProcess,
                        Code = m.SRNStatus.Code
                    },
                    Member = new Member
                    {
                        Users = m.Member.Users.Select(x => new MemberUsers
                        {
                            UserId = x.UserId
                        }).ToList()
                    },
                })
                .AsQueryable();


            if (query != null)
            {
                if (!_globalHelper.IsInternalSACRRAUser(user))
                {
                    if (user.RoleId == UserRoles.ALGLeader)
                    {
                        var algLeader = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                            .FirstOrDefault(i => i.Leader.Users.Any(x => x.UserId == user.Id)
                                && i.Leader.MembershipTypeId == MembershipTypes.ALGLeader);

                        if (algLeader == null)
                            query = null;

                        if (query != null)
                            query = query.Where(x => x.ALGLeaderId == algLeader.LeaderId);
                    }
                    else
                    {
                        query = query.Where(x => x.Member.Users.Any(i => i.UserId == user.Id));
                    }
                }
            }

            if (withSrnNumberOnly)
                query = query.Where(i => !string.IsNullOrEmpty(i.SRNNumber));

            if (isActivityAllowedWhileInProcess != null)
            {
                if ((bool)isActivityAllowedWhileInProcess)
                    query = query.Where(i => i.SRNStatus.IsActivityAllowedWhileInProcess);
                else
                    query = query.Where(i => !i.SRNStatus.IsActivityAllowedWhileInProcess);
            }

            var srns = query.ToList();

            var changeStatuses = _dbContext.MemberChangeRequests
                .Where(m => m.Type == ChangeObjectType.SRN)
                .AsQueryable();

            var returnData = _mapper.Map<List<SRNViewOutputDTO>>(srns);

            foreach (var item in returnData)
            {
                var srn = query.FirstOrDefault(i => i.SRNNumber == item.SRNNumber);
                var changeRequest = changeStatuses.FirstOrDefault(i => i.ObjectId == item.Id);
                item.ChangeRequestStatus = (changeRequest != null) ? changeRequest.Status.ToString() : "No Change Request";

                _SRNHelper.PopulateSRNStatusUpdateHistory(item, srn.SRNStatusUpdates, item.FileTypeId);
            }

            return returnData;
        }

        public IMapper _mapper { get; }

        public SRNService(AppDbContext dbContext, GlobalHelper globalHelper, IMapper mapper, IOptions<ConfigSettings> configSettings, SRNServiceHelper sRNHelper,
          MemberExtensions memberExtensionsm, NCRReportingAccountTypeClassificationsService ncrAccountTypeClassificationsService, CreditInformationClassificationService creditInfoClassificationsService,
          ContactTypeRepository contactTypeRepository
            )
        {
            _dbContext = dbContext;
            _globalHelper = globalHelper;
            _mapper = mapper;
            _configSettings = configSettings.Value;
            _SRNHelper = sRNHelper;
            _memberExtensions = memberExtensionsm;
            _ncrAccountTypeClassificationsService = ncrAccountTypeClassificationsService;
            _creditInfoClassificationsService = creditInfoClassificationsService;
            _contactTypeRepository = contactTypeRepository;
        }

        public IEnumerable<SRNRolloutScheduleOutputDTO> GetRolloutSchedule(User user)
        {
            try
            {
                using (var conn = _dbContext.Database.GetDbConnection())
                {

                    string sql = "";
                    if (user.RoleId == UserRoles.Member)
                    {
                        sql = $@"
                            SELECT r.*
                            FROM dbo.vwRolloutSchedule r
                            WHERE r.MemberID IN 
                            (
                                SELECT MemberId 
                                FROM MemberUsers 
                                WHERE UserId = {user.Id}
                            )
                            ORDER BY r.SRNId, r.RowNumber
                        ";
                    }
                    //Get SRNs that are managed by the current leader only
                    else if (user.RoleId == UserRoles.ALGLeader)
                    {
                        sql = $@"
                            SELECT r.*
                            FROM dbo.vwRolloutSchedule r
                            WHERE r.ALGLeaderID IN 
                            (
                                SELECT MemberId 
                                FROM MemberUsers MU
                                INNER JOIN Members M ON MU.MemberId = M.Id
                                WHERE M.MembershipTypeId = 5
                                AND MU.UserId = {user.Id}
                            )
                            ORDER BY r.SRNId, r.RowNumber
                        ";
                    }
                    else
                    {
                        sql = $@"
                            SELECT r.*
                            FROM dbo.vwRolloutSchedule r
                            ORDER BY r.SRNId, r.RowNumber
                        ";
                    }


                    var result = conn.Query<SRNRolloutStatusModel>(sql);
                    var rolloutSchedule = _mapper.Map<IEnumerable<SRNRolloutScheduleOutputDTO>>(result);

                    return rolloutSchedule;
                }
            }
            catch (Exception ex)
            {
                Helpers.Helpers.LogError(_dbContext, ex, "Failed to get SRN Rollout Schedule.");
            }

            return null;
        }

        public List<BranchLocationOutputDTO> GetBranchLocations(int memberId, User user)
        {
            var selectRecord = _dbContext.Set<BranchLocation>()
                    .Include(i => i.SRN)
                        .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.Users)
                    .Where(i => i.SRN.MemberId == memberId)
                    .AsNoTracking()
                    .ToList()
                    .GroupBy(i => i.Name).Select(i => i.First()) //To select distinct location names
                    .ToList();


            if (selectRecord != null)
            {
                if (!_globalHelper.IsInternalSACRRAUser(user))
                {
                    selectRecord = selectRecord.Where(x => x.SRN.Member.Users.Any(i => i.UserId == user.Id)).ToList();
                }
            }

            var returnRecord = _mapper.Map<List<BranchLocationOutputDTO>>(selectRecord);

            return returnRecord;
        }

        public List<SRNSummaryDetailsOutputDTO> GetSRNSummaryDetails(User user)
        {


            if (_globalHelper.IsInternalSACRRAUser(user))
            {
                var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Include(i => i.CreditInformationClassification)
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.ALGLeader.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber,
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name,
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name,
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        BureauInstruction = m.BureauInstruction,
                        Comments = m.Comments,
                        CreationDate = m.CreationDate,
                        CreditInformationClassification = new CreditInformationClassification
                        {
                            Name = m.CreditInformationClassification.Name
                        }
                    })
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

                var queryItems = query.ToList();
                var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryDetailsOutputDTO>>(queryItems).ToList();

                foreach (var item in itemsToReturn)
                {
                    _globalHelper.GetDefaultValue(item);
                }

                itemsToReturn = itemsToReturn.OrderBy(x => x.MemberName).ThenBy(x => x.SRNNumber).ToList();

                return itemsToReturn;
            }

            else if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (user.RoleId == UserRoles.Member)
                {
                    var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(x => x.Users)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Where(i => i.SRNStatus.Name != "Rejected" && i.Member.Users.Any(x => x.UserId == user.Id))
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.ALGLeader.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        Comments = m.Comments,
                        BureauInstruction = m.BureauInstruction,
                        AccountStatusDate = m.AccountStatusDate,
                        ALGLeaderId = m.ALGLeaderId,
                        CreationDate = m.CreationDate
                    })
                    .AsQueryable();

                    var queryItems = query.ToList();
                    var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryDetailsOutputDTO>>(queryItems).ToList();

                    foreach (var item in itemsToReturn)
                    {
                        _globalHelper.GetDefaultValue(item);
                    }

                    itemsToReturn = itemsToReturn.OrderBy(x => x.MemberName).ThenBy(x => x.SRNNumber).ToList();

                    return itemsToReturn;
                }

                //Only return SRNs that are managed by the current ALG leader
                else if (user.RoleId == UserRoles.ALGLeader || user.RoleId == UserRoles.Bureau)
                {
                    var query = _dbContext.Set<SRN>()
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.AccountType)
                    .Include(i => i.SRNStatus)
                    .Include(i => i.NCRReportingAccountTypeClassification)
                    .Include(i => i.SPGroup)
                    .Include(i => i.ALGLeader)
                    .Select(m => new SRN
                    {
                        Id = m.Id,
                        Member = new Member
                        {
                            Id = m.Member.Id,
                            RegisteredName = m.Member.RegisteredName,
                            RegisteredNumber = m.Member.RegisteredNumber,
                            IndustryClassificationId = m.Member.IndustryClassificationId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Member.StakeholderManager.FirstName,
                                LastName = m.Member.StakeholderManager.LastName
                            },
                            Users = m.Member.Users.Select(x => new MemberUsers
                            {
                                UserId = x.UserId
                            }).ToList()
                        },
                        ALGLeader = new Member
                        {
                            RegisteredName = m.ALGLeader.RegisteredName
                        },
                        SPGroup = new SPGroup
                        {
                            SPNumber = m.SPGroup.SPNumber
                        },
                        SRNNumber = m.SRNNumber,
                        TradingName = m.TradingName,
                        AccountType = new AccountType
                        {
                            Name = m.AccountType.Name
                        },
                        NCRReportingAccountTypeClassification = new NCRReportingAccountTypeClassification
                        {
                            Name = m.NCRReportingAccountTypeClassification.Name
                        },
                        SRNStatus = new SRNStatus
                        {
                            Name = m.SRNStatus.Name
                        },
                        StatusLastUpdatedAt = m.StatusLastUpdatedAt,
                        AccountStatusDate = m.AccountStatusDate,
                        Comments = m.Comments,
                        BureauInstruction = m.BureauInstruction,
                        ALGLeaderId = m.ALGLeaderId,
                        CreationDate = m.CreationDate
                    })
                    .Where(i => i.SRNStatus.Name != "Rejected")
                    .AsQueryable();

                    if (user.RoleId == UserRoles.ALGLeader)
                    {
                        var algLeader = _dbContext.MemberUsers
                        .FirstOrDefault(i => i.UserId == user.Id
                            && i.Member.MembershipTypeId == MembershipTypes.ALGLeader);

                        if (algLeader != null)
                        {
                            query = query.Where(x => x.ALGLeaderId == algLeader.MemberId);
                        }

                        else
                        {
                            return new List<SRNSummaryDetailsOutputDTO>();
                        }
                    }

                    var queryItems = query.ToList();
                    var itemsToReturn = _mapper.Map<IEnumerable<SRNSummaryDetailsOutputDTO>>(queryItems).ToList();

                    foreach (var item in itemsToReturn)
                    {
                        _globalHelper.GetDefaultValue(item);
                    }

                    itemsToReturn = itemsToReturn.OrderBy(x => x.MemberName).ThenBy(x => x.SRNNumber).ToList();

                    return itemsToReturn;
                }

            }

            return new List<SRNSummaryDetailsOutputDTO>();
        }

        public IEnumerable<SRNSummaryAllDetailsOutputDTO> GetSRNSummaryAllDetails(User user)
        {
            using (var conn = _dbContext.Database.GetDbConnection())
            {
                var sql = "";
                // If regular member we only want the SRNS that they have access to
                if (user.RoleId == UserRoles.Member)
                {
                    sql = $@"
                        SELECT v.*
                        FROM dbo.vwSRNSummaryAllDetails v
                        INNER JOIN dbo.MemberUsers mu ON v.MemberID = mu.MemberId
                        WHERE MU.UserId = {user.Id}
                        ORDER BY v.SRNNumber;
                    ";
                }
                // If internal sacrra user or bureau
                else if (_globalHelper.IsInternalSACRRAUser(user) || (user.RoleId == UserRoles.Bureau))
                {
                    sql = $@"
                        SELECT v.*
                        FROM dbo.vwSRNSummaryAllDetails v
                        ORDER BY v.SRNNumber;
                    ";
                }
                // If ALG Leader
                else if (user.RoleId == UserRoles.ALGLeader)
                {
                    sql = $@"
                        SELECT v.*
                        FROM dbo.vwSRNSummaryAllDetails v
                        WHERE v.ALGLeaderID IN
                        (
                            SELECT MemberId 
                            FROM MemberUsers mu 
                            INNER JOIN Members m
                            ON mu.MemberId = m.Id 
                            WHERE m.MembershipTypeId = 5 AND mu.UserId = {user.Id}
                        )
                        ORDER BY v.SRNNumber;
                    ";
                }
                else
                {
                    throw new Exception("Invalid user role");
                }

                try
                {
                    var result = conn.Query<SRNSummaryAllDetailsOutputDTO>(sql, commandTimeout: 180);

                    result = SanitizeDates(result);

                    return result;
                }
                catch (Exception ex)
                {
                    throw new Exception();
                }
            }
        }

        private IEnumerable<SRNSummaryAllDetailsOutputDTO> SanitizeDates(IEnumerable<SRNSummaryAllDetailsOutputDTO> data)
        {
            foreach (var dataItem in data)
            {
                dataItem.SRNCreationDate = !string.IsNullOrEmpty(dataItem.SRNCreationDate) ? dataItem.SRNCreationDate.Substring(0, 10) : null;
                dataItem.StatusLastUpdatedAt = !string.IsNullOrEmpty(dataItem.StatusLastUpdatedAt) ? dataItem.StatusLastUpdatedAt.Substring(0, 10) : null;
                dataItem.SRNSignoffDate = !string.IsNullOrEmpty(dataItem.SRNSignoffDate) ? dataItem.SRNSignoffDate.Substring(0, 10) : null;
            }

            return data;

        }

        public IEnumerable<SRNSummaryAllDetailsBureauOutputDTO> GetSRNSummaryAllDetailsBureau(User user)
        {
            var data = GetSRNSummaryAllDetails(user);

            var result = _mapper.Map<IEnumerable<SRNSummaryAllDetailsBureauOutputDTO>>(data);
            return result;
        }

        public List<SRNReTestingOutputDTO> GetLiveSRNFileList(int memberId)
        {
            try
            {
                var srnUpdateHistoryList = new List<SRNReTestingOutputDTO>();
                var srnUpdateHistoryDBList = _dbContext.vwSRNWithUpdateHistories
                    .Where(x => x.MemberId == memberId)
                    .Where(x => x.IsComple == true)
                    .Where(x => x.SRNStatusId == 4)
                    .Where(x => x.IsLatestHistory == 1)
                    .ToList();
                var srnStatusList = _dbContext.SRNStatuses.ToList();

                foreach (var srnUpdate in srnUpdateHistoryDBList)
                {
                    var srnUpdateHistory = new SRNReTestingOutputDTO()
                    {
                        FileType = srnUpdate.HistoryFileType <= 0 ? "N/A" : EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>(srnUpdate.HistoryFileType)?.Value,
                        SRNNumber = srnUpdate.SRNNumber,
                        SRNStatusUpdateHistoryId = srnUpdate.HistoryId,
                        CurrentStatus = srnStatusList
                            .Where(x => x.Id == srnUpdate.SRNStatusId)
                            .FirstOrDefault()?
                            .Name,
                        DailyFileDevelopmentStartDate = srnUpdate.DailyFileDevelopmentStartDate == null ? "N/A" : srnUpdate.DailyFileDevelopmentStartDate.ToString(),
                        DailyFileDevelopmentEndDate = srnUpdate.DailyFileDevelopmentEndDate == null ? "N/A" : srnUpdate.DailyFileDevelopmentEndDate.ToString(),
                        DailyFileTestStartDate = srnUpdate.DailyFileTestStartDate == null ? "N/A" : srnUpdate.DailyFileTestStartDate.ToString(),
                        DailyFileTestEndDate = srnUpdate.DailyFileTestEndDate == null ? "N/A" : srnUpdate.DailyFileTestEndDate.ToString(),
                        DailyFileGoLiveDate = srnUpdate.DailyFileGoLiveDate == null ? "N/A" : srnUpdate.DailyFileGoLiveDate.ToString(),
                        MonthlyFileDevelopmentStartDate = srnUpdate.MonthlyFileDevelopmentStartDate == null ? "N/A" : srnUpdate.MonthlyFileDevelopmentStartDate.ToString(),
                        MonthlyFileDevelopmentEndDate = srnUpdate.MonthlyFileDevelopmentEndDate == null ? "N/A" : srnUpdate.MonthlyFileDevelopmentEndDate.ToString(),
                        MonthlyFileTestStartDate = srnUpdate.MonthlyFileTestStartDate == null ? "N/A" : srnUpdate.MonthlyFileTestStartDate.ToString(),
                        MonthlyFileTestEndDate = srnUpdate.MonthlyFileTestEndDate == null ? "N/A" : srnUpdate.MonthlyFileTestEndDate.ToString(),
                        MonthlyFileGoLiveDate = srnUpdate.MonthlyFileGoLiveDate == null ? "N/A" : srnUpdate.MonthlyFileGoLiveDate.ToString(),
                        IsDailyFile = srnUpdate.HistoryFileType == (int)SRNStatusFileTypes.DailyFile,
                        IsMonthlyFile = srnUpdate.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile,
                        LastSubmission = srnUpdate.HistorySRNLastSubmissionDate == null ? "N/A" : srnUpdate.HistorySRNLastSubmissionDate.ToString(),
                        UpdateType = srnUpdate.UpdateType == null ? "N/A" : EnumHelper.GetEnumIdValuePair<SRNStatusUpdateRequestUpdateTypeEnum>((int)srnUpdate.UpdateType)?.Value,
                        SRNFileTestingStatusReason = srnUpdate.HistorySRNFileTestingStatusReason,
                    };

                    srnUpdateHistoryList.Add(srnUpdateHistory);
                }

                return srnUpdateHistoryList;
            }
            catch (Exception exception)
            {
                throw new Exception("Unable to fetch SRN files that are live.", exception);
            }
        }

        public void updateSRNTestingStatus(SRNRequestReTestingInputDTO updatedSRN, User user)
        {
            var srn = _dbContext.SRNs
                .Where(i => i.SRNNumber == updatedSRN.SRNNumber)
                .Include(x => x.SRNStatusUpdates)
                .FirstOrDefault();

            if (srn.Id != 0)
            {
                Member member;
                var httpClient = new HttpClient();
                string testEndDate;
                string convertedTestEndDate;
                object camundaVariables;
                string requestBody;
                StringContent httpRequestContent;
                string requestURL;
                HttpRequestMessage webRequest;
                HttpResponseMessage httpRequestResult;

                member = _dbContext.Members
                    .Where(m => m.Id == srn.MemberId)
                    .FirstOrDefault();

                if (updatedSRN.IsDailyFile)
                {
                    testEndDate = DateTime.Parse(updatedSRN.DailyFileTestEndDate).AddDays(-3).ToString("yyyy-MM-dd");
                    convertedTestEndDate = new DateTimeOffset(DateTime.Parse(testEndDate).ToUniversalTime()).ToString("yyyy-MM-ddTHH:mm:ssZ");

                    camundaVariables = new
                    {
                        variables = new
                        {
                            dailyFileDevelopmentStartDate = new { value = updatedSRN.DailyFileDevelopmentStartDate, type = "String" },
                            dailyFileDevelopmentEndDate = new { value = updatedSRN.DailyFileDevelopmentEndDate, type = "String" },
                            dailyFileTestStartDate = new { value = updatedSRN.DailyFileTestStartDate, type = "String" },
                            dailyFileTestEndDate = new { value = updatedSRN.DailyFileTestEndDate, type = "String" },
                            dailyFileGoLiveDate = new { value = updatedSRN.DailyFileGoLiveDate, type = "String" },
                            SRNId = new { value = srn.Id, type = "Long" },
                            srnNumber = new { value = srn.SRNNumber, type = "String" },
                            newSrn = new { value = false, type = "boolean" },
                            SRNUpdateType = new { value = SRNStatusFileTypes.DailyFile, type = "Long" },
                            testEndDate = new { value = convertedTestEndDate, type = "String" },
                            stakeHolderManagerAssignee = new { value = member.StakeholderManagerId.ToString(), type = "String" },
                            fileType = new { value = (int)SRNStatusFileTypes.DailyFile, type = "Long" },
                            isLiveFileSubmissionSuspended = new { value = updatedSRN.IsLiveFileSubmissionSuspended, type = "boolean" },
                            srnFileTestingStatusReason = new { value = updatedSRN.SRNFileTestingStatusReason, type = "String" }
                        },
                        businessKey = srn.SRNNumber
                    };
                }
                else
                {
                    testEndDate = DateTime.Parse(updatedSRN.MonthlyFileTestEndDate).AddDays(-3).ToString("yyyy-MM-dd");
                    convertedTestEndDate = new DateTimeOffset(DateTime.Parse(testEndDate).ToUniversalTime()).ToString("yyyy-MM-ddTHH:mm:ssZ");

                    camundaVariables = new
                    {
                        variables = new
                        {
                            monthlyFileDevelopmentStartDate = new { value = updatedSRN.MonthlyFileDevelopmentStartDate, type = "String" },
                            monthlyFileDevelopmentEndDate = new { value = updatedSRN.MonthlyFileDevelopmentEndDate, type = "String" },
                            monthlyFileTestStartDate = new { value = updatedSRN.MonthlyFileTestStartDate, type = "String" },
                            monthlyFileTestEndDate = new { value = updatedSRN.MonthlyFileTestEndDate, type = "String" },
                            monthlyFileGoLiveDate = new { value = updatedSRN.MonthlyFileGoLiveDate, type = "String" },
                            SRNId = new { value = srn.Id, type = "Long" },
                            srnNumber = new { value = srn.SRNNumber, type = "String" },
                            newSrn = new { value = false, type = "boolean" },
                            SRNUpdateType = new { value = SRNStatusFileTypes.DailyFile, type = "Long" },
                            testEndDate = new { value = convertedTestEndDate, type = "String" },
                            stakeHolderManagerAssignee = new { value = member.StakeholderManagerId.ToString(), type = "String" },
                            fileType = new { value = (int)SRNStatusFileTypes.MonthlyFile, type = "Long" },
                            isLiveFileSubmissionSuspended = new { value = updatedSRN.IsLiveFileSubmissionSuspended, type = "boolean" },
                            srnFileTestingStatusReason = new { value = updatedSRN.SRNFileTestingStatusReason, type = "String" }
                        },
                        businessKey = srn.SRNNumber
                    };
                }

                requestBody = JsonConvert.SerializeObject(camundaVariables);
                httpRequestContent = new StringContent(requestBody, Encoding.UTF8, "application/json");
                requestURL = $"{_configSettings.CamundaBaseAddress}/process-definition/key/SRN-Status-Update-To-Test/start";
                webRequest = new HttpRequestMessage(HttpMethod.Post, requestURL)
                {
                    Content = httpRequestContent
                };
                httpRequestResult = httpClient.Send(webRequest);
                httpRequestResult.EnsureSuccessStatusCode();
                _dbContext.SaveChanges();
            }
        }

        public List<int> CreateSrnEntries(List<SRNRequestInputDTO> srnEntryList, User user)
        {
            var resultSrnIDList = new List<int>();
            if (user == null) throw new InvalidUserException();

            foreach (var srnEntry in srnEntryList)
            {
                switch (srnEntry.IsMonthlyFile)
                {
                    case true when srnEntry.IsDailyFile:
                        srnEntry.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
                        break;

                    case true when !srnEntry.IsDailyFile:
                        srnEntry.FileType = SRNStatusFileTypes.MonthlyFile;
                        break;

                    case false when srnEntry.IsDailyFile:
                        srnEntry.FileType = SRNStatusFileTypes.DailyFile;
                        break;

                    default:
                        throw new Exception("SRN is not daily or monthly file.");
                }
            }

            var mappedSrnEntryList = _mapper.Map<List<SRN>>(srnEntryList);
            var newSrnStatus = _dbContext.Set<SRNStatus>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Name.ToLower() == "requested");
            var newSrnStatusReason = _dbContext.Set<SRNStatusReason>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Name.ToLower() == "new development");
            var newSrnRolloutStatus = _dbContext.RolloutStatuses
                .FirstOrDefault(x => x.Name.ToLower() == "requested");

            if (newSrnRolloutStatus == null) throw new Exception("Rollout status not found.");
            if (newSrnStatus == null) throw new Exception("New SRN status not found.");
            if (newSrnStatusReason == null) throw new Exception("New SRN status reason not found.");

            var i = 0;
            foreach (var mappedSrnEntry in mappedSrnEntryList)
            {
                if (user.RoleId == UserRoles.ALGLeader)
                {
                    var algLeader = _dbContext.Members
                        .Include(x => x.Users)
                        .Where(i => i.MembershipTypeId == MembershipTypes.ALGLeader)
                        .FirstOrDefault(x => x.Users.Any(y => y.UserId == user.Id));

                    if (algLeader == null)
                        throw new Exception("ALG Leader not found for current user.");

                    mappedSrnEntry.ALGLeaderId = algLeader.Id;
                }

                mappedSrnEntry.DateRequested = DateTime.Now;
                mappedSrnEntry.SRNStatusId = newSrnStatus.Id;
                mappedSrnEntry.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.SRNs.Add(mappedSrnEntry);

                try
                {
                    _dbContext.SaveChanges();
                    resultSrnIDList.Add(mappedSrnEntry.Id);
                }
                catch (Exception exception)
                {
                    throw new Exception("Unable to create SRN.", exception);
                }

                var srnMember = _dbContext.Members
                    .FirstOrDefault(x => x.Id == mappedSrnEntry.MemberId);
                bool doesSrnRequireTesting;

                if (srnMember == null) throw new Exception("Member linked to SRN not found.");

                if (srnMember.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    var memberAlgLeader = _dbContext.Members
                        .FirstOrDefault(x => x.Id == mappedSrnEntry.ALGLeaderId);

                    if (memberAlgLeader == null) throw new Exception("ALG Leader linked to SRN not found.");

                    doesSrnRequireTesting = memberAlgLeader.RequireSRNTesting;
                }
                else
                {
                    doesSrnRequireTesting = srnMember.RequireSRNTesting;
                }

                var srnEntry = srnEntryList[i];
                StartNewSRNApplicationProcess(srnEntry, mappedSrnEntry.Id, doesSrnRequireTesting);
                i++;
            }

            _dbContext.SaveChanges();

            foreach (var mappedSrnEntry in mappedSrnEntryList)
            {
                var stagingChangeLog = new MemberStagingChangeLogResource();
                var entityBlob = JsonConvert.SerializeObject(mappedSrnEntry, Formatting.None, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });
                var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog, Formatting.None, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });

                _SRNHelper.CreateEventLog(_dbContext, user.Id, "SRN Create", mappedSrnEntry.TradingName, entityBlob, stagingBlob, mappedSrnEntry.Id, "SRN");
            }

            return resultSrnIDList;
        }
        private void StartNewSRNApplicationProcess(SRNRequestInputDTO srnInput, int srnId, bool requireTesting)
        {
            var variables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>();

            try
            {
                switch (srnInput.FileType)
                {
                    case SRNStatusFileTypes.DailyFile:
                        {
                            variables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "SRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MemberId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MemberId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "fileType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.FileType.ToString() },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "testEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.DailyFileTestEndDate > DateTime.Now
                                                    ? srnInput.DailyFileTestEndDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "goLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.DailyFileGoLiveDate > DateTime.Now
                                                    ? srnInput.DailyFileGoLiveDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "requireTesting",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requireTesting ? "yes" : "no" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileDevelopmentStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileDevelopmentEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileTestStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileTestStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileTestEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileGoLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };
                            break;
                        }

                    case SRNStatusFileTypes.MonthlyFile:
                        {
                            variables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "SRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MemberId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MemberId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "fileType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.FileType.ToString() },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "testEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.MonthlyFileTestEndDate > DateTime.Now
                                                    ? srnInput.MonthlyFileTestEndDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "goLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.MonthlyFileGoLiveDate > DateTime.Now
                                                    ? srnInput.MonthlyFileGoLiveDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "requireTesting",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requireTesting ? "yes" : "no" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileDevelopmentStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileDevelopmentEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileTestStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileTestStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileTestEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileGoLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };
                            break;
                        }

                    case SRNStatusFileTypes.MonthlyAndDailyFile:
                        {
                            variables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "SRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MemberId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MemberId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "fileType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.FileType.ToString() },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "testEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.DailyFileTestEndDate > DateTime.Now
                                                    ? srnInput.DailyFileTestEndDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "goLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            {
                                                "value", srnInput.DailyFileGoLiveDate > DateTime.Now
                                                    ? srnInput.DailyFileGoLiveDate.Value.AddDays(-3)
                                                        .ToString("yyyy-MM-dd")
                                                    : srnInput.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd")
                                            },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "requireTesting",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requireTesting ? "yes" : "no" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileDevelopmentStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileDevelopmentEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileTestStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileTestStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileTestEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "DailyFileGoLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
{
                                        "MonthlyFileDevelopmentStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileDevelopmentEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileTestStartDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileTestStartDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileTestEndDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MonthlyFileGoLiveDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnInput.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };
                            break;
                        }
                }
            }
            catch (Exception exception)
            {
                throw new Exception("Unable to create SRN application process.", exception);
            }

            var json = JsonConvert.SerializeObject(variables);
            var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/New-SRN-Application/start";
            var restClient = new RestClient(uri);
            var request = new RestRequest();

            request.AddJsonBody(json);

            var response = restClient.Post(request);
            if (response.StatusCode != HttpStatusCode.OK)
            {
                throw new Exception("An error occured while kicking off new SRN Application Workflow." + response.Content.ToString());
            }
        }

        public void UpdateSRNStatus(string newStatus, SRN srn)
        {
            if (srn != null)
            {
                var status = _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefault(s => s.Name.ToLower() == newStatus.Trim().ToLower());

                srn.SRNStatusId = (status != null) ? status.Id : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.SaveChanges();
            }
        }

        public async Task RequestSRNStatusUpdate(List<SRNStatusUpdateRequestDTO> requests, User user)
        {
            if (requests != null)
            {

                foreach (var request in requests)
                {
                    var newStatus = await _dbContext.SRNStatuses
                        .FirstOrDefaultAsync(i => i.Id == request.SRNStatusId);

                    SRN srn = null;

                    if (newStatus != null)
                    {
                        srn = await _dbContext.Set<SRN>()
                                    .Include(i => i.SRNStatusReason)
                                    .Include(i => i.SRNStatus)
                                    .Include(i => i.SRNStatusUpdates)
                                    .FirstOrDefaultAsync(i => i.Id == request.Id);

                        var sacrraAdmin = await _dbContext.Users
                                .FirstOrDefaultAsync(i => i.RoleId == UserRoles.SACRRAAdministrator);

                        var sacrraAdminAssinee = (sacrraAdmin != null) ? sacrraAdmin.Id.ToString() : "";

                        var stakeholderManager = await _dbContext.Members
                                    .Select(m => new Member { StakeholderManagerId = m.StakeholderManagerId, Id = m.Id })
                                    .FirstOrDefaultAsync(i => i.Id == srn.MemberId);

                        var shmId = "";
                        if (stakeholderManager != null)
                            shmId = (stakeholderManager.StakeholderManagerId > 0) ? stakeholderManager.StakeholderManagerId.ToString() : "";

                        //Kick off the workflow if the status is "Closed" or "Closure Pending"
                        if (newStatus.Name == "Closed" || newStatus.Name == "Closure Pending")
                        {
                            if (request.UpdateTypeId == null)
                            {
                                throw new InvalidSRNStatusUpdateException();
                            }
                            string updateType = "";
                            bool isDateInThePast = false;
                            string dateString = "";

                            if (srn.SRNStatus.Name == "Closure Pending" || HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            if (request.UpdateTypeId == SRNStatusTypes.LastSubmission && request.LastSubmissionDate == null)
                                throw new InvalidSRNStatusUpdateException();

                            if (request.UpdateTypeId == SRNStatusTypes.BureauInstruction && request.AccountStatusDate == null)
                                throw new InvalidSRNStatusUpdateException();


                            if (request.UpdateTypeId == SRNStatusTypes.LastSubmission)
                            {
                                updateType = "lastSubmission";
                                isDateInThePast = IsInThePast(request.LastSubmissionDate);
                            }
                            else if (request.UpdateTypeId == SRNStatusTypes.BureauInstruction)
                            {
                                updateType = "bureauInstruction";
                                isDateInThePast = IsInThePast(request.AccountStatusDate);
                            }

                            if (isDateInThePast)
                                dateString = "inThePast";
                            else
                                dateString = "inTheFuture";


                            if (!string.IsNullOrEmpty(dateString) && request.Id > 0)
                            {

                                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "updateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", updateType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "lastSubmissionDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "statusDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", shmId },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

                                using (var client = new HttpClient())
                                {
                                    var contractResolver = new DefaultContractResolver
                                    {
                                        NamingStrategy = new CamelCaseNamingStrategy()
                                    };
                                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                                    {
                                        ContractResolver = contractResolver,
                                        Formatting = Formatting.Indented
                                    });
                                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update/start";
                                    var result = await client.PostAsync(uri, content);
                                    result.EnsureSuccessStatusCode();
                                }
                            }
                        }
                        //Kick off workflow for non-cancellation statuses
                        else if (newStatus.Name == "Live" || newStatus.Name == "Running Down" || newStatus.Name == "Dormant")
                        {
                            if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            await CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, user.Id, "");
                        }
                        else if (newStatus.Name == "Test")
                        {
                            if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            await CreateStatusUpdateHistory(srn, request, newStatus, sacrraAdminAssinee, shmId, user.Id);
                        }

                        if (srn != null)
                        {
                            var stagingChangeLog = await CreateSRNStatusStagingChangeLog(request, srn, user);

                            var entityBlob = JsonConvert.SerializeObject(srn, Formatting.None, new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });

                            var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog, Formatting.None, new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });


                            if (request.UpdateTypeId == SRNStatusTypes.BureauInstruction)
                            {
                                srn.AccountStatusDate = (request.AccountStatusDate != null) ? request.AccountStatusDate : DateTime.Now;
                            }

                            srn.LastSubmissionDate = (request.LastSubmissionDate != null) ? request.LastSubmissionDate : null;
                            srn.BureauInstruction = request.BureauInstruction;
                            srn.SRNStatusReasonId = (request.SRNStatusReasonId > 0) ? (int?)request.SRNStatusReasonId : null;
                            srn.FileType = (request.FileTypeId > 0) ? request.FileTypeId : srn.FileType;
                            srn.Comments = request.Comments;
                            //NOT DOT update the status at this point, it will be updated by the topic "update-srn-status-to-pending-closure"

                            await _dbContext.SaveChangesAsync();

                            _SRNHelper.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, stagingBlob, srn.Id, "SRN");

                        }
                    }
                    else
                    {
                        throw new InvalidSRNStatusUpdateException();
                    }
                }
            }
        }

        public static bool IsInThePast(DateTime? dateInput)
        {
            if (dateInput == null)
                return false;
            if (dateInput == DateTime.MinValue)
                return false;
            if (dateInput <= DateTime.Now.Date)
                return true;

            return false;
        }
        public static bool HasActiveStatusUpdate(SRN srn, SRNStatusUpdateRequestDTO request)
        {
            // get last status IsComple status for each file type for the SRN
            var dailyFileIsComplete = srn.SRNStatusUpdates
                .OrderByDescending(i => i.Id)
                .FirstOrDefault(i => i.FileType == SRNStatusFileTypes.DailyFile)?.IsComple ?? true;
            var monthlyFileIsComplete = srn.SRNStatusUpdates
                .OrderByDescending(i => i.Id)
                .FirstOrDefault(i => i.FileType == SRNStatusFileTypes.MonthlyFile)?.IsComple ?? true;
            var nullFileIsComplete = srn.SRNStatusUpdates
                .OrderByDescending(i => i.Id)
                .FirstOrDefault(i => i.FileType == null)?.IsComple ?? true;

            var hasActiveStatusUpdate = false;
            switch (request.FileTypeId)
            {
                case SRNStatusFileTypes.MonthlyAndDailyFile:
                    hasActiveStatusUpdate = !dailyFileIsComplete || !monthlyFileIsComplete;
                    break;
                case SRNStatusFileTypes.DailyFile:
                    hasActiveStatusUpdate = !dailyFileIsComplete;
                    break;
                case SRNStatusFileTypes.MonthlyFile:
                    hasActiveStatusUpdate = !monthlyFileIsComplete;
                    break;
                case null:
                    hasActiveStatusUpdate = !nullFileIsComplete;
                    break;
            }

            return hasActiveStatusUpdate;
        }

        private async Task<ProcessInstanceInfoResource> CreateSRNStatusUpdateTask(SRNStatus newStatus, SRNStatusUpdateRequestDTO request, string sacrraAdminAssinee, int userId, string fileType)
        {
            var isLiveOrTest = (newStatus.Name == "Live" || newStatus.Name == "Test") ? "yes" : "no";

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "isLiveOrTest",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", isLiveOrTest },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient())
            {
                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-Non-Cancellations/start";
                var result = await client.PostAsync(uri, content);
                result.EnsureSuccessStatusCode();

                var jsonResult = await result.Content.ReadAsStringAsync();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult);

                return processInfo;
            }
        }

        private async Task CreateStatusUpdateHistory(SRN srn, SRNStatusUpdateRequestDTO request, SRNStatus newStatus, string sacrraAdminAssinee, string shmId, int userId)
        {
            if (srn != null && request != null)
            {
                if (HasActiveStatusUpdate(srn, request))
                    throw new SRNStatusUpdateInProgressException();

                var updateHistoryModel = _mapper.Map<SRNStatusUpdateHistory>(request);
                updateHistoryModel.DateCreated = DateTime.Now;
                updateHistoryModel.Comments = request.Comments;

                var updateNumber = Guid.NewGuid().ToString();

                if (request.FileTypeId == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    ProcessInstanceInfoResource dailyFileTask = null;

                    if (newStatus.Name == "Test")
                    {
                        var testEndDate = (request.DailyFileTestEndDate != null) ? string.Format("{0:yyyy-MM-dd}", request.DailyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.DailyFileTestEndDate != null) ? (DateTime)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01");

                        //If test date is the future
                        if (testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        dailyFileTask = await CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "DailyFile");
                    }
                    else
                    {
                        dailyFileTask = await CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "DailyFile");
                    }

                    var dailyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request);
                    dailyFileUpdate.FileType = SRNStatusFileTypes.DailyFile;
                    dailyFileUpdate.DateCreated = DateTime.Now;
                    dailyFileUpdate.UpdateNumber = updateNumber;

                    var rolloutStatus = await _dbContext.RolloutStatuses
                        .FirstOrDefaultAsync(i => i.Name == newStatus.Name);

                    if (rolloutStatus != null)
                        dailyFileUpdate.RolloutStatusId = rolloutStatus.Id;

                    if (dailyFileTask != null)
                        dailyFileUpdate.ProcessInstanceId = dailyFileTask.Id;

                    ProcessInstanceInfoResource monthlyFileTask = null;
                    if (newStatus.Name == "Test")
                    {
                        var testEndDate = (request.MonthlyFileTestEndDate != null) ? string.Format("{0:yyyy-MM-dd}", request.MonthlyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.MonthlyFileTestEndDate != null) ? (DateTime)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01");

                        //If test date is the future
                        if (testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        monthlyFileTask = await CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "MonthlyFile");
                    }
                    else
                    {
                        monthlyFileTask = await CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "MonthlyFile");
                    }

                    var monthlyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request);
                    monthlyFileUpdate.FileType = SRNStatusFileTypes.MonthlyFile;
                    monthlyFileUpdate.DateCreated = DateTime.Now;
                    monthlyFileUpdate.UpdateNumber = updateNumber;

                    if (monthlyFileTask != null)
                        monthlyFileUpdate.ProcessInstanceId = monthlyFileTask.Id;

                    if (rolloutStatus != null)
                        monthlyFileUpdate.RolloutStatusId = rolloutStatus.Id;

                    srn.SRNStatusUpdates.Add(dailyFileUpdate);
                    srn.SRNStatusUpdates.Add(monthlyFileUpdate);
                }
                else if (request.FileTypeId == SRNStatusFileTypes.DailyFile || request.FileTypeId == SRNStatusFileTypes.MonthlyFile)
                {
                    ProcessInstanceInfoResource updateTask = null;
                    if (newStatus.Name == "Test")
                    {
                        var testEndDate = "1900-01-01";
                        DateTime testDate = Convert.ToDateTime("1900-01-01");

                        if (request.FileTypeId == SRNStatusFileTypes.DailyFile)
                        {
                            testDate = (request.DailyFileTestEndDate != null) ? (DateTime)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01");
                        }
                        else if (request.FileTypeId == SRNStatusFileTypes.MonthlyFile)
                        {
                            testDate = (request.MonthlyFileTestEndDate != null) ? (DateTime)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01");
                        }

                        //If test date is the future
                        if (testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        updateTask = await CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, request.FileTypeId.ToString());
                    }
                    else
                    {
                        updateTask = await CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, request.FileTypeId.ToString());
                    }

                    if (updateTask != null)
                        updateHistoryModel.ProcessInstanceId = updateTask.Id;

                    var rolloutStatus = await _dbContext.RolloutStatuses
                        .FirstOrDefaultAsync(i => i.Name == newStatus.Name);

                    if (rolloutStatus != null)
                        updateHistoryModel.RolloutStatusId = rolloutStatus.Id;

                    updateHistoryModel.UpdateNumber = updateNumber;
                    srn.SRNStatusUpdates.Add(updateHistoryModel);
                }

            }
        }

        private async Task<ProcessInstanceInfoResource> CreateSRNStatusUpdateToTestTask(SRNStatus newStatus, string testEndDate, SRNStatusUpdateRequestDTO request, string sacrraAdminAssinee, string stakeHolderManagerAssignee, int userId, string fileType)
        {

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "testEndDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", testEndDate },
                                                    { "type", "string" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", stakeHolderManagerAssignee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "newSrn",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", "False" },
                                                    { "type", "Boolean" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient())
            {
                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-To-Test/start";
                var result = await client.PostAsync(uri, content);
                result.EnsureSuccessStatusCode();

                var jsonResult = await result.Content.ReadAsStringAsync();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult);

                return processInfo;
            }
        }

        private async Task<MemberStagingChangeLogResource> CreateSRNStatusStagingChangeLog(SRNStatusUpdateRequestDTO updateRequest, SRN srn, User user)
        {
            var stagingChangeLog = new MemberStagingChangeLogResource();

            //Add type of status update
            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "SRN Status",
                OldValue = await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)srn.SRNStatusId, "SRNStatusId"),
                NewValue = await Helpers.Helpers.GetPropertyVaueById(_dbContext, (int)updateRequest.SRNStatusId, "SRNStatusId")
            });
            if (updateRequest.AccountStatusDate != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "SRN Status Date",
                    OldValue = (srn.AccountStatusDate != null) ? srn.AccountStatusDate.ToString() : "",
                    NewValue = (updateRequest.AccountStatusDate != null) ? updateRequest.AccountStatusDate.Value.ToString() : ""
                });
            }
            if (updateRequest.LastSubmissionDate != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Last Submission Date",
                    OldValue = (srn.LastSubmissionDate != null) ? srn.LastSubmissionDate.ToString() : "",
                    NewValue = (updateRequest.LastSubmissionDate != null) ? updateRequest.LastSubmissionDate.Value.ToString() : ""
                });
            }
            if (updateRequest.SRNStatusReasonId > 0)
            {
                var newValue = await _dbContext.SRNStatusReasons
                    .FirstOrDefaultAsync(i => i.Id == updateRequest.SRNStatusReasonId);

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "SRN Status Reason",
                    OldValue = (srn.SRNStatusReason != null) ? srn.SRNStatusReason.Name : "",
                    NewValue = newValue.Name
                });
            }

            return stagingChangeLog;
        }

        public async Task UpdateSRNWhileInProcess(SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {
            var existingSRN = _dbContext.SRNs
                .Include(x => x.Member)
                    .ThenInclude(x => x.Users)
                .Include(i => i.SRNStatusUpdates)
                .Include(i => i.Contacts)
                .Include(i => i.SRNStatus)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == srnUpdateInputDTO.Id);

            srnUpdateInputDTO.ALGLeaderId = (srnUpdateInputDTO.ALGLeaderId > 0) ? srnUpdateInputDTO.ALGLeaderId : existingSRN.ALGLeaderId;

            if (srnUpdateInputDTO.IsDailyFile && !srnUpdateInputDTO.IsMonthlyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.DailyFile;
            }

            if (!srnUpdateInputDTO.IsDailyFile && srnUpdateInputDTO.IsMonthlyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.MonthlyFile;
            }

            if (srnUpdateInputDTO.IsDailyFile && srnUpdateInputDTO.IsMonthlyFile)
            {
                srnUpdateInputDTO.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
            }

            await _SRNHelper.ApplySRNChangesForSHM(existingSRN.Id, srnUpdateInputDTO, user);
        }

        public string GetSrnSummaryFileExtract(User user)
        {
            try
            {
                return _SRNHelper.GetCombinedExtractViewByUserRole(user);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Helper method to extract and format date values from Camunda task variables.
        /// Handles both PascalCase and camelCase variable naming conventions.
        /// </summary>
        /// <param name="taskVariables">The dynamic task variables object from Camunda</param>
        /// <param name="pascalCaseKey">The PascalCase version of the variable name</param>
        /// <param name="camelCaseKey">The camelCase version of the variable name</param>
        /// <returns>Formatted date string in dd-MM-yyyy format, or null if variable not found</returns>
        private static string GetTaskVariableAsFormattedDate(dynamic taskVariables, string pascalCaseKey, string camelCaseKey)
        {
            // Try PascalCase first, then camelCase as fallback
            var variable = taskVariables[pascalCaseKey] ?? taskVariables[camelCaseKey];
            return variable?["value"]?.ToString("dd-MM-yyyy");
        }
    }
}
