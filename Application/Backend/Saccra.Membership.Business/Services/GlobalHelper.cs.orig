using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Resources.IdValuePair;
using RestSharp;
using Sacrra.Membership.Business.Resources.SRNStatus;
using RestSharp.Authenticators;
using System.Threading;
using Microsoft.VisualBasic;
using System.Security.Claims;
using Sacrra.Membership.Business.DTOs.AuthDTOs;

namespace Sacrra.Membership.Business.Services
{
    public class GlobalHelper
    {
        private readonly AppDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ConfigSettings _configSettings;
        private readonly IMapper _mapper;
        private readonly Auth0APIManagement _auth0APIManagementSettings;

        public GlobalHelper(AppDbContext dbContext, IHttpContextAccessor httpContextAccessor,
            IOptions<ConfigSettings> configSettings, IMapper mapper, IOptions<Auth0APIManagement> auth0APIManagementSettings)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _configSettings = configSettings.Value;
            _mapper = mapper;
            _auth0APIManagementSettings = auth0APIManagementSettings.Value;
        }

        public async Task<string> GetAuth0APIToken(Auth0SettingsDTO auth0APIManagementSettings)
        {
            if (auth0APIManagementSettings != null)
            {
                using (var client = new HttpClient())
                {
                    var auth0Object = new
                    {
                        grant_type = "client_credentials",
                        client_id = auth0APIManagementSettings.ClientID,
                        client_secret = auth0APIManagementSettings.ClientSecret,
                        audience = auth0APIManagementSettings.Audience
                    };
                    var json = JsonConvert.SerializeObject(auth0Object);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = "https://" + auth0APIManagementSettings.Domain + "/oauth/token";
                    var result = await client.PostAsync(uri, content);

                    result.EnsureSuccessStatusCode();

                    var token = JObject.Parse(await result.Content.ReadAsStringAsync())["access_token"].ToString();

                    return token;
                }
            }

            return null;
        }

        public User GetUserByAuth0Id(string auth0Id)
        {
            var user = _dbContext.Users.FirstOrDefault(i => i.Auth0Id == auth0Id);

            return user;
        }

        public virtual bool IsInternalSACRRAUser(User user)
        {
            if (user.RoleId == UserRoles.FinancialAdministrator ||
                user.RoleId == UserRoles.SACRRAAdministrator ||
                user.RoleId == UserRoles.StakeHolderAdministrator ||
                user.RoleId == UserRoles.StakeHolderManager)
            {
                return true;
            }

            return false;
        }

        public string GetPropertyDisplayName(PropertyInfo prop)
        {
            var displayName = prop.Name;
            var attributes = prop.GetCustomAttributes(typeof(DisplayAttribute),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    displayName = attributes.Cast<DisplayAttribute>().Single().Name;
                }
            }

            return displayName;
        }

        public void LogError(AppDbContext _dbContext, Exception ex, string message = null, int statusCode = 0)
        {
            if (ex != null)
            {
                Log.Error(ex, message);
            }
        }

        public async Task<List<TaskListResource>> GetUserTasks(string assignee, string processDefinitionKey = null)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var definitionKey = (!string.IsNullOrEmpty(processDefinitionKey)) ? "&processDefinitionKey=" + processDefinitionKey : null;
                    var uri = _configSettings.CamundaBaseAddress + "/task" + "?assignee=" + assignee + definitionKey;
                    var result = await client.GetAsync(uri);

                    result.EnsureSuccessStatusCode();

                    var resultString = await result.Content.ReadAsStringAsync();
                    var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                    await PopulateMemberDetails(tasksResourceList);

                    var mappedTasks = _mapper.Map<List<TaskListResource>>(tasksResourceList);

                    return mappedTasks;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for assignee " + assignee;
                // Don't pass the same DbContext instance to avoid concurrent access issues
                LogError(null, ex, message);
                throw new Exception(message);
            }
        }

        public async Task PopulateMemberDetails(List<TaskGetResource> tasksResourceList)
        {
            try
            {
                using var client = new HttpClient();
                foreach (var task in tasksResourceList)
                {
                    // TODO: Find better way to fetch and map camunda variables
                    var variablesUri = _configSettings.CamundaBaseAddress +
                                       "/variable-instance?processInstanceIdIn=" + task.ProcessInstanceId;
                    var variables = await client.GetAsync(variablesUri);

                    variables.EnsureSuccessStatusCode();

                    var variablesResultString = await variables.Content.ReadAsStringAsync();
                    JArray array = JArray.Parse(variablesResultString);
                    var found = false;

                    foreach (JObject content in array.Children<JObject>())
                    {
                        foreach (JProperty prop in content.Properties())
                        {
                            if (prop.Name == "type" && prop.First.Value<string>() == "Object")
                            {
                                content.Remove();
                                found = true;

                                break;
                            }
                        }

                        if (found)
                        {
                            break;
                        }
                    }

                    var variablesResultStringModified = array.ToString();
                    var variablesResourceList =
                        JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(
                            variablesResultStringModified);
                    var memberIDs = variablesResourceList.Where(i =>
                            (i.Type == "Integer" || i.Type == "Long") && (i.Name == "OrganisationID" ||
                                                                          i.Name == "MemberId" ||
                                                                          i.Name == "memberId"))
                        .ToList();

                    if (memberIDs.Count > 0)
                    {
                        task.MemberId = Convert.ToInt32(memberIDs[0].Value);
                    }

                    if (task.MemberId > 0)
                    {
                        var member = _dbContext.Members
                            .Include(i => i.StakeholderManager)
                            .FirstOrDefault(i => i.Id == task.MemberId);
                        if (member != null)
                        {
                            if (member.MembershipTypeId ==
                                Sacrra.Membership.Database.Enums.MembershipTypes.ALGClient)
                            {
                                var leader = _dbContext.ALGClientLeaders
                                    .Include(i => i.Leader)
                                    .FirstOrDefault(i => i.ClientId == member.Id);
                                if (leader != null)
                                {
                                    task.ALGLeader = leader.Leader.RegisteredName;
                                }

                            }

                            task.MembershipType = member.MembershipTypeId;
                            task.SacrraIndustryCategory = member.IndustryClassificationId;
                            task.StakeHolderManager = (member.StakeholderManager != null)
                                ? member.StakeholderManager.FullName
                                : null;
                            task.RegisteredName = member.RegisteredName;
                            task.Member = member.RegisteredName;
                        }
                    }

                    if (string.IsNullOrEmpty(task.RegisteredName) && task.MemberId > 0)
                    {
                        var member = await _dbContext.Set<Member>()
                            .Include(i => i.StakeholderManager)
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Id == task.MemberId);

                        task.RegisteredName = (member != null) ? member.RegisteredName : null;
                        task.StakeHolderManager = (member?.StakeholderManager != null)
                            ? member.StakeholderManager.FullName
                            : null;
                    }

                    var srnIDs = variablesResourceList
                        .Where(i => (i.Type == "Integer" || i.Type == "Long") && i.Name == "SRNId").ToList();

                    if (srnIDs.Count > 0)
                    {
                        task.SRNId = Convert.ToInt32(srnIDs[0].Value);

                        if (string.IsNullOrEmpty(task.RegisteredName))
                        {
                            var srn = await _dbContext.Set<SRN>()
                                .Include(i => i.Member)
                                .ThenInclude(x => x.StakeholderManager)
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnIDs[0].Value));

                            task.RegisteredName = (srn != null) ? srn.Member.RegisteredName : null;
                            task.StakeHolderManager = (srn.Member.StakeholderManager != null)
                                ? srn.Member.StakeholderManager.FullName
                                : null;

                        }
                    }

                    if (task.SRNId <= 0)
                    {
                        var srnId = 0;
                        var requestType = "";
                        var requestTypeVariable =
                            variablesResourceList.FirstOrDefault(i =>
                                i.Type == "String" && i.Name == "RequestType");

                        if (requestTypeVariable != null)
                        {
                            requestType = requestTypeVariable.Value;
                        }

                        if (!string.IsNullOrEmpty(requestType))
                        {
                            task.RequestType = requestType;

                            if (srnId > 0)
                            {
                                var srn = await _dbContext.SRNs
                                    .Include(i => i.Member)
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Id == srnId);

                                if (srn != null)
                                {
                                    if (srn.Member != null)
                                    {
                                        task.SRNId = srnId;
                                        task.MemberId = srn.MemberId;
                                        task.RegisteredName = srn.Member.RegisteredName;
                                        task.TradingName = srn.TradingName;
                                        task.SRNNumber = srn.SRNNumber;
                                    }
                                }
                            }
                        }
                    }
                    else if (task.SRNId > 0)
                    {
                        var srn = await _dbContext.SRNs
                            .Include(i => i.Member)
                            .Include(x => x.ALGLeader)
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Id == task.SRNId);


                        if (srn != null)
                        {
                            task.TradingName = srn.TradingName;
                            task.ALGLeader = srn.ALGLeader?.RegisteredName;
                            task.SRNNumber = srn.SRNNumber;

                            // TODO: Keep these 2 lines
                            task.IsPossibleTradingNameDuplicate = (task.Name == "SHM Reviews SRN(s) Application")
                                ? IsPossibleDuplicateTradingName(srn.Id, srn.TradingName)
                                : false;
                            task.FileType = variablesResourceList.Find(x => x.Name == "fileType")?.Value;

                            var srnUpdateTypeVariable =
                                variablesResourceList.FirstOrDefault(i =>
                                    i.Type == "Long" && i.Name == "SRNUpdateType");
                            if (srnUpdateTypeVariable != null)
                            {
                                task.SRNUpdateType = Convert.ToInt32(srnUpdateTypeVariable.Value);
                            }
                        }
                    }

                    var fileSubmissionRequestIdVariable =
                        variablesResourceList.Find(x => x.Name == "FileSubmissionRequestId");

                    if (fileSubmissionRequestIdVariable != null)
                    {
                        task.FileSubmissionRequestId = int.Parse(fileSubmissionRequestIdVariable.Value);
                    }

<<<<<<< HEAD
                    if(variablesResourceList.Any(x => x.Name.Contains("Amount Invoiced", StringComparison.OrdinalIgnoreCase)) && task.Name.Equals("Check if onboarding invoice paid"))
                    {
                        task.pastelOrAccountNumber = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Pastel/Account Number", StringComparison.CurrentCultureIgnoreCase)).Value?.ToString();
                        task.invoiceDate = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Invoice Date", StringComparison.CurrentCultureIgnoreCase)).Value?.ToString();
                        task.invoiceNumber = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Invoice Number", StringComparison.CurrentCultureIgnoreCase)).Value?.ToString();
                        task.invoiceAmount = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Amount Invoiced", StringComparison.CurrentCultureIgnoreCase)).Value?.ToString();
                        task.commentOrRefrence = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Comment/Refrence", StringComparison.CurrentCultureIgnoreCase)).Value?.ToString();
                    }
                    
=======
                    if (variablesResourceList.Any(x => x.Name.Contains("Amount Invoiced", StringComparison.OrdinalIgnoreCase)) &&
                        task.Name.Equals("Check if onboarding invoice paid") ||
                        task.Name.Equals("Check if assessement invoice paid") ||
                        task.Name.Equals("Check payment and acceptance of constitution review")
                        )
                    {
                        task.pastelOrAccountNumber = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Pastel/Account Number", StringComparison.CurrentCultureIgnoreCase))?.Value?.ToString() ?? string.Empty;
                        task.invoiceDate = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Invoice Date", StringComparison.CurrentCultureIgnoreCase))?.Value?.ToString() ?? string.Empty;
                        task.invoiceNumber = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Invoice Number", StringComparison.CurrentCultureIgnoreCase))?.Value?.ToString() ?? string.Empty;
                        task.invoiceAmount = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Amount Invoiced", StringComparison.CurrentCultureIgnoreCase))?.Value?.ToString() ?? string.Empty;
                        task.commentOrRefrence = variablesResourceList.SingleOrDefault(x => x.Name.Contains("Comment/Refrence", StringComparison.CurrentCultureIgnoreCase))?.Value?.ToString() ?? string.Empty;
                    }

>>>>>>> origin/SA-5053
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda tasks";
                // Don't pass the same DbContext instance to avoid concurrent access issues
                LogError(null, ex, message);
                throw new Exception(message);
            }
        }

        public int GetEntityTypeId(AppDbContext _dbContext, string name)
        {
            if (_dbContext == null || string.IsNullOrEmpty(name))
            {
                return 0;
            }
            else
            {
                var type = _dbContext.EntityTypes.FirstOrDefault(i => i.Name == name.Trim());
                if (type == null)
                    return 0;
                else
                    return type.Id;
            }
        }

        public string GetEnumValue(string propertyName, int id)
        {
            string propertyValue = string.Empty;

            switch (propertyName)
            {
                case "MembershipTypeId":
                    var entity = EnumHelper.GetEnumIdValuePair<MembershipTypes>(id);
                    if (entity != null)
                        propertyValue = entity.Value;
                    break;

                case "PrincipleDebtRangeId":
                    var vendor = EnumHelper.GetEnumIdValuePair<PrincipleDebtRanges>(id);
                    if (vendor != null)
                        propertyValue = vendor.Value;
                    break;

                case "IndustryClassificationId":
                    var type = EnumHelper.GetEnumIdValuePair<IndustryClassifications>(id);
                    if (type != null)
                        propertyValue = type.Value;
                    break;

                case "NcrReportingPrimaryBusinessClassificationId":
                    var typeClassification = EnumHelper.GetEnumIdValuePair<NcrReportingPrimaryBusinessClassifications>(id);
                    if (typeClassification != null)
                        propertyValue = typeClassification.Value;
                    break;

                case "FileType":
                    var fileType = EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>(id);
                    if (fileType != null)
                        propertyValue = fileType.Value;
                    break;
            }

            return propertyValue;
        }

        public bool IsPossibleDuplicateTradingName(int srnId, string tradingName)
        {
            var tradingNameExists = false;

            if (!string.IsNullOrEmpty(tradingName))
            {
                tradingName = tradingName.Trim();

                //Search for this trading name from the list of SRNs
                var srns = _dbContext.SRNs
                    .Where(i => i.TradingName == tradingName)
                    .Select(x => new SRN { TradingName = x.TradingName })
                    .AsQueryable();

                if (!srns.Any())
                {
                    return false;
                }
                else
                {
                    //Search for this trading name from other SRNs
                    var otherMemberSRNs = _dbContext.SRNs
                        .Where(x => x.Id != srnId)
                        .Select(x => new SRN { TradingName = x.TradingName })
                        .AsEnumerable();

                    if (otherMemberSRNs.Any())
                        tradingNameExists = otherMemberSRNs.Any(i => i.TradingName == tradingName);
                }
            }

            return tradingNameExists;
        }

        internal void PrepareSRNContactForUpdate(AppDbContext dbContext, SRNContact existingContact)
        {
            var attachedEntity = dbContext.ChangeTracker.Entries<SRNContact>().FirstOrDefault(e => e.Entity.Id == existingContact.Id);

            if (attachedEntity != null)
            {
                dbContext.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }
        }

        internal void PrepareSRNForUpdate(AppDbContext dbContext, SRN existingSrn)
        {
            var attachedEntity = dbContext.ChangeTracker.Entries<SRN>().FirstOrDefault(e => e.Entity.Id == existingSrn.Id);

            if (attachedEntity != null)
            {
                dbContext.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            dbContext.Entry(existingSrn).State = EntityState.Modified;
            dbContext.Entry(existingSrn).Property(o => o.MemberId).IsModified = true;
        }

        internal string GetPropertyGroup(PropertyInfo srnProp)
        {
            var groupName = "";
            var attributes = srnProp.GetCustomAttributes(typeof(DisplayAttribute), false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    groupName = attributes.Cast<DisplayAttribute>().Single().GroupName;
                }
            }

            return groupName;
        }

        internal string GetPropertyVaueById(AppDbContext dbContext, int id, string propertyName)
        {
            string propertyValue = string.Empty;

            switch (propertyName)
            {
                case "LoanManagementSystemVendorId":
                    var entity = dbContext.LoanManagementSystemVendors.FirstOrDefault(i => i.Id == id);
                    if (entity != null)
                        propertyValue = entity.Name;
                    break;

                case "SoftwareVendorId":
                    var vendor = dbContext.SoftwareVendors.FirstOrDefault(i => i.Id == id);
                    if (vendor != null)
                        propertyValue = vendor.Name;
                    break;

                case "AccountTypeId":
                    var type = dbContext.AccountTypes.FirstOrDefault(i => i.Id == id);
                    if (type != null)
                        propertyValue = type.Name;
                    break;

                case "NCRReportingAccountTypeClassificationId":
                    var typeClassification = dbContext.NCRReportingAccountTypeClassifications.FirstOrDefault(i => i.Id == id);
                    if (typeClassification != null)
                        propertyValue = typeClassification.Name;
                    break;

                case "SPGroupId":
                    var spGroup = dbContext.SPGroups.FirstOrDefault(i => i.Id == id);
                    if (spGroup != null)
                        propertyValue = spGroup.SPNumber;
                    break;
                case "SRNStatusId":
                    var srnStatus = dbContext.SRNStatuses.FirstOrDefault(i => i.Id == id);
                    if (srnStatus != null)
                        propertyValue = srnStatus.Name;
                    break;

                case "CreditInformationClassificationId":
                    var creditInformationClassification = dbContext.CreditInformationClassifications.FirstOrDefault(i => i.Id == id);
                    if (creditInformationClassification != null)
                        propertyValue = creditInformationClassification.Name;
                    break;
            }

            return propertyValue;
        }

        internal void CreateEventLog(AppDbContext dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var userFullName = "";

            if (userId > 0)
            {
                var user = dbContext.Users.FirstOrDefault(i => i.Id == userId);

                if (user != null)
                    userFullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                userFullName = "Internal System";
            }


            var entityTypeId = GetEntityTypeId(dbContext, entityTypeName);

            if (entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                dbContext.Add(eventLog);
                dbContext.SaveChanges();
            }
        }

        public async Task<TaskGetResource> GetTaskAsync(string id)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + id;
                    var result = await client.GetAsync(uri);
                    result.EnsureSuccessStatusCode();

                    var resultString = await result.Content.ReadAsStringAsync();
                    var taskResource = JsonConvert.DeserializeObject<TaskGetResource>(resultString);

                    return taskResource;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda task with id " + id;
                // Don't pass the same DbContext instance to avoid concurrent access issues
                LogError(null, ex, message);
                throw new Exception(message);
            }
        }

        public TaskGetResource GetTask(string id)
        {
            try
            {
                var client = new RestClient();
                var uri = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + id);
                var result = client.Get(uri);
                var taskResource = JsonConvert.DeserializeObject<TaskGetResource>(result.Content);

                return taskResource;
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda task with id " + id;
                // Don't pass the same DbContext instance to avoid concurrent access issues
                LogError(null, ex, message);
                throw new Exception(message);
            }
        }

        public async Task<List<VariableInstanceGetResource>> GetVariablesAsync(string processInstanceId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                    var result = await client.GetAsync(uri);
                    result.EnsureSuccessStatusCode();

                    var resultString = await result.Content.ReadAsStringAsync();
                    var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString);

                    return variablesResourceList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve variables for process id " + processInstanceId;
                // Don't pass the same DbContext instance to avoid concurrent access issues
                LogError(null, ex, message);
                throw new Exception(message);
            }
        }

        public List<VariableInstanceGetResource> GetVariables(string processInstanceId)
        {
            try
            {
                var client = new RestClient();
                var uri = new RestRequest(_configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId);
                var result = client.Get(uri);
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(result.Content);

                return variablesResourceList;
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve variables for process id " + processInstanceId;
                LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void GetDefaultValue<T>(T objEntity)
        {
            var type = objEntity.GetType();
            IList<PropertyInfo> properties = new List<PropertyInfo>(type.GetProperties());
            foreach (var prop in properties)
            {
                var propValue = prop.GetValue(objEntity, null);

                if (propValue == null)
                {
                    if (prop.PropertyType.IsClass && prop.PropertyType != typeof(string) && prop.PropertyType != typeof(List<IdValuePairResource>))
                    {
                        var subClass = CreateInstance(prop);

                        foreach (var subProp in subClass.GetType().GetProperties())
                        {
                            if (subProp.PropertyType.IsPrimitive)
                            {
                                subProp.SetValue(subClass, 0);
                            }
                            else if (subProp.PropertyType == (typeof(string)))
                            {
                                subProp.SetValue(subClass, "N/A");
                            }
                        }

                        prop.SetValue(objEntity, subClass);
                    }
                    else if (prop.PropertyType.IsPrimitive || prop.PropertyType.IsEnum)
                    {
                        prop.SetValue(objEntity, 0);
                    }
                    else if (prop.PropertyType == (typeof(string)))
                    {
                        prop.SetValue(objEntity, null);
                    }
                    else if (prop.PropertyType == typeof(int?))
                    {
                        prop.SetValue(objEntity, 0);
                    }
                    else if (prop.PropertyType == typeof(bool?))
                    {
                        prop.SetValue(objEntity, false);
                    }
                    else if (prop.PropertyType == typeof(List<IdValuePairResource>))
                    {
                        prop.SetValue(objEntity, new List<IdValuePairResource>());
                    }
                    else if (prop.PropertyType == typeof(DateTime?))
                    {
                        prop.SetValue(objEntity, null);
                    }
                    else if (prop.PropertyType == typeof(IdValuePairResource))
                    {
                        prop.SetValue(objEntity, new IdValuePairResource { Id = 0, Value = "N/A" });
                    }
                }
            }
        }

        private object CreateInstance(PropertyInfo entity)
        {
            return Activator.CreateInstance(entity.PropertyType);
        }
        public RestClient GetRestClient(string baseApiUrl)
        {
            RestClientOptions options = new($"{baseApiUrl}/api");
            // disable SSL errors
            options.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            var restClient = new RestClient(options);
            return restClient;
        }
        public void CheckForAPIRequestError(RestResponse restRequest)
        {
            if (restRequest.StatusCode != System.Net.HttpStatusCode.OK)
            {
                // If the API throws an exception or we cannot access the API
                if (restRequest.ErrorException != null)
                    throw restRequest.ErrorException;

                // Otherwise return the error we get back from the API
                var message = restRequest.Content;
                var ex = new Exception(message);
                throw ex;
            }
        }

        public string GetPropertyAssociationName(PropertyInfo prop)
        {
            var associationName = prop.Name;
            var attributes = prop.GetCustomAttributes(typeof(DBAssociation),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    associationName = attributes.Cast<DBAssociation>().Single().Name;
                }
            }

            return associationName;
        }


        public SRNStatusGetResource GetByName(string name)
        {
            var selectRecord = _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefault(s => s.Name.ToLower() == name.Trim().ToLower());

            var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

            return returnRecord;
        }
    }
}
