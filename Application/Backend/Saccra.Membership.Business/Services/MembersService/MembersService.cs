using AutoMapper;
using Dapper;
using Hangfire;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database.Views;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using System.Security.Claims;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.ALGLeadersDTO;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.DTOs.PaginationDTOs;

namespace Sacrra.Membership.Business.Services.MembersService
{
    public class MembersService
    {
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;
        private readonly MemberServiceHelper _memberServiceHelper;
        private readonly CamundaRepository _camundaRepository;
        public IMapper _mapper { get; }
        private LookupsRepository _lookupsRepo;
        private readonly IConfiguration _config;

        public MembersService(AppDbContext dbContext, GlobalHelper globalHelper, IMapper mapper, MemberServiceHelper memberServiceHelper,
            CamundaRepository camundaRepository, LookupsRepository lookupsRepo, IConfiguration config)
        {
            _dbContext = dbContext;
            _globalHelper = globalHelper;
            _mapper = mapper;
            _memberServiceHelper = memberServiceHelper;
            _camundaRepository = camundaRepository;
            _lookupsRepo = lookupsRepo;
            _config = config;
        }

        public IEnumerable<MemberMyInfoOutputDTO> GetMyInformation(User user)
        {
            if (user == null)
                throw new InvalidUserException();

            if (user.Id > 0)
            {
                var activeStatuses = _memberServiceHelper.GetActiveSRNStatuses();
                var members = _dbContext.Set<Member>().AsNoTracking().AsQueryable();

                if (!_globalHelper.IsInternalSACRRAUser(user))
                {
                    if (user.RoleId == UserRoles.ALGLeader)
                    {
                        members = members.Where(x => x.MembershipTypeId == MembershipTypes.ALGLeader
                            && x.Users.Any(i => i.UserId == user.Id))
                          .Select(member => new Member
                          {
                              Id = member.Id,
                              RegisteredName = member.RegisteredName,
                              RegisteredNumber = member.RegisteredNumber,
                              IdNumber = member.IdNumber,
                              MembershipTypeId = member.MembershipTypeId,
                              ApplicationStatusId = member.ApplicationStatusId,
                              StakeholderManager = new User
                              {
                                  FirstName = member.StakeholderManager.FirstName,
                                  LastName = member.StakeholderManager.LastName
                              },
                          }).AsQueryable();
                    }
                    else if (user.RoleId == UserRoles.Member)
                    {
                        members = members.Where(x => (x.MembershipTypeId == MembershipTypes.FullMember || x.MembershipTypeId == MembershipTypes.NonMember)
                            && x.Users.Any(i => i.UserId == user.Id))
                         .Select(member => new Member
                         {
                             Id = member.Id,
                             RegisteredName = member.RegisteredName,
                             RegisteredNumber = member.RegisteredNumber,
                             IdNumber = member.IdNumber,
                             MembershipTypeId = member.MembershipTypeId,
                             ApplicationStatusId = member.ApplicationStatusId,
                             StakeholderManager = new User
                             {
                                 FirstName = member.StakeholderManager.FirstName,
                                 LastName = member.StakeholderManager.LastName
                             },
                         }).AsQueryable();
                    }
                    else if (user.RoleId == UserRoles.Bureau)
                    {
                        members = members.Where(x => (x.MembershipTypeId == MembershipTypes.Bureau)
                            && x.Users.Any(i => i.UserId == user.Id))
                         .Select(member => new Member
                         {
                             Id = member.Id,
                             RegisteredName = member.RegisteredName,
                             RegisteredNumber = member.RegisteredNumber,
                             IdNumber = member.IdNumber,
                             MembershipTypeId = member.MembershipTypeId,
                             ApplicationStatusId = member.ApplicationStatusId,
                             StakeholderManager = new User
                             {
                                 FirstName = member.StakeholderManager.FirstName,
                                 LastName = member.StakeholderManager.LastName
                             },
                         }).AsQueryable();
                    }
                    else
                    {
                        return new List<MemberMyInfoOutputDTO>();
                    }
                }
                else if (_globalHelper.IsInternalSACRRAUser(user))
                {
                    members = members
                        .Include(x => x.StakeholderManager)
                            .Select(member => new Member
                            {
                                Id = member.Id,
                                RegisteredName = member.RegisteredName,
                                RegisteredNumber = member.RegisteredNumber,
                                IdNumber = member.IdNumber,
                                MembershipTypeId = member.MembershipTypeId,
                                ApplicationStatusId = member.ApplicationStatusId,
                                StakeholderManager = new User
                                {
                                    FirstName = member.StakeholderManager.FirstName,
                                    LastName = member.StakeholderManager.LastName
                                },
                            }).AsQueryable();
                }

                return _mapper.Map<List<MemberMyInfoOutputDTO>>(members.ToList()).AsEnumerable();
            }

            return null;
        }

        public List<IdValuePairResource> GetMemberList(ApplicationStatuses? status, User user)
        {
            var query = _dbContext.Set<Member>()
                .AsNoTracking()
                .Include(x => x.Users)
                .AsQueryable();

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                query = query.Where(i => i.Users.Any(x => x.UserId == user.Id));
            }

            if (status != null)
                query = query.Where(i => i.ApplicationStatusId == status);

            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(query).ToList();

            return itemsToReturn;
        }

        public IEnumerable<MemberMyInfoOutputDTO> GetMemberListWithSHM(User user)
        {
            if (user == null)
                throw new InvalidUserException();

            if (user.Id <= 0)
                return null;

            var members = _dbContext.Set<Member>()
                .AsNoTracking()
                .Include(m => m.StakeholderManager) // include stakeholder manager navigation
                .Select(member => new Member
                {
                    Id = member.Id,
                    RegisteredName = member.RegisteredName,
                    RegisteredNumber = member.RegisteredNumber,
                    IdNumber = member.IdNumber,
                    MembershipTypeId = member.MembershipTypeId,
                    ApplicationStatusId = member.ApplicationStatusId,
                    StakeholderManager = new User
                    {
                        FirstName = member.StakeholderManager.FirstName,
                        LastName = member.StakeholderManager.LastName
                    }
                })
                .ToList();

            return _mapper.Map<List<MemberMyInfoOutputDTO>>(members);


        }
        public List<string> GetAllCompanyRegistrationNumbers()
        {
            var regNumbers = new List<string>();

            regNumbers = _dbContext.Members
                .Where(i => !string.IsNullOrEmpty(i.RegisteredNumber))
                .Select(i => i.RegisteredNumber).ToList();

            return regNumbers;
        }

        public List<ALGLeaderOutputDTO> GetALGLeaders(User user)
        {
            var members = _dbContext.Members
            .Include(i => i.Users)
            .AsNoTracking()
            .Where(i => i.MembershipTypeId == MembershipTypes.ALGLeader)
            .ToList();

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                members = members.Where(x => x.Users.Any(i => i.UserId == user.Id)).ToList();
            }

            var algLeaders = _mapper.Map<List<ALGLeaderOutputDTO>>(members);
            return algLeaders;
        }

        public MemberUpdateMessageOutputDTO UpdateMember(MemberUpdateInputDTO memberUpdateInputDTO, User user)
        {
            var member = _dbContext.Set<Member>()
                .Include(i => i.TradingNames)
                .Include(i => i.Contacts)
                .Include(i => i.Users)
                .Include(i => i.Domains)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == memberUpdateInputDTO.Id);

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (!member.Users.Any(x => x.UserId == user.Id))
                    throw new UnauthorizedException();
            }

            bool isApprovalRequired = false;

            if (memberUpdateInputDTO.IsNcrRegistrant != false && memberUpdateInputDTO.NcrcpNumber != null)
            {
                memberUpdateInputDTO.NcrcpNumber = "NCRCP" + memberUpdateInputDTO.NcrcpNumber.ToString();
            }
            else
            {
                memberUpdateInputDTO.NcrcpNumber = "";
            }


            UserGetResource currentUser = _mapper.Map<UserGetResource>(user);

            if (currentUser != null)
            {
                if (currentUser.RoleId == UserRoles.Member || currentUser.RoleId == UserRoles.ALGLeader)
                {
                    var changerequestId = 0;

                    if (_memberServiceHelper.DoMemberChangesRequireApproval(member, memberUpdateInputDTO))
                    {
                        isApprovalRequired = true;
                        changerequestId = _memberServiceHelper.CreateMemberChangeRequest(member, memberUpdateInputDTO, user.Id);
                    }
                    else
                    {
                        _memberServiceHelper.ApplyMemberChanges(_dbContext, member, _mapper, memberUpdateInputDTO, user);
                    }

                    _camundaRepository.StartMemberUpdateWorkflow(member, isApprovalRequired, changerequestId).Wait();
                }
                else if (currentUser.RoleId == UserRoles.SACRRAAdministrator
                    || currentUser.RoleId == UserRoles.StakeHolderAdministrator
                    || currentUser.RoleId == UserRoles.StakeHolderManager)
                {
                    _memberServiceHelper.ApplyMemberChanges(_dbContext, member, _mapper, memberUpdateInputDTO, user);
                }
            }

            return new MemberUpdateMessageOutputDTO { IsApprovalRequired = isApprovalRequired };
        }

        public void MemberSrnsActive(int memberId)
        {
            var snrsActive = _dbContext.SRNs.Any(i => i.MemberId == memberId && i.SRNStatusId != 19);

            if (snrsActive)
            {
                throw new MemberSrnsActive();
            }
        }

        public void RequestMember(MemberRequestInputDTO memberForCreate, User user)
        {
            if (_memberServiceHelper.DoesMemberExist(memberForCreate))
            {
                throw new MemberExistsException();
            }
            if (_memberServiceHelper.DoesVATNumberExist(memberForCreate))
            {
                throw new VATNumberExistsException();
            }

            if (memberForCreate.NCRCPNumber != null && memberForCreate.NCRCPNumber != "" && memberForCreate.IsNCRRegistrant == true)
            {
                memberForCreate.NCRCPNumber = "NCRCP" + memberForCreate.NCRCPNumber;
            }
            else
            {
                memberForCreate.NCRCPNumber = null;
            }

            memberForCreate.HeadOfficePostalAddress = memberForCreate.isSameAddresses ? memberForCreate.HeadOfficePhysicalAddress : memberForCreate.HeadOfficePostalAddress;


            var userId = user.Id;
            var model = new Member();

            model = _mapper.Map<Member>(memberForCreate);

            if (model.PrincipleDebtRangeId <= 0)
                model.PrincipleDebtRangeId = null;
            if (model.PrimaryBureauId <= 0)
                model.PrimaryBureauId = null;

            string IDDocument = null;
            string ncrCertificateJson = null;
            string AuditedFinancialDocument = null;

            if (memberForCreate.IdentificationDocument != null)
            {
                IDDocument = JsonConvert.SerializeObject(memberForCreate.IdentificationDocument);
            }

            if (memberForCreate.NCRCertificateDocument != null)
            {
                ncrCertificateJson = JsonConvert.SerializeObject(memberForCreate.NCRCertificateDocument);
            }

            if (memberForCreate.AuditedFinancialDocument != null)
            {
                AuditedFinancialDocument = JsonConvert.SerializeObject(memberForCreate.AuditedFinancialDocument);
            }

            var memberGetResource = new MemberGetResource();

            model.ApplicationStatusId = ApplicationStatuses.MemberRegistrationSubmitted;
            model.DateCreated = DateTime.Now;
            model.RequireSRNTesting = true;

            UserGetResource currentUser = _mapper.Map<UserGetResource>(user);

            Member algLeader = null;
            if (model.MembershipTypeId == MembershipTypes.ALGClient && !_globalHelper.IsInternalSACRRAUser(user))
            {
                if (memberForCreate.ALGLeaderIds == null)
                    algLeader = _memberServiceHelper.GetALGLeaderIdByUser(user.Id);
                else if (memberForCreate.ALGLeaderIds.Count <= 0)
                    algLeader = _memberServiceHelper.GetALGLeaderIdByUser(user.Id);
                else
                {
                    algLeader = _dbContext.Members
                        .AsNoTracking()
                        .FirstOrDefault(i => i.Id == memberForCreate.ALGLeaderIds[0].Id);
                }
            }

            if (currentUser != null)
            {
                if (currentUser.RoleId == UserRoles.SACRRAAdministrator
                    || currentUser.RoleId == UserRoles.StakeHolderAdministrator
                    || currentUser.RoleId == UserRoles.StakeHolderManager)
                {
                    if (model.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        if (memberForCreate.ALGLeaderIds != null)
                        {
                            if (memberForCreate.ALGLeaderIds.Count > 0)
                            {
                                var leader = _dbContext.Members
                                    .AsNoTracking()
                                    .FirstOrDefault(i => i.Id == memberForCreate.ALGLeaderIds[0].Id);

                                if (leader != null)
                                    model.StakeholderManagerId = leader.StakeholderManagerId;
                            }
                            else
                            {
                                throw new NoALGLeaderException();
                            }
                        }
                        else
                        {
                            throw new NoALGLeaderException();
                        }
                    }
                    else
                    {
                        model.StakeholderManagerId = currentUser.Id;
                    }
                }
                else if (currentUser.RoleId == UserRoles.ALGLeader)
                {
                    if (model.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        if (algLeader != null)
                            model.StakeholderManagerId = algLeader.StakeholderManagerId;
                        else
                            throw new NoALGLeaderException();
                    }
                }
            }

            if (memberForCreate.PrincipleDebtRangeId != null && memberForCreate.NCRFeeCategoryId != null)
            {
                switch (memberForCreate.NCRFeeCategoryId)
                {
                    case 1:
                        if (memberForCreate.PrincipleDebtRangeId != 1)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 2:
                        if (memberForCreate.PrincipleDebtRangeId != 2)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 3:
                        if (memberForCreate.PrincipleDebtRangeId != 3)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;

                    case 4:
                        if (memberForCreate.PrincipleDebtRangeId != 4)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 5:
                        if (memberForCreate.PrincipleDebtRangeId != 5)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 6:
                        if (memberForCreate.PrincipleDebtRangeId != 6)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 7:
                        if (memberForCreate.PrincipleDebtRangeId != 7)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 8:
                        if (memberForCreate.PrincipleDebtRangeId != 8)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                    case 9:
                        if (memberForCreate.PrincipleDebtRangeId != 9)
                        {
                            throw new NCRCategoryException();
                        }
                        model.NcrCategory = EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)memberForCreate.NCRFeeCategoryId).Value;
                        break;
                }
            }


            if (!string.IsNullOrEmpty(IDDocument) || !string.IsNullOrEmpty(ncrCertificateJson) || !string.IsNullOrEmpty(AuditedFinancialDocument))
            {
                model.MemberDocument = new MemberDocument
                {
                    MemberId = model.Id,
                    IDDocumentBlob = IDDocument,
                    NcrCertificateBlob = ncrCertificateJson,
                    AuditedFinancialBlob = AuditedFinancialDocument
                };
            }

            if (model.IsSoleProp)
                model.RegisteredNumber = null;
            else
                model.IdNumber = null;

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                //Link user to member
                model.Users.Add(new MemberUsers
                {
                    MemberId = model.Id,
                    UserId = user.Id,
                    DateCreated = DateTime.Now
                });
            }

            _memberServiceHelper.UpdatePartialMember(userId, memberForCreate);

            //Assign ALG Leaders to an ALG Client
            if (memberForCreate.MembershipTypeId == (int)MembershipTypes.ALGClient)
            {
                if (_globalHelper.IsInternalSACRRAUser(user))
                {
                    if (memberForCreate.ALGLeaderIds != null)
                    {
                        if (memberForCreate.ALGLeaderIds.Count > 0)
                        {
                            foreach (var leader in memberForCreate.ALGLeaderIds)
                            {
                                if (leader.Id > 0)
                                {
                                    model.Leaders.Add(new ALGClientLeader
                                    {
                                        ClientId = model.Id,
                                        LeaderId = leader.Id,
                                        DateCreated = DateTime.Now

                                    });

                                    //Link other ALG Leader users to this client
                                    var algLeaderUsers = _dbContext.Set<MemberUsers>()
                                        .Where(i => i.MemberId == leader.Id && i.UserId != user.Id)
                                        .ToList();

                                    if (algLeaderUsers != null)
                                    {
                                        foreach (var algUser in algLeaderUsers)
                                        {
                                            model.Users.Add(new MemberUsers
                                            {
                                                MemberId = model.Id,
                                                UserId = algUser.UserId,
                                                DateCreated = DateTime.Now
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    throw new NoALGLeaderException();
                                }
                            }
                        }
                        else
                        {
                            throw new NoALGLeaderException();
                        }
                    }
                }

                else if (user.RoleId == UserRoles.ALGLeader)
                {
                    if (algLeader != null)
                    {
                        model.Leaders.Add(new ALGClientLeader
                        {
                            ClientId = model.Id,
                            LeaderId = algLeader.Id,
                            DateCreated = DateTime.Now

                        });

                        //Link other ALG Leader users to this client
                        var algLeaderUsers = _dbContext.Set<MemberUsers>()
                            .Where(i => i.MemberId == algLeader.Id && i.UserId != user.Id)
                            .ToList();

                        if (algLeaderUsers != null)
                        {
                            foreach (var algUser in algLeaderUsers)
                            {
                                model.Users.Add(new MemberUsers
                                {
                                    MemberId = model.Id,
                                    UserId = algUser.UserId,
                                    DateCreated = DateTime.Now
                                });
                            }
                        }
                    }
                }
                else
                {
                    throw new NoALGLeaderException();
                }
            }

            string processInstanceId = string.Empty;

            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                //We're using the try...catch block in order to DELETE the camunda task that would
                //have been created if DB transaction fails
                try
                {
                    model.RegisteredName = (!string.IsNullOrWhiteSpace(model.RegisteredName)) ? model.RegisteredName.Trim() : model.RegisteredName;
                    model.RegisteredNumber = (!string.IsNullOrWhiteSpace(model.RegisteredNumber)) ? model.RegisteredNumber.Trim() : model.RegisteredNumber;

                    _dbContext.Members.Add(model);
                    _dbContext.SaveChanges();

                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    _memberServiceHelper.CreateEventLog(memberForCreate, model, stagingChangeLog);

                    var entityBlob = JsonConvert.SerializeObject(memberForCreate);
                    var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    _memberServiceHelper.CreateEventLog(_dbContext, user.Id, "Member Create", memberForCreate.CompanyRegisteredName, entityBlob, stagingBlob, model.Id, "Member");

                    if (model.MembershipTypeId == MembershipTypes.FullMember
                        || model.MembershipTypeId == MembershipTypes.NonMember
                        || model.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        if (model.MembershipTypeId == MembershipTypes.ALGClient)
                        {
                            if (algLeader == null && user.RoleId == UserRoles.ALGLeader)
                            {
                                throw new NoALGLeaderException();
                            }
                        }

                        processInstanceId = _memberServiceHelper.AddMemberRegistrationTask(model, false);
                    }

                    transaction.Commit();
                }
                catch (Exception exception)
                {
                    if (!string.IsNullOrWhiteSpace(processInstanceId))
                    {
                        _camundaRepository.DeleteProcessInstance(processInstanceId);
                    }
                    throw new MemberCreationException();
                }
            }

        }

        public object ListALGClients(PaginationInputDTO paginationData, User user, ApplicationStatuses? status = null)
        {

            if (user != null)
            {
                var activeStatuses = GetActiveSRNStatuses();
                var algLeader = _dbContext.ALGClientLeaders
                    .Include(i => i.Leader)
                    .FirstOrDefault(i => i.Leader.Users.Any(x => x.UserId == user.Id)
                        && i.Leader.MembershipTypeId == MembershipTypes.ALGLeader);

                if (algLeader != null)
                {
                    var paginationRange = new Range(paginationData.PageNumber, paginationData.Rows + paginationData.PageNumber);
                    var queryTotalRecords = _dbContext.Set<ALGClientLeader>().Where(i => i.Client.StakeholderManagerId > 0 && i.LeaderId == algLeader.LeaderId).Count();
                    IQueryable<ALGClientLeader> query = _dbContext.Set<ALGClientLeader>()
                    .Include(i => i.Client)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Client)
                        .ThenInclude(i => i.SRNs)
                        .ThenInclude(i => i.SRNStatus)
                    .Where(i => i.Client.StakeholderManagerId > 0
                        && i.LeaderId == algLeader.LeaderId)
                    .Select(m => new ALGClientLeader
                    {
                        Id = m.Id,
                        ClientId = m.ClientId,
                        Client = new Member
                        {
                            Id = m.Client.Id,
                            DateActivated = m.Client.DateActivated,
                            RegisteredName = m.Client.RegisteredName,
                            RegisteredNumber = m.Client.RegisteredNumber,
                            IdNumber = m.Client.IdNumber,
                            ApplicationStatusId = m.Client.ApplicationStatusId,
                            MembershipTypeId = m.Client.MembershipTypeId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Client.StakeholderManager.FirstName,
                                LastName = m.Client.StakeholderManager.LastName
                            },
                            TotalActiveSRNs = m.Client.SRNs.Count(x => activeStatuses.Contains(x.SRNStatusId)),
                            TotalSRNs = m.Client.SRNs.Count()
                        }
                    })
                    .OrderBy(x => x.Client.RegisteredName);

                    MyClientsFilters filters = JsonConvert.DeserializeObject<MyClientsFilters>(paginationData.Filters.ToString());

                    query.AsQueryable();

                    if (query != null)
                    {
                        if (status != null)
                            query = query.Where(i => i.Client.ApplicationStatusId == status);

                        if (paginationData.Rows > 0)
                        {
                            var clients = _mapper.Map<IEnumerable<ALGClientGetResource>>(query)
                                .Skip((paginationData.PageNumber - 1) * paginationData.Rows)
                                .Take(paginationData.Rows)
                                .ToList();
                            var mapClients = _mapper.Map<IEnumerable<AlgLeaderClientOutputDTO>>(clients).ToList();

                            return new { totalRecords = queryTotalRecords, clients = mapClients };
                        }
                        else
                        {
                            var clients = _mapper.Map<IEnumerable<ALGClientGetResource>>(query).ToList();
                            var mapClients = _mapper.Map<IEnumerable<AlgLeaderClientOutputDTO>>(clients).ToList();

                            return mapClients;
                        }
                    }
                    return new List<AlgLeaderClientOutputDTO>();
                }

                return new List<AlgLeaderClientOutputDTO>();
            }
            return new List<AlgLeaderClientOutputDTO>();
        }

        public DTOs.PagedList<AlgLeaderClientOutputDTO> ListALGClients_V2(PaginationInputDTO paginationData, User user, ApplicationStatuses? status = null)
        {

            if (user != null)
            {
                var algLeader = _dbContext.ALGClientLeaders
                    .Include(i => i.Leader)
                    .FirstOrDefault(i => i.Leader.Users.Any(x => x.UserId == user.Id)
                        && i.Leader.MembershipTypeId == MembershipTypes.ALGLeader);

                if (algLeader != null)
                {
                    var query = _dbContext.Set<ALGClientLeader>()
                    .Include(i => i.Client)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Client)
                        .ThenInclude(i => i.SRNs)
                        .ThenInclude(i => i.SRNStatus)
                    .Where(i => i.Client.StakeholderManagerId > 0
                        && i.LeaderId == algLeader.LeaderId)
                    .Select(m => new ALGClientLeader
                    {
                        Id = m.Id,
                        ClientId = m.ClientId,
                        Client = new Member
                        {
                            Id = m.Client.Id,
                            DateActivated = m.Client.DateActivated,
                            RegisteredName = m.Client.RegisteredName,
                            RegisteredNumber = m.Client.RegisteredNumber,
                            IdNumber = m.Client.IdNumber,
                            ApplicationStatusId = m.Client.ApplicationStatusId,
                            MembershipTypeId = m.Client.MembershipTypeId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Client.StakeholderManager.FirstName,
                                LastName = m.Client.StakeholderManager.LastName
                            }
                        }
                    })
                    .AsQueryable();

                    //If no sorting specified, sort data by RegisteredName
                    if (string.IsNullOrWhiteSpace(paginationData.SortField))
                        query = query.OrderBy(i => i.Client.RegisteredName);
                    else
                    {
                        query = SortALGClients(query, paginationData.SortField, paginationData.SortOrder);
                    }

                    MyClientsFilters filters = JsonConvert.DeserializeObject<MyClientsFilters>(paginationData.Filters.ToString());

                    query = FilterALGClients(query, filters);

                    if (query != null)
                    {
                        if (status != null)
                            query = query.Where(i => i.Client.ApplicationStatusId == status);

                        var count = query.Count();

                        var pagesToSkip = paginationData.PageNumber == 1 ? 0 : paginationData.PageNumber;

                        var items = query
                            .Skip(pagesToSkip)
                            .Take(paginationData.Rows)
                            .ToList();

                        var clients = _mapper.Map<List<ALGClientGetResource>>(items);

                        var mapClients = _mapper.Map<List<AlgLeaderClientOutputDTO>>(clients);

                        return new DTOs.PagedList<AlgLeaderClientOutputDTO>(mapClients, count, paginationData.PageNumber, paginationData.Rows);
                    }
                }
            }

            return new DTOs.PagedList<AlgLeaderClientOutputDTO>(new List<AlgLeaderClientOutputDTO>(), 0, paginationData.PageNumber, paginationData.Rows);
        }

        private int[] GetActiveSRNStatuses()
        {
            var activeStatuses = _dbContext.SRNStatuses
                .Where(i => i.IsActive)
                .Select(m => m.Id)
                .ToArray();

            return activeStatuses;
        }

        public MemberOutputDTO GetMember(int id, User user)
        {
            var selectRecord = _dbContext.Members
                     .Include(i => i.PrimaryBureau)
                     .Include(i => i.SecondaryBureau)
                     .Include(i => i.TradingNames)
                     .Include(i => i.Users)
                        .ThenInclude(i => i.User)
                     .Include(i => i.StakeholderManager)
                     .Include(i => i.Contacts)
                         .ThenInclude(i => i.ContactType)
                     .Include(i => i.MemberDocument)
                     .Include(i => i.MemberStatusReason)
                     .Include(i => i.Domains)
                     .AsNoTracking()
                     .FirstOrDefault(s => s.Id == id);

            if (selectRecord == null)
            {
                return null;
            }

            if (selectRecord.Id <= 0)
            {
                return null;
            }

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (!selectRecord.Users.Any(i => i.UserId == user.Id))
                {
                    throw new UnauthorizedException();
                }
            }

            // Using regex to only retrieve the NCRCPNumber without the letters behind it
            if (selectRecord.NcrcpNumber != "NCRCPundefined" && selectRecord.NcrcpNumber != null && selectRecord.IsNcrRegistrant == true)
            {
                Regex re = new Regex(@"([a-zA-Z]+)(\d+)");
                Match result = re.Match(selectRecord.NcrcpNumber);
                selectRecord.NcrcpNumber = result.Groups[2].Value;
            }
            else
            {
                selectRecord.NcrcpNumber = null;
            }

            var returnRecord = _mapper.Map<MemberOutputDTO>(selectRecord);
            var changeStatus = _dbContext.MemberChangeRequests.FirstOrDefault(m => m.Type == ChangeObjectType.Member && m.ObjectId == id);

            returnRecord.ChangeRequestStatus = (changeStatus != null) ? changeStatus.Status.ToString() : "No Change Request";
            returnRecord.ApplicationStatus = (returnRecord.ApplicationStatus != null) ? _lookupsRepo.GetEnumIdValuePair<ApplicationStatuses>(returnRecord.ApplicationStatus.Id) : null;
            returnRecord.MemberStatus = (returnRecord.ApplicationStatus != null) ? _lookupsRepo.GetEnumIdValuePair<ApplicationStatuses>(returnRecord.ApplicationStatus.Id).Value : null;

            if (selectRecord.MembershipTypeId == MembershipTypes.ALGClient)
            {
                var clientLeaders = _dbContext.ALGClientLeaders
                    .Include(i => i.Leader)
                    .AsNoTracking()
                    .Where(i => i.ClientId == id)
                    .ToList();

                if (clientLeaders != null)
                {
                    foreach (var leader in clientLeaders)
                    {
                        returnRecord.ALGLeaders.Add(new ALGLeaderOutputDTO
                        {
                            Id = leader.LeaderId,
                            Value = leader.Leader.RegisteredName
                        });
                    }
                }
            }

            return returnRecord;
        }

        public IEnumerable<Member> GetAllMembersForFreshdesk()
        {
            var excludedDomains = _dbContext.FreshdeskExcludedDomains
                .AsQueryable();

            var members = _dbContext.Set<Member>()
                .AsNoTracking()
                .Include(i => i.StakeholderManager)
                .Include(i => i.Domains)
                .Where(i => i.MembershipTypeId != MembershipTypes.ALGClient)
                .Select(m => new Member()
                {
                    Id = m.Id,
                    RegisteredName = m.RegisteredName,
                    StakeholderManager = new()
                    {
                        Email = m.StakeholderManager.Email
                    },
                    Domains = m.Domains.Select(i => new MemberDomain
                    {
                        Name = i.Name,
                    }).Where(i => !excludedDomains.Select(y => y.Name).Contains(i.Name)) /* exclude some domains */
                    .ToList()
                })
                .AsEnumerable();

            return members;
        }
        public IEnumerable<FreshdeskMemberContactDTO> GetAllMemberContactsForFreshdesk()
        {
            using (var conn = _dbContext.Database.GetDbConnection())
            {
                var sql = @"SELECT
                        [Email],
                        [Id], 
                        [FirstName], 
                        [Surname], 
                        [OfficeTelNumber], 
                        [CellNumber], 
                        [JobTitle],
                        [Name] AS [ContactType], 
                        [MemberId], 
                        [RegisteredName],
                        [HeadOfficePhysicalAddress]
                        FROM
                        ( 
                        SELECT ROW_NUMBER() OVER (PARTITION BY Email ORDER BY MC.Id) AS RowNumber,
                        MC.Id,
                        MC.[Email],
                        MC.[FirstName], 
                        MC.[Surname], 
                        MC.[OfficeTelNumber], 
                        MC.[CellNumber], 
                        MC.[JobTitle],
                        CT.[Name], 
                        MC.[MemberId], 
                        M.[RegisteredName],
                        M.[HeadOfficePhysicalAddress]
                        FROM MemberContacts MC
                        INNER JOIN Members AS M ON MC.MemberId = M.Id
                        INNER JOIN ContactTypes AS CT ON MC.ContactTypeId = CT.Id
                        WHERE (((MC.[OfficeTelNumber] IS NOT NULL AND ((LTRIM(RTRIM(MC.[OfficeTelNumber])) <> N'') OR MC.[OfficeTelNumber] IS NULL)) OR (MC.[CellNumber] IS NOT NULL AND ((LTRIM(RTRIM(MC.[CellNumber])) <> N'') OR MC.[CellNumber] IS NULL))) 
                        AND (MC.[Email] IS NOT NULL AND ((LTRIM(RTRIM(MC.[Email])) <> N'') OR MC.[Email] IS NULL))) 
                        AND (M.[MembershipTypeId] != 4)
                        ) AS X
                        WHERE X.RowNumber = 1";

                var result = conn.Query<FreshdeskMemberContactDTO>(sql);
                return result;
            }
        }

        public bool DoesMemberExist(string registrationNumber)
        {
            var exists = false;

            if (!string.IsNullOrWhiteSpace(registrationNumber))
            {
                registrationNumber = registrationNumber.Trim().Replace("%2F", "/");
                exists = _dbContext.Members
                    .Any(i => i.RegisteredNumber == registrationNumber
                        || i.IdNumber == registrationNumber);
            }

            return exists;
        }
        public bool DoesVATNumberExist(string vatNumber)
        {
            var exists = false;

            if (!string.IsNullOrWhiteSpace(vatNumber))
            {
                exists = _dbContext.Members.Any(i => i.VatNumber == vatNumber);
            }

            return exists;
        }

        public List<ALGLeaderIdValuePairOutputDTO> GetALGLeaders(int clientId, User user)
        {
            if (clientId > 0)
            {
                var algClientLeaders = _dbContext.ALGClientLeaders
                        .Include(i => i.Client)
                            .ThenInclude(x => x.Users)
                        .Include(i => i.Leader)
                        .Where(i => i.ClientId == clientId && i.Leader.MembershipTypeId == MembershipTypes.ALGLeader)
                        .Select(m => new ALGClientLeader
                        {
                            Client = new()
                            {
                                Users = m.Client.Users.Select(x => new MemberUsers
                                {
                                    UserId = x.UserId
                                }).ToList(),
                            },
                            Leader = new()
                            {
                                Id = m.Leader.Id,
                                RegisteredName = m.Leader.RegisteredName
                            }
                        })
                        .ToList();

                if (!_globalHelper.IsInternalSACRRAUser(user))
                {
                    algClientLeaders = algClientLeaders.Where(x => x.Client.Users.Any(i => i.UserId == user.Id)).ToList();
                }

                var algLeaders = _mapper.Map<List<ALGLeaderIdValuePairOutputDTO>>(algClientLeaders);
                return algLeaders;
            }
            return null;
        }

        public List<dynamic> GetMonthlyMemberInvoices()
        {
            var memberinvoices = _dbContext.vwCalculateMonthlyAlgInvoicings.ToList();
            var months = new List<string>();
            var memberInvoicesList = new List<dynamic>();
            var algLeaderNames = _dbContext.Members
                    .Where(x => x.MembershipTypeId == MembershipTypes.ALGLeader)
                    .Select(x => x.RegisteredName)
                    .ToList();

            foreach (var algLeaderName in algLeaderNames)
            {
                var invoiceData = new ExpandoObject() as IDictionary<string, Object>;

                invoiceData.Add("RegisteredName", algLeaderName);
                foreach (var memberInvoice in memberinvoices)
                {
                    if (memberInvoice.RegisteredName == algLeaderName)
                    {
                        if (months.Find(x => x == memberInvoice.BillingDate) == null)
                        {
                            months.Add(memberInvoice.BillingDate);
                        }

                        invoiceData.Add(memberInvoice.BillingDate, memberInvoice.Amount);
                    }
                }

                memberInvoicesList.Add(invoiceData);
            }

            memberInvoicesList.Insert(0, new
            {
                Months = months
            });

            return memberInvoicesList;
        }

        private static IEnumerable<string> MonthsBetween(DateTime startDate, DateTime endDate)
        {
            DateTime iterator;
            DateTime limit;

            if (endDate > startDate)
            {
                iterator = new DateTime(startDate.Year, startDate.Month, 1);
                limit = endDate;
            }
            else
            {
                iterator = new DateTime(endDate.Year, endDate.Month, 1);
                limit = startDate;
            }

            var dateTimeFormat = CultureInfo.CurrentCulture.DateTimeFormat;
            while (iterator <= limit)
            {
                yield return string.Format("{0} {1}",
                    dateTimeFormat.GetMonthName(iterator.Month),
                    iterator.Year
                );

                iterator = iterator.AddMonths(1);
            }
        }

        private IQueryable<ALGClientLeader> SortALGClients(IQueryable<ALGClientLeader> query, string sortField, int sortOrder)
        {
            string sort = (sortOrder == -1) ? "desc" : "asc";

            switch (sortField)
            {
                case "companyName":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.RegisteredName);
                    else
                        query = query.OrderBy(i => i.Client.RegisteredName);
                    break;

                case "companyRegistrationNumber":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.RegisteredNumber);
                    else
                        query = query.OrderBy(i => i.Client.RegisteredNumber);
                    break;

                case "identificationNumber":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.IdNumber);
                    else
                        query = query.OrderBy(i => i.Client.IdNumber);
                    break;

                case "stakeholderManager":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.StakeholderManager.FirstName)
                            .ThenByDescending(i => i.Client.StakeholderManager.LastName);
                    else
                        query = query.OrderBy(i => i.Client.StakeholderManager.FirstName)
                            .ThenBy(i => i.Client.StakeholderManager.LastName); ;
                    break;

                case "applicationStatus":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.ApplicationStatusId);
                    else
                        query = query.OrderBy(i => i.Client.ApplicationStatusId);
                    break;

                case "memberActivationDate":
                    if (sort == "desc")
                        query = query.OrderByDescending(i => i.Client.DateActivated);
                    else
                        query = query.OrderBy(i => i.Client.DateActivated);
                    break;
            }

            return query;
        }

        private IQueryable<ALGClientLeader> FilterALGClients(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            if (filters.CompanyName != null)
            {
                query = FilterALGClientsByCompanyName(query, filters);
            }

            if (filters.CompanyRegistrationNumber != null)
            {
                query = FilterALGClientsByCompanyRegistrationNumber(query, filters);
            }

            if (filters.StakeholderManager != null)
            {
                query = FilterALGClientsByStakeholderManager(query, filters);
            }

            if (filters.ApplicationStatus != null)
            {
                query = FilterALGClientsByApplicationStatus(query, filters);
            }

            if (filters.MemberActivationDate != null)
            {
                query = FilterALGClientsByCompanyActivationDate(query, filters);
            }

            return query;
        }

        private IQueryable<ALGClientLeader> FilterALGClientsByCompanyName(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            for (int counter = 0; counter < filters.CompanyName.Count; counter++)
            {
                var company = filters.CompanyName[counter];

                if (!string.IsNullOrWhiteSpace(company.Value))
                {
                    if (company.MatchMode == "contains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredName.Contains(company.Value) && i.Client.RegisteredName.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredName.Contains(company.Value) || i.Client.RegisteredName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredName.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "notContains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => !i.Client.RegisteredName.Contains(company.Value) && !i.Client.RegisteredName.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => !i.Client.RegisteredName.Contains(company.Value) || !i.Client.RegisteredName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.RegisteredName.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "startsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredName.StartsWith(company.Value) && i.Client.RegisteredName.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredName.StartsWith(company.Value) || i.Client.RegisteredName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredName.StartsWith(company.Value));
                        }
                    }
                    else if (company.MatchMode == "endsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredName.EndsWith(company.Value) && i.Client.RegisteredName.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredName.EndsWith(company.Value) || i.Client.RegisteredName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredName.EndsWith(company.Value));
                        }
                    }

                    else if (company.MatchMode == "equals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredName.Equals(company.Value) && i.Client.RegisteredName.Equals(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredName.Equals(company.Value) || i.Client.RegisteredName.Equals(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredName.Equals(company.Value));
                        }
                    }
                    else if (company.MatchMode == "notEquals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => !i.Client.RegisteredName.Equals(company.Value) && !i.Client.RegisteredName.Equals(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => !i.Client.RegisteredName.Equals(company.Value) || !i.Client.RegisteredName.Equals(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.RegisteredName.Equals(company.Value));
                        }
                    }
                }
            }

            return query;
        }
        private IQueryable<ALGClientLeader> FilterALGClientsByCompanyRegistrationNumber(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            for (int counter = 0; counter < filters.CompanyRegistrationNumber.Count; counter++)
            {
                var company = filters.CompanyRegistrationNumber[counter];

                if (!string.IsNullOrWhiteSpace(company.Value))
                {

                    if (company.MatchMode == "contains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.Contains(company.Value) && i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.Contains(company.Value) || i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredNumber.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "notContains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => !i.Client.RegisteredNumber.Contains(company.Value) && !i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => !i.Client.RegisteredNumber.Contains(company.Value) || !i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.RegisteredNumber.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "startsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.StartsWith(company.Value) && i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.StartsWith(company.Value) || i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredNumber.StartsWith(company.Value));
                        }
                    }
                    else if (company.MatchMode == "endsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.EndsWith(company.Value) && i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.EndsWith(company.Value) || i.Client.RegisteredNumber.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredNumber.EndsWith(company.Value));
                        }
                    }
                    else if (company.MatchMode == "equals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.Equals(company.Value) && i.Client.RegisteredNumber.Equals(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.RegisteredNumber.Equals(company.Value) || i.Client.RegisteredNumber.Equals(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.RegisteredNumber.Equals(company.Value));
                        }
                    }
                    else if (company.MatchMode == "notEquals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => !i.Client.RegisteredNumber.Equals(company.Value) && !i.Client.RegisteredNumber.Equals(company.Value));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => !i.Client.RegisteredNumber.Equals(company.Value) || !i.Client.RegisteredNumber.Equals(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.RegisteredNumber.Equals(company.Value));
                        }
                    }
                }
            }

            return query;
        }

        private IQueryable<ALGClientLeader> FilterALGClientsByStakeholderManager(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            for (int counter = 0; counter < filters.StakeholderManager.Count; counter++)
            {
                var company = filters.StakeholderManager[counter];

                if (!string.IsNullOrEmpty(company.Value))
                {
                    if (company.MatchMode == "contains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.Contains(company.Value) && i.Client.StakeholderManager.FirstName.Contains(company.Value))
                                && (i.Client.StakeholderManager.LastName.Contains(company.Value) && i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.Contains(company.Value) || i.Client.StakeholderManager.FirstName.Contains(company.Value))
                            || (i.Client.StakeholderManager.LastName.Contains(company.Value) || i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.StakeholderManager.FirstName.Contains(company.Value) ||
                                x.Client.StakeholderManager.LastName.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "notContains")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (!i.Client.StakeholderManager.FirstName.Contains(company.Value) && !i.Client.StakeholderManager.FirstName.Contains(company.Value))
                                && (!i.Client.StakeholderManager.LastName.Contains(company.Value) && !i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => (!i.Client.StakeholderManager.FirstName.Contains(company.Value) || !i.Client.StakeholderManager.FirstName.Contains(company.Value))
                              || (!i.Client.StakeholderManager.LastName.Contains(company.Value) || !i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.StakeholderManager.FirstName.Contains(company.Value)
                                || !x.Client.StakeholderManager.FirstName.Contains(company.Value));
                        }
                    }
                    else if (company.MatchMode == "startsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.StartsWith(company.Value) && i.Client.StakeholderManager.FirstName.Contains(company.Value))
                              && (i.Client.StakeholderManager.LastName.StartsWith(company.Value) && i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.StartsWith(company.Value) || i.Client.StakeholderManager.FirstName.Contains(company.Value))
                              || i.Client.StakeholderManager.LastName.StartsWith(company.Value) || i.Client.StakeholderManager.LastName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.StakeholderManager.FirstName.StartsWith(company.Value)
                             || x.Client.StakeholderManager.LastName.StartsWith(company.Value));
                        }
                    }
                    else if (company.MatchMode == "endsWith")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.EndsWith(company.Value) && i.Client.StakeholderManager.FirstName.Contains(company.Value))
                              && (i.Client.StakeholderManager.LastName.EndsWith(company.Value) && i.Client.StakeholderManager.LastName.Contains(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => i.Client.StakeholderManager.FirstName.EndsWith(company.Value) || i.Client.StakeholderManager.FirstName.Contains(company.Value)
                                || i.Client.StakeholderManager.LastName.EndsWith(company.Value) || i.Client.StakeholderManager.LastName.Contains(company.Value));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.StakeholderManager.FirstName.EndsWith(company.Value)
                                || x.Client.StakeholderManager.LastName.EndsWith(company.Value));
                        }
                    }
                    if (company.MatchMode == "equals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.Equals(company.Value) && i.Client.StakeholderManager.FirstName.Equals(company.Value))
                                && (i.Client.StakeholderManager.LastName.Equals(company.Value) && i.Client.StakeholderManager.LastName.Equals(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => (i.Client.StakeholderManager.FirstName.Equals(company.Value) || i.Client.StakeholderManager.FirstName.Equals(company.Value))
                            || (i.Client.StakeholderManager.LastName.Equals(company.Value) || i.Client.StakeholderManager.LastName.Equals(company.Value)));
                        }
                        else
                        {
                            query = query.Where(x => x.Client.StakeholderManager.FirstName.Equals(company.Value) ||
                                x.Client.StakeholderManager.LastName.Equals(company.Value));
                        }
                    }
                    if (company.MatchMode == "notEquals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(i => (!i.Client.StakeholderManager.FirstName.Equals(company.Value) && !i.Client.StakeholderManager.FirstName.Equals(company.Value))
                                && (!i.Client.StakeholderManager.LastName.Equals(company.Value) && !i.Client.StakeholderManager.LastName.Equals(company.Value)));
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(i => (!i.Client.StakeholderManager.FirstName.Equals(company.Value) || !i.Client.StakeholderManager.FirstName.Equals(company.Value))
                            || (!i.Client.StakeholderManager.LastName.Equals(company.Value) || !i.Client.StakeholderManager.LastName.Equals(company.Value)));
                        }
                        else
                        {
                            query = query.Where(x => !x.Client.StakeholderManager.FirstName.Equals(company.Value) ||
                                !x.Client.StakeholderManager.LastName.Equals(company.Value));
                        }
                    }
                }
            }

            return query;
        }

        private IQueryable<ALGClientLeader> FilterALGClientsByApplicationStatus(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            var applicationStatusesEnumValues = EnumHelper.GetEnumIdValuePairs<ApplicationStatuses>();

            for (int counter = 0; counter < filters.ApplicationStatus.Count; counter++)
            {
                var company = filters.ApplicationStatus[counter];

                if (!string.IsNullOrWhiteSpace(company.Value))
                {
                    var enumValue = applicationStatusesEnumValues.FirstOrDefault(i => i.Value == company.Value);

                    if (company.MatchMode == "equals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId == (ApplicationStatuses)enumValue.Id && x.Client.ApplicationStatusId == (ApplicationStatuses)enumValue.Id);
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId == (ApplicationStatuses)enumValue.Id || x.Client.ApplicationStatusId == (ApplicationStatuses)enumValue.Id);
                        }
                        else
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId == (ApplicationStatuses)enumValue.Id);
                        }
                    }
                    else if (company.MatchMode == "notEquals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId != (ApplicationStatuses)enumValue.Id && x.Client.ApplicationStatusId != (ApplicationStatuses)enumValue.Id);
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId != (ApplicationStatuses)enumValue.Id || x.Client.ApplicationStatusId != (ApplicationStatuses)enumValue.Id);
                        }
                        else
                        {
                            query = query.Where(x => x.Client.ApplicationStatusId != (ApplicationStatuses)enumValue.Id);
                        }
                    }
                }
            }

            return query;
        }

        private IQueryable<ALGClientLeader> FilterALGClientsByCompanyActivationDate(IQueryable<ALGClientLeader> query, MyClientsFilters filters)
        {
            for (int counter = 0; counter < filters.MemberActivationDate.Count; counter++)
            {
                var company = filters.MemberActivationDate[counter];

                if (!string.IsNullOrWhiteSpace(company.Value))
                {
                    if (company.MatchMode == "equals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date == Convert.ToDateTime(company.Value).Date && x.Client.DateActivated.Value.Date == Convert.ToDateTime(company.Value).Date);
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date == Convert.ToDateTime(company.Value).Date || x.Client.DateActivated.Value.Date == Convert.ToDateTime(company.Value).Date);
                        }
                        else
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date == Convert.ToDateTime(company.Value).Date);
                        }
                    }
                    else if (company.MatchMode == "notEquals")
                    {
                        if (counter > 0 && company.Operator == "and")
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date != Convert.ToDateTime(company.Value).Date && x.Client.DateActivated.Value.Date != Convert.ToDateTime(company.Value).Date);
                        }
                        else if (counter > 0 && company.Operator == "or")
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date != Convert.ToDateTime(company.Value).Date || x.Client.DateActivated.Value.Date != Convert.ToDateTime(company.Value).Date);
                        }
                        else
                        {
                            query = query.Where(x => x.Client.DateActivated.Value.Date != Convert.ToDateTime(company.Value).Date);
                        }
                    }
                }
            }

            return query;
        }

        public List<vwMemberDetails> GetAllMemberDetailsAsync()
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext).Result;
            if (user == null)
                return new List<vwMemberDetails>();

            // Backoffice roles can see all member data
            if (user.RoleId == UserRoles.FinancialAdministrator
                || user.RoleId == UserRoles.SACRRAAdministrator
                || user.RoleId == UserRoles.StakeHolderAdministrator
                || user.RoleId == UserRoles.StakeHolderManager
                || user.RoleId == UserRoles.Bureau)
            {
                return _dbContext.vwMemberDetails.ToList();
            }
            // Members can only see their own data
            else if (user.RoleId == UserRoles.Member)
            {
                var memberId = _dbContext.MemberUsers.Where(x => x.UserId == user.Id).Select(x => x.MemberId).FirstOrDefault();
                return _dbContext.vwMemberDetails.Where(m => m.Id == memberId).ToList();
            }
            // ALG Leaders can see their ALG Client data
            else if (user.RoleId == UserRoles.ALGLeader)
            {
                var algLeaderMemberId = _dbContext.MemberUsers
                .Include(x => x.Member)
                .Where(x => x.UserId == user.Id && x.Member.MembershipTypeId == MembershipTypes.ALGLeader)
                .Select(x => x.MemberId)
                .FirstOrDefault();

                if (algLeaderMemberId == 0)
                    return new List<vwMemberDetails>();

                return _dbContext.vwMemberDetails.Where(m => m.ALGLeaderId == algLeaderMemberId).ToList();

            }
            // Default to an empty list if no role is found
            else
            {
                return new List<vwMemberDetails>();
            }
        }

        public async Task<vwMemberDetails> GetMemberDetailsBySRNAsync(string srnNumber)
        {
            if (string.IsNullOrEmpty(srnNumber))
                throw new ArgumentException("SRN number is required", nameof(srnNumber));

            return await _dbContext.vwMemberDetails
                .FirstOrDefaultAsync(m => m.SRNNumber == srnNumber);
        }



        public void UpdateMemberStakeholderManager(int memberId,int stakeholderId, User user, int oldStakeholderId)
        {
            var member = _dbContext.Set<Member>()
                         .FirstOrDefault(i => i.Id == memberId);

            member.StakeholderManagerId = stakeholderId;
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new MemberStagingChangeLogResource();


            var updateDetailsBlob = JsonConvert.SerializeObject(member);
            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                _globalHelper.CreateEventLog(_dbContext, user.Id, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
            }
        }

        }


}
