using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.MemberContact;
using System.Net;
using System.ComponentModel.Design;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.Exceptions;

namespace Sacrra.Membership.Business.Services
{
    public class MemberServiceHelper
    {
        private readonly AppDbContext _dbContext;
        private readonly CamundaRepository _camundaRepository;
        private readonly BureauRepository _bureauRepository;
        private readonly GlobalHelper _globalHelper;
        private LookupsRepository _lookupsRepo;
        public IMapper _mapper { get; }

        public MemberServiceHelper(AppDbContext dbContext, CamundaRepository camundaRepository, GlobalHelper globalHelper, IMapper mapper, BureauRepository bureauRepository, LookupsRepository lookupsRepo)
        {
            _dbContext = dbContext;
            _camundaRepository = camundaRepository;
            _globalHelper = globalHelper;
            _mapper = mapper;
            _bureauRepository = bureauRepository;
            _lookupsRepo = lookupsRepo;
        }

        public int[] GetActiveSRNStatuses()
        {
            var activeStatuses = _dbContext.SRNStatuses
                .Where(i => i.IsActive)
                .Select(m => m.Id)
                .ToArray();

            return activeStatuses;
        }

        public string AddMemberRegistrationTask(Member member, bool doesMemberExist)
        {
            return _camundaRepository.AddMemberRegistrationTask(member, doesMemberExist);
        }

        public bool DoesMemberExist(MemberRequestInputDTO modelForCreate)
        {
            Member selectRecord = null;
            if (!string.IsNullOrWhiteSpace(modelForCreate.IdentificationNumber))
            {
                selectRecord = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefault(s => s.IdNumber == modelForCreate.IdentificationNumber.Trim());
            }
            else
            {
                selectRecord = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefault(s => s.RegisteredNumber == modelForCreate.CompanyRegistrationNumber.Trim());
            }

            if (selectRecord != null)
                return true;
            else
                return false;
        }

        public Member GetALGLeaderIdByUser(int userId)
        {
            if (userId > 0)
            {
                var leader = _dbContext.Members
                            .Include(i => i.Users)
                            .AsNoTracking()
                            .Where(i => i.MembershipTypeId == MembershipTypes.ALGLeader)
                            .FirstOrDefault(i => i.Users.Any(x => x.UserId == userId));

                return leader;
            }

            return null;
        }

        public void UpdatePartialMember(int userId, MemberRequestInputDTO modelForCreate)
        {
            var existingPartialMember = _dbContext.PartialMembers
                    .AsNoTracking()
                    .FirstOrDefault(s => s.UserId == userId && !s.IsComplete);

            if (existingPartialMember != null)
            {
                existingPartialMember.IsComplete = true;
                _dbContext.Set<PartialMember>().Update(existingPartialMember);
            }
        }

        public void CreateEventLog(MemberRequestInputDTO modelForCreate, Member member, MemberStagingChangeLogResource stagingChangeLog)
        {
            CreateContactsEventLog(modelForCreate.Contacts, stagingChangeLog);
            CreateTradingNamesEventLog(modelForCreate.CompanyTradingNames, stagingChangeLog);
            CreateDomainsEventLog(modelForCreate.Domains, stagingChangeLog);
            CreateALGLeadersEventLog(modelForCreate.ALGLeaderIds, stagingChangeLog);

            var modelForUpdate = _mapper.Map<MemberUpdateResource>(modelForCreate);

            CreateMemberEventLog(modelForUpdate, member, stagingChangeLog);
        }

        public void CreateEventLog(AppDbContext _dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var userFullName = "";
            if (userId > 0)
            {
                var user = _dbContext.Users.FirstOrDefault(i => i.Id == userId);
                if (user != null)
                    userFullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                userFullName = "Internal System";
            }


            var entityTypeId = _globalHelper.GetEntityTypeId(_dbContext, entityTypeName);

            if (entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                _dbContext.Add(eventLog);
                _dbContext.SaveChanges();
            }
        }

        private void CreateContactsEventLog(List<ContactInputDTO> contacts, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var contact in contacts)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact First Name",
                        OldValue = "",
                        NewValue = contact.FirstName
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Surname",
                        OldValue = "",
                        NewValue = contact.LastName
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Cell Number",
                        OldValue = "",
                        NewValue = contact.CellphoneNumber
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Email",
                        OldValue = "",
                        NewValue = contact.EmailAddress
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Office Tel Number",
                        OldValue = "",
                        NewValue = contact.OfficeNumber
                    });

                var newType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == contact.ContactTypeId);

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Contact Type",
                        OldValue = "",
                        NewValue = newType.Name
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Job Title",
                        OldValue = "",
                        NewValue = contact.Designation
                    });
            }
        }

        internal int CreateMemberChangeRequest(Member member, MemberUpdateInputDTO memberUpdateInputDTO, int userId)
        {
            int changeRequestId = 0;

            if (member != null && memberUpdateInputDTO != null)
            {
                MemberGetResource memberGetResource = _mapper.Map<MemberGetResource>(member);

                if (DoMemberChangesRequireApproval(member, memberUpdateInputDTO))
                {
                    var memberChangeRequest = _dbContext.Set<ChangeRequestStaging>()
                            .AsNoTracking()
                            .FirstOrDefault(i => i.ObjectId == member.Id);

                    if (memberChangeRequest != null)
                    {
                        changeRequestId = memberChangeRequest.Id;
                        var objectId = memberChangeRequest.ObjectId;

                        var changedModel = _mapper.Map(memberUpdateInputDTO, memberChangeRequest);
                        memberChangeRequest.Status = ChangeRequestStatus.NotActioned;
                        memberChangeRequest.Id = changeRequestId;
                        memberChangeRequest.ObjectId = objectId;
                        memberChangeRequest.DateCreated = DateTime.Now;

                        var oldDetails = _mapper.Map<MemberUpdateAllTypesResource>(member);
                        memberChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                        memberChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(memberUpdateInputDTO);
                        memberChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(CreateStagingChangeLog(member, memberUpdateInputDTO));
                        memberChangeRequest.UserId = userId;

                        _dbContext.Set<ChangeRequestStaging>().Update(memberChangeRequest);
                        _dbContext.SaveChanges();
                    }
                    else
                    {
                        var newChangeRequest = _mapper.Map<ChangeRequestStaging>(memberUpdateInputDTO);
                        newChangeRequest.Id = 0;
                        newChangeRequest.Type = ChangeObjectType.Member;
                        newChangeRequest.Status = ChangeRequestStatus.NotActioned;
                        newChangeRequest.ObjectId = member.Id;
                        newChangeRequest.DateCreated = DateTime.Now;

                        var oldDetails = _mapper.Map<MemberUpdateInputDTO>(member);
                        newChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                        newChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(memberUpdateInputDTO);
                        newChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(CreateStagingChangeLog(member, memberUpdateInputDTO));
                        newChangeRequest.UserId = userId;

                        _dbContext.Set<ChangeRequestStaging>().Add(newChangeRequest);
                        _dbContext.SaveChanges();
                        changeRequestId = newChangeRequest.Id;

                    }
                }
            }

            return changeRequestId;
        }

        public void ApplyMemberChanges(AppDbContext dbContext, Member member, IMapper mapper, MemberUpdateInputDTO memberUpdateInputDTO, User user)
        {
            if (member != null)
            {
                MemberStagingChangeLogResource stagingChangeLog = new MemberStagingChangeLogResource();
                UpdateMemberContacts(member, memberUpdateInputDTO, stagingChangeLog);
                UpdateMemberTradingNames(member, memberUpdateInputDTO, stagingChangeLog);
                UpdateALGLeaders(_dbContext, member.Id, memberUpdateInputDTO, stagingChangeLog, member);

                CreateMemberChangeLog(_dbContext, memberUpdateInputDTO, member, stagingChangeLog);

                var model = _mapper.Map(memberUpdateInputDTO, member);
                model.Id = member.Id;

                string identityDocument = null;
                string ncrCertificateDocument = null;
                string auditedFinancialDocument = null;

                if (memberUpdateInputDTO.IdentificationDocument != null)
                {
                    identityDocument = JsonConvert.SerializeObject(memberUpdateInputDTO.IdentificationDocument);
                }

                if (memberUpdateInputDTO.NCRCertificateDocument != null)
                {
                    ncrCertificateDocument = JsonConvert.SerializeObject(memberUpdateInputDTO.NCRCertificateDocument);
                }

                if (memberUpdateInputDTO.AuditedFinancialDocument != null)
                {
                    auditedFinancialDocument = JsonConvert.SerializeObject(memberUpdateInputDTO.AuditedFinancialDocument);
                }

                if (memberUpdateInputDTO.PrincipleDebtRangeId > 0)
                    model.NcrCategory = ((PrincipleDebtRanges)Enum.Parse(typeof(PrincipleDebtRanges), "N" + memberUpdateInputDTO.PrincipleDebtRangeId)).ToString();

                var ncrCategoryEnumList = _lookupsRepo.GetEnumIdValuePairs<NCRCategoryEnum>();

                model.ApplicationStatusId = member.ApplicationStatusId;
                model.StakeholderManagerId = member.StakeholderManagerId;
                model.NcrCategory = (memberUpdateInputDTO.NCRFeeCategoryId > 0) ? ncrCategoryEnumList.FirstOrDefault(x => x.Id == memberUpdateInputDTO.NCRFeeCategoryId).Value : null;

                if (model.PrimaryBureauId <= 0)
                {
                    model.PrimaryBureauId = null;
                }

                if (model.SecondaryBureauId <= 0)
                {
                    model.SecondaryBureauId = null;
                }

                model.IdNumber = (!string.IsNullOrEmpty(model.IdNumber)) ? model.IdNumber : null;
                model.RegisteredNumber = (!string.IsNullOrEmpty(model.RegisteredNumber)) ? model.RegisteredNumber : null;

                var existingRegisteredOrIdNumber = new Member();
                if (memberUpdateInputDTO.CompanyRegistrationNumber != null)
                {
                    existingRegisteredOrIdNumber = _dbContext.Members
                        .Where(x => x.RegisteredNumber == memberUpdateInputDTO.CompanyRegistrationNumber)
                        .Where(x => x.Id != memberUpdateInputDTO.Id)
                        .AsNoTracking()
                        .FirstOrDefault();
                }
                else
                {
                    existingRegisteredOrIdNumber = _dbContext.Members
                            .Where(x => x.IdNumber == memberUpdateInputDTO.IdentificationNumber)
                            .Where(x => x.Id != memberUpdateInputDTO.Id)
                            .AsNoTracking()
                            .FirstOrDefault();
                }

                if (existingRegisteredOrIdNumber != null)
                {
                    throw new RegistrationNumberExistsException((int)HttpStatusCode.BadRequest, "Member registration number already exists.", null);
                }

                _dbContext.Set<Member>().Update(model);

                var memberDoc = _dbContext.MemberDocuments
                    .AsNoTracking()
                    .FirstOrDefault(i => i.MemberId == member.Id);

                if (memberDoc == null)
                    memberDoc = new MemberDocument();

                CreateMemberDocumentsChangeLog(memberDoc, identityDocument, ncrCertificateDocument, auditedFinancialDocument, stagingChangeLog);

                memberDoc.MemberId = member.Id;
                memberDoc.IDDocumentBlob = identityDocument;
                memberDoc.NcrCertificateBlob = ncrCertificateDocument;
                memberDoc.AuditedFinancialBlob = auditedFinancialDocument;
                _dbContext.Set<MemberDocument>().Update(memberDoc);

                var memberDetails = _dbContext.ALGMemberDetails
                    .AsNoTracking()
                    .FirstOrDefault(i => i.MemberId == member.Id);

                if (model.MembershipTypeId == MembershipTypes.ALGLeader)
                {
                    CreateALGMemberDetailsChangeLog(memberDetails, memberUpdateInputDTO.NumberOfClients, memberUpdateInputDTO.LoanManagementSystemName, stagingChangeLog);
                }

                if (memberDetails != null)
                {
                    memberDetails.NumberOfClients = memberUpdateInputDTO.NumberOfClients;
                    memberDetails.LoanManagementSystemName = memberUpdateInputDTO.LoanManagementSystemName;
                    _dbContext.Update(memberDetails);
                }
                else if (memberDetails == null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGLeader)
                    {
                        memberDetails = new ALGMemberDetails
                        {
                            MemberId = member.Id,
                            NumberOfClients = memberUpdateInputDTO.NumberOfClients,
                            LoanManagementSystemName = memberUpdateInputDTO.LoanManagementSystemName
                        };
                        _dbContext.Add(memberDetails);
                    }
                }


                _dbContext.SaveChanges();

                var updateDetailsBlob = JsonConvert.SerializeObject(memberUpdateInputDTO);
                var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

                if (stagingChangeLog.Changes.Count > 0)
                {
                    _globalHelper.CreateEventLog(_dbContext, user.Id, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
                }
            }
        }

        private MemberStagingChangeLogResource CreateMemberChangeLog(AppDbContext dbContext, MemberUpdateInputDTO memberUpdateInputDTO, Member member, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (memberUpdateInputDTO != null)
            {
                var updateModelType = memberUpdateInputDTO.GetType();
                var memberModelType = member.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> memberProperties = new List<PropertyInfo>(memberModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(memberUpdateInputDTO, null);

                    var propAssociation = _globalHelper.GetPropertyAssociationName(updateProp);

                    var memberProp = memberProperties.FirstOrDefault(i => i.Name == (!string.IsNullOrEmpty(propAssociation) ? propAssociation : updateProp.Name));
                    if (memberProp != null)
                    {
                        //Primary keys don't get updated
                        if (memberProp.Name != "MemberId")
                        {
                            object memberPropValue = memberProp.GetValue(member, null);

                            if (memberPropValue != null)
                            {
                                var propType = memberPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    CreateUpdateLog(updatePropValue, memberProp, memberPropValue, stagingChangeList);
                                }
                                else if (propType.IsEnum)
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = _globalHelper.GetEnumValue(memberProp.Name, (int)updatePropValue);

                                    if (memberPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = _globalHelper.GetEnumValue(memberProp.Name, (int)memberPropValue);

                                    if (newValue != oldValue)
                                    {
                                        if (!string.IsNullOrEmpty(newValue))
                                            newValue = _globalHelper.GetEnumValue(memberProp.Name, (int)updatePropValue);
                                        if (!string.IsNullOrEmpty(oldValue))
                                            oldValue = _globalHelper.GetEnumValue(memberProp.Name, (int)memberPropValue);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(memberProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType == typeof(List<MemberDomain>))
                                {
                                    if (memberUpdateInputDTO.Domains != null)
                                    {
                                        foreach (var domain in memberUpdateInputDTO.Domains)
                                        {
                                            string oldValue = "";
                                            string newValue = "";

                                            var oldDomain = member.Domains.FirstOrDefault(i => i.Id == domain.Id);

                                            if (oldDomain != null)
                                            {
                                                oldValue = oldDomain.Name;
                                            }

                                            newValue = domain.Name;

                                            if (oldValue != newValue)
                                            {
                                                var stagingChange = new StagingChange
                                                {
                                                    Name = "Member Domain",
                                                    OldValue = oldValue,
                                                    NewValue = newValue
                                                };

                                                stagingChangeList.Add(stagingChange);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
                return stagingChangeLog;
            }

            return null;
        }

        private void CreateALGMemberDetailsChangeLog(ALGMemberDetails memberDetails, int numberOfClients, string loanManagementSystemName, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (memberDetails != null)
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                if (memberDetails.NumberOfClients != numberOfClients)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "Number of Clients",
                        OldValue = memberDetails.NumberOfClients.ToString(),
                        NewValue = numberOfClients.ToString()
                    };

                    stagingChangeList.Add(stagingChange);
                }
                if (memberDetails.LoanManagementSystemName != loanManagementSystemName)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "Loan Management System Name",
                        OldValue = memberDetails.LoanManagementSystemName,
                        NewValue = loanManagementSystemName
                    };

                    stagingChangeList.Add(stagingChange);
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }
            else
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>
                {
                    new StagingChange
                    {
                        Name = "Number of Clients",
                        OldValue = "0",
                        NewValue = numberOfClients.ToString()
                    },
                    new StagingChange
                    {
                        Name = "Loan Management System Name",
                        OldValue = "",
                        NewValue = loanManagementSystemName
                    }
                };

                stagingChangeList.AddRange(stagingChangeList);
            }
        }

        private void CreateMemberDocumentsChangeLog(MemberDocument memberDoc, string idDocumentJson, string ncrCertificateJson, string auditedFinancialDocumentJson, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (memberDoc != null)
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                if (memberDoc.IDDocumentBlob != idDocumentJson)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "ID Document",
                        OldValue = memberDoc.IDDocumentBlob,
                        NewValue = idDocumentJson
                    };

                    stagingChangeList.Add(stagingChange);
                }

                if (memberDoc.AuditedFinancialBlob != auditedFinancialDocumentJson)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "Audited Financial Document",
                        OldValue = memberDoc.AuditedFinancialBlob,
                        NewValue = auditedFinancialDocumentJson
                    };

                    stagingChangeList.Add(stagingChange);
                }

                if (memberDoc.NcrCertificateBlob != ncrCertificateJson)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "NCR Certificate",
                        OldValue = memberDoc.NcrCertificateBlob,
                        NewValue = ncrCertificateJson
                    };

                    stagingChangeList.Add(stagingChange);
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }
        }

        private void UpdateALGLeaders(AppDbContext dbContext, int memberId, MemberUpdateInputDTO modelForUpdate, MemberStagingChangeLogResource stagingChangeLog, Member member)
        {
            var algLeaders = dbContext.ALGClientLeaders
                .Include(i => i.Leader)
                .AsNoTracking()
                .Where(i => i.ClientId == memberId)
                .ToList();

            var lstExistingALGLeaders = new List<int>();
            var lstDeletedALGLeaders = new List<ALGClientLeader>();

            var newALGLeaders = new List<int>();
            if (modelForUpdate != null)
            {
                if (modelForUpdate.ALGLeaders != null)
                {
                    foreach (var leaderId in modelForUpdate.ALGLeaders)
                    {
                        //Create log for new ALG leaders
                        if (!algLeaders.Any(i => i.LeaderId == leaderId))
                        {
                            newALGLeaders.Add(leaderId);

                            var newLeader = dbContext.Members.FirstOrDefault(i => i.Id == leaderId);

                            var stagingChange = new StagingChange
                            {
                                Name = "ALG Leader",
                                OldValue = "",
                                NewValue = newLeader.RegisteredName
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                    }
                }

                //Create log for deleted ALG leaders

                foreach (var leader in algLeaders)
                {
                    if (!modelForUpdate.ALGLeaders.Any(i => i.Equals(leader.LeaderId)))
                    {
                        lstDeletedALGLeaders.Add(leader);

                        var stagingChange = new StagingChange
                        {
                            Name = "ALG Leader",
                            OldValue = leader.Leader.RegisteredName,
                            NewValue = ""
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                    }
                }

                //Remove access to all deleted ALG users
                foreach (var leader in lstDeletedALGLeaders)
                {
                    //Get all users of the ALG Leader that was removed
                    var algLeaderUsers = dbContext.MemberUsers
                        .AsNoTracking()
                        .Where(i => i.MemberId == leader.LeaderId)
                        .ToList();

                    if (algLeaderUsers != null)
                    {
                        //Get all users that are linked to the client
                        var allUsersLinkedToClient = dbContext.MemberUsers
                            .Where(i => i.MemberId == memberId)
                            .ToList();

                        if (allUsersLinkedToClient != null)
                        {
                            //Remove the ALG users from the Client

                            foreach (var user in allUsersLinkedToClient)
                            {
                                var userToBeRemoved = member.Users
                                    .FirstOrDefault(i => i.UserId == user.UserId
                                     && algLeaderUsers.Any(x => x.UserId.Equals(i.UserId)));

                                if (userToBeRemoved != null)
                                {
                                    var attachedEntity = _dbContext.ChangeTracker.Entries<MemberUsers>().FirstOrDefault(e => e.Entity.Id == userToBeRemoved.Id);
                                    if (attachedEntity != null)
                                    {
                                        _dbContext.Entry(attachedEntity.Entity).State = EntityState.Detached;
                                    }

                                    //_dbContext.MemberUsers.Remove(userToBeRemoved);
                                    _dbContext.Entry(userToBeRemoved).State = EntityState.Deleted;
                                    member.Users.Remove(userToBeRemoved);
                                    dbContext.SaveChanges();
                                }
                            }
                        }
                    }
                }

                //Delete all ALG Leaders that were removed from the update recource
                if (lstDeletedALGLeaders.Count > 0)
                {
                    _dbContext.ALGClientLeaders.RemoveRange(lstDeletedALGLeaders);
                }

                //Add new ALG leaders
                foreach (var leaderId in newALGLeaders)
                {
                    _dbContext.ALGClientLeaders.Add(new ALGClientLeader
                    {
                        ClientId = memberId,
                        LeaderId = leaderId,
                        DateCreated = DateTime.Now
                    });

                    //Give access to all new ALG users
                    var algLeaderUsers = dbContext.MemberUsers
                        .Include(i => i.User)
                        .Where(i => i.MemberId == leaderId && i.User.RoleId == UserRoles.ALGLeader)
                        .ToList();

                    foreach (var user in algLeaderUsers)
                    {
                        _dbContext.MemberUsers.Add(new MemberUsers
                        {
                            UserId = user.UserId,
                            MemberId = memberId,
                            DateCreated = DateTime.Now
                        });
                    }
                }
            }
        }

        private void UpdateMemberTradingNames(Member member, MemberUpdateInputDTO memberUpdateInputDTO, MemberStagingChangeLogResource stagingChangeLog)
        {
            //Add new trading names and update existing ones
            var lstExistingTradingNames = new List<TradingName>();
            if (memberUpdateInputDTO != null)
            {
                if (memberUpdateInputDTO.CompanyTradingNames != null)
                {
                    var newTradingNames = new List<TradingNameUpdateResource>();
                    foreach (var tradingName in memberUpdateInputDTO.CompanyTradingNames)
                    {
                        if (!member.TradingNames.Any(i => i.Id == tradingName.Id))
                        {
                            newTradingNames.Add(tradingName);

                            var stagingChange = new StagingChange
                            {
                                Name = "Member Trading Name",
                                OldValue = "",
                                NewValue = tradingName.Name
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        else
                        {
                            var existingTradingName = member.TradingNames
                                .FirstOrDefault(i => i.Id == tradingName.Id);

                            if (existingTradingName.Name != tradingName.Name)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Member Trading Name",
                                    OldValue = existingTradingName.Name,
                                    NewValue = tradingName.Name
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingTradingName.Name = tradingName.Name;

                            lstExistingTradingNames.Add(existingTradingName);
                        }
                    }
                }
            }

            //Delete all trading names that were removed from the update recource
            var deletedTradingNames = new List<TradingName>();

            if (member.TradingNames != null)
            {
                foreach (var tradingName in member.TradingNames)
                {
                    var tradingNameFound = lstExistingTradingNames.FirstOrDefault(i => i.Id == tradingName.Id);
                    if (tradingNameFound == null)
                        deletedTradingNames.Add(tradingName);
                }
            }

            if (deletedTradingNames.Count > 0)
            {
                foreach (var tradingName in deletedTradingNames)
                {
                    member.TradingNames.Remove(tradingName);
                }
            }
        }

        private void UpdateMemberContacts(Member member, MemberUpdateInputDTO memberUpdateInputDTO, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (stagingChangeLog.Changes == null)
            {
                stagingChangeLog.Changes = new List<StagingChange>();
            }

            if (memberUpdateInputDTO?.Contacts == null)
                return;

            var lstExistingContacts = new List<MemberContact>();
            var contactTypes = _dbContext.ContactTypes
                .AsNoTracking()
                .ToDictionary(x => x.Id, x => x.Name);

            foreach (var contact in memberUpdateInputDTO.Contacts)
            {
                var existingContact = member.Contacts.FirstOrDefault(i => i.Id == contact.Id);

                if (existingContact == null)
                {
                    // New contact
                    var stagingChange = new StagingChange
                    {
                        Name = "New Contact Added",
                        OldValue = "",
                        NewValue = $"{contact.FirstName} {contact.LastName}"
                    };
                    stagingChangeLog.Changes.Add(stagingChange);
                    continue;
                }

                // Existing contact - check field by field changes
                AddChangeIfDifferent("Contact First Name", existingContact.FirstName, contact.FirstName, stagingChangeLog);
                existingContact.FirstName = contact.FirstName;

                AddChangeIfDifferent("Contact Surname", existingContact.Surname, contact.LastName, stagingChangeLog);
                existingContact.Surname = contact.LastName;

                AddChangeIfDifferent("Contact Cell Number", existingContact.CellNumber, contact.CellphoneNumber, stagingChangeLog);
                existingContact.CellNumber = contact.CellphoneNumber;

                AddChangeIfDifferent("Contact Email", existingContact.Email, contact.EmailAddress, stagingChangeLog);
                existingContact.Email = contact.EmailAddress;

                AddChangeIfDifferent("Contact Office Tel Number", existingContact.OfficeTelNumber, contact.OfficeNumber, stagingChangeLog);
                existingContact.OfficeTelNumber = contact.OfficeNumber;

                if (existingContact.ContactTypeId != contact.ContactTypeId)
                {
                    contactTypes.TryGetValue(existingContact.ContactTypeId, out var oldTypeName);
                    contactTypes.TryGetValue(contact.ContactTypeId, out var newTypeName);

                    AddChangeIfDifferent("Contact Contact Type", oldTypeName, newTypeName, stagingChangeLog);
                    existingContact.ContactTypeId = contact.ContactTypeId;
                }

                AddChangeIfDifferent("Contact Job Title", existingContact.JobTitle, contact.Designation, stagingChangeLog);
                existingContact.JobTitle = contact.Designation;

                lstExistingContacts.Add(existingContact);
            }
        }

        private void AddChangeIfDifferent(string fieldName, string oldValue, string newValue, MemberStagingChangeLogResource log)
        {
            if (!string.Equals(oldValue?.Trim(), newValue?.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                log.Changes.Add(new StagingChange
                {
                    Name = fieldName,
                    OldValue = oldValue,
                    NewValue = newValue
                });
            }
        }


        private MemberStagingChangeLogResource CreateStagingChangeLog(Member member, MemberUpdateInputDTO memberUpdateInputDTO)
        {
            if (member != null && memberUpdateInputDTO != null)
            {
                var updateModelType = memberUpdateInputDTO.GetType();
                var oldModelType = member.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> oldProperties = new List<PropertyInfo>(oldModelType.GetProperties());
                MemberStagingChangeLogResource memberStagingChangeLog = new MemberStagingChangeLogResource();
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(memberUpdateInputDTO, null);

                    var oldProp = oldProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (oldProp != null)
                    {
                        object oldPropValue = oldProp.GetValue(member, null);

                        if (oldPropValue != null)
                        {
                            var propType = oldPropValue.GetType();
                            if (propType.IsPrimitive || propType == (typeof(System.String)))
                            {
                                if (updatePropValue.ToString() != oldPropValue.ToString())
                                {
                                    var oldValue = "";
                                    var newValue = "";

                                    //Foreign Keys
                                    if (oldProp.Name == "PrimaryBureauId" || oldProp.Name == "SecondaryBureauId")
                                    {
                                        var oldBureau = Get((int)oldPropValue);
                                        if (oldBureau != null)
                                            oldValue = oldBureau.Name;

                                        var newBureau = Get((int)updatePropValue);
                                        if (newBureau != null)
                                            newValue = newBureau.Name;
                                    }
                                    else
                                    {
                                        oldValue = oldPropValue.ToString();
                                        newValue = updatePropValue.ToString();
                                    }

                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                            else if (propType.IsEnum)
                            {
                                if ((int)updatePropValue != (int)oldPropValue)
                                {
                                    var newValue = _globalHelper.GetEnumValue(oldProp.Name, (int)updatePropValue);
                                    var oldValue = _globalHelper.GetEnumValue(oldProp.Name, (int)oldPropValue);

                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes = stagingChangeList;
                return memberStagingChangeLog;
            }

            return null;
        }

        public BureauGetDTO Get(int id)
        {
            var selectRecord = _dbContext.Set<Bureau>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<BureauGetDTO>(selectRecord);

            return returnRecord;
        }

        internal bool DoMemberChangesRequireApproval(Member member, MemberUpdateInputDTO memberUpdateInputDTO)
        {
            bool doesRequireApproval = false;

            if (member != null && memberUpdateInputDTO != null)
            {
                if (member.IndustryClassificationId != null)
                {
                    if (memberUpdateInputDTO.SacrraIndustryClassId != (int)member.IndustryClassificationId)
                        doesRequireApproval = true;
                }
                else if (member.IndustryClassificationId == null && memberUpdateInputDTO.SacrraIndustryClassId > 0)
                {
                    doesRequireApproval = true;
                }
            }

            return doesRequireApproval;
        }

        private MemberStagingChangeLogResource CreateMemberEventLog(MemberUpdateResource modelForUpdate, Member member, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var memberModelType = member.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> memberProperties = new List<PropertyInfo>(memberModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var memberProp = memberProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (memberProp != null)
                    {
                        object memberPropValue = memberProp.GetValue(member, null);

                        if (memberPropValue != null)
                        {
                            var propType = memberPropValue.GetType();
                            if (propType.IsPrimitive || propType == (typeof(System.String)))
                            {
                                string oldValue = "";
                                string newValue = "";

                                //Foreign Keys
                                if (updateProp.Name == "PrimaryBureauId" || updateProp.Name == "SecondaryBureauId")
                                {
                                    var newBureau = _dbContext.Members
                                        .AsNoTracking()
                                        .FirstOrDefault(i => i.Id == (int)updatePropValue);

                                    if (newBureau != null)
                                        newValue = newBureau.RegisteredName;
                                }
                                else
                                {
                                    newValue = updatePropValue.ToString();
                                }

                                var stagingChange = new StagingChange
                                {
                                    Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                            else if (propType.IsEnum)
                            {
                                var newValue = _globalHelper.GetEnumValue(memberProp.Name, (int)updatePropValue);
                                var oldValue = "";

                                var stagingChange = new StagingChange
                                {
                                    Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }

        private void CreateTradingNamesEventLog(List<MemberTradingNameDTO> tradingNames, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var tradingName in tradingNames)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Member Trading Name",
                        OldValue = "",
                        NewValue = tradingName.Name
                    });
            }
        }
        private void CreateDomainsEventLog(List<MemberDomainInputDTO> domains, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (domains != null)
            {
                foreach (var domain in domains)
                {
                    stagingChangeLog.Changes.Add(
                        new StagingChange
                        {
                            Name = "Member Domain",
                            OldValue = "",
                            NewValue = domain.Name
                        });
                }
            }
        }

        private void CreateALGLeadersEventLog(List<ALGLeaderInputDTO> algLeaders, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (algLeaders != null)
            {
                if (algLeaders.Count > 0)
                {
                    foreach (var leaderId in algLeaders)
                    {
                        var leader = _dbContext.Members.FirstOrDefault(i => i.Id == leaderId.Id);

                        if (leader != null)
                        {
                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "ALG Leader",
                                OldValue = "",
                                NewValue = leader.RegisteredName
                            });
                        }
                    }
                }
            }
        }

        public bool DoesVATNumberExist(MemberRequestInputDTO modelForCreate)
        {
            bool vatExists = false;
            if (!string.IsNullOrWhiteSpace(modelForCreate.VatNumber))
            {
                var selectRecord = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefault(s => s.VatNumber == modelForCreate.VatNumber.Trim());

                if (selectRecord != null)
                {
                    if (selectRecord.Id > 0)
                        vatExists = true;
                }
            }

            return vatExists;
        }
        private void CreateUpdateLog(object updatePropValue, PropertyInfo memberProp, object memberPropValue, List<StagingChange> stagingChangeList)
        {
            string oldValue = "";
            string newValue = "";


            if (updatePropValue == null)
                newValue = "";
            else
            {
                if (memberProp.Name == "NcrCategory")
                {
                    if (updatePropValue != null)
                    {
                        newValue = "N" + updatePropValue.ToString();
                    }
                }
                else
                {
                    newValue = updatePropValue.ToString();
                }
            }

            if (memberPropValue == null)
                oldValue = "";
            else
                oldValue = memberPropValue.ToString();

            if (newValue != oldValue)
            {
                //Foreign Keys
                if (memberProp.Name == "PrimaryBureauId" || memberProp.Name == "SecondaryBureauId")
                {
                    if (!string.IsNullOrEmpty(oldValue))
                    {
                        var oldBureau = _dbContext.Members
                        .AsNoTracking()
                        .FirstOrDefault(i => i.Id == (int)memberPropValue);

                        if (oldBureau != null)
                            oldValue = oldBureau.RegisteredName;
                    }

                    if (!string.IsNullOrEmpty(newValue))
                    {
                        var newBureau = _dbContext.Members
                        .AsNoTracking()
                        .FirstOrDefault(i => i.Id == (int)updatePropValue);

                        if (newBureau != null)
                            newValue = newBureau.RegisteredName;
                    }
                }

                else
                {
                    if (memberProp.Name == "NcrCategory")
                    {
                        if (updatePropValue != null)
                        {
                            newValue = "N" + updatePropValue.ToString();
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(oldValue))
                            oldValue = memberPropValue.ToString();
                        if (!string.IsNullOrEmpty(newValue))
                            newValue = updatePropValue.ToString();
                    }
                }

                var stagingChange = new StagingChange
                {
                    Name = _globalHelper.GetPropertyDisplayName(memberProp),
                    OldValue = oldValue,
                    NewValue = newValue
                };

                stagingChangeList.Add(stagingChange);
            }
        }
    }
}
