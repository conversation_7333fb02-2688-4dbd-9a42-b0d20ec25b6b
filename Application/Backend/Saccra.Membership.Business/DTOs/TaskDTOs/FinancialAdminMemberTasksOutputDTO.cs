using System.Collections.Generic;

namespace Sacrra.Membership.Business.DTOs
{
    public class FinancialAdminMemberTasksOutputDTO
    {
        public string Id { get; set; }
        public string TaskDate { get; set; }
        public string TaskType { get; set; }
        public string Member { get; set; }
        public int MemberId { get; set; }
        public string StakeholderManager { get; set; }
        public string TaskDefinitionKey { get; set; }
        public string? pastelOrAccountNumber { get; set; }
        public string? invoiceDate { get; set; }
        public string? invoiceNumber { get; set; }
        public string? invoiceAmount { get; set; }
        public string? commentOrRefrence { get; set; }
    }
}
