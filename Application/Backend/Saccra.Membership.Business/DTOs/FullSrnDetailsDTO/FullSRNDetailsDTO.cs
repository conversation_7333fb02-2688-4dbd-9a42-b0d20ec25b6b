using System.Collections.Generic;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database.Models;

namespace Saccra.Membership.Business.DTOs.FullSrnDetailsDTO
{
    public class FullSRNDetailsDTO
    {
        public Member Member { get; set; }  
        public List<AccountType> AccountTypes { get; set; }
        public List<IdValuePairResource> NCRReportingAccountTypeClassifications { get; set; }
        public List<LoanManagementSystemVendor> LoanManagementSystemVendors { get; set; }
        public List<SoftwareVendor> SoftwareVendors { get; set; }
        public List<IdValuePairResource> CreditInformationClassifications { get; set; }
        public List<IdValuePairResource> ContactTypesByMembershipType { get; set; }

       
    }
}
