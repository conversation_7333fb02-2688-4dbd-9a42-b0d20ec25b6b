using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saccra.Membership.Business.DTOs.SRNFieldSettingsDTO
{
    public class SrnFieldDto<T>
    {
        public T Value { get; set; }
        public bool IsUpdatable { get; set; }
    }
    public class SRNFieldSettingDTO
    {
        public SrnFieldDto<string> SRNNumber { get; set; }
        public SrnFieldDto<int?> LoanManagementSystemVendorId { get; set; }
        public SrnFieldDto<int?> SoftwareVendorId { get; set; }
        public SrnFieldDto<int?> MemberId { get; set; }
        public SrnFieldDto<int?> AccountTypeId { get; set; }
        public SrnFieldDto<int?> SRNStatusId { get; set; }

    }
}
