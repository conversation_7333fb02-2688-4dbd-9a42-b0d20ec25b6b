using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saccra.Membership.Business.DTOs.SRNUpdateDTOs
{
    public class SRNDetailsDTO
    {
        public int Id { get; set; }

        [Display(Name = "SRN Display Name")]
        [DBAssociation(Name = "TradingName")]
        public string SRNDisplayName { get; set; }

        public List<BranchLocationUpdateResource> BranchLocations { get; set; }

        [Display(Name = "Account Type")]
        public int AccountTypeId { get; set; }

        [Display(Name = "Number of Accounts")]
        [DBAssociation(Name = "NumberOfActiveAccounts")]
        public long NumberOfAccounts { get; set; }

        [Display(Name = "Billing Cycle")]
        [DBAssociation(Name = "BillingCycle")]
        public int BillingCycle { get; set; }

        [Display(Name = "NCR Reporting Account Type Classification")]
        public int? NCRReportingAccountTypeClassificationId { get; set; }

        [Display(Name = "Loan Management System")]
        [DBAssociation(Name = "LoanManagementSystemVendorId")]
        public int? LoanManagementSystemId { get; set; }

        [Display(Name = "Third Party Vendor")]
        [DBAssociation(Name = "SoftwareVendorId")]
        public int? ThirdPartyVendorId { get; set; }

        [Display(Name = "SP Number")]
        [DBAssociation(Name = "SPGroupId")]
        public int? SPNumberId { get; set; }

        [Display(Name = "Credit Information Classification")]
        public int? CreditInformationClassificationId { get; set; }

        public int UserId { get; set; }

    }
}
