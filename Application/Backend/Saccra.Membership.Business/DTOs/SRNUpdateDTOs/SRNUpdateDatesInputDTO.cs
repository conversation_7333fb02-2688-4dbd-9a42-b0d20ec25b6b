using Sacrra.Membership.Database.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Saccra.Membership.Business.DTOs.SRNUpdateDTOs
{
    public class SRNUpdateDatesInputDTO
    {
        public int Id { get; set; }

        [Display(Name = "File Type")]
        public SRNStatusFileTypes FileType { get; set; }

        [Display(Name = "Is Daily File")]
        public bool IsDailyFile { get; set; }

        [Display(Name = "Is Monthly File")]
        public bool IsMonthlyFile { get; set; }

        [Display(Name = "Daily File Development Start Date")]
        public DateTime? DailyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Daily File Development End Date")]
        public DateTime? DailyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Daily File Test Start Date")]
        public DateTime? DailyFileTestStartDate { get; set; }

        [Display(Name = "Daily File Test End Date")]
        public DateTime? DailyFileTestEndDate { get; set; }

        [Display(Name = "Daily File Go Live Date")]
        public DateTime? DailyFileGoLiveDate { get; set; }

        [Display(Name = "Monthly File Development Start Date")]
        public DateTime? MonthlyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Monthly File Development End Date")]
        public DateTime? MonthlyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Monthly File Test Start Date")]
        public DateTime? MonthlyFileTestStartDate { get; set; }

        [Display(Name = "Monthly File Test End Date")]
        public DateTime? MonthlyFileTestEndDate { get; set; }

        [Display(Name = "Monthly File Go Live Date")]
        public DateTime? MonthlyFileGoLiveDate { get; set; }
    }
}
