using System.Collections.Generic;

namespace Sacrra.Membership.Business.DTOs
{
    public class MemberRequestInputDTO
    {
        public string CompanyRegisteredName { get; set; }
        public bool IsSoleProprietor { get; set; }
        public string CompanyRegistrationNumber { get; set; }
        public string IdentificationNumber { get; set; }
        public List<MemberTradingNameDTO> CompanyTradingNames { get; set; }
        public DocumentInputOutputDTO AuditedFinancialDocument { get; set; }
        public DocumentInputOutputDTO IdentificationDocument { get; set; }
        public DocumentInputOutputDTO NCRCertificateDocument { get; set; }
        public bool IsVATRegistered { get; set; }
        public string VatNumber { get; set; }
        public string CompanyWebsite { get; set; }
        public string HeadOfficePhysicalAddress { get; set; }
        public bool isSameAddresses { get; set; }
        public string HeadOfficePostalAddress { get; set; }
        public int sacrraIndustryClassId { get; set; }
        public long? AnnualTurnover { get; set; }
        public int? MembershipTypeId { get; set; }
        public List<ALGLeaderInputDTO> ALGLeaderIds { get; set; }
        public int? ALGLeaderBillingId { get; set; }
        public bool IsNCRRegistrant { get; set; }
        public string NCRCPNumber { get; set; }
        public int? PrincipleDebtRangeId { get; set; }
        public int? NCRFeeCategoryId { get; set; }
        public int? NCRReportingPrimaryBusinessClassificationId { get; set; }
        public int PrimaryBureauId { get; set; }
        public int? SecondaryBureauId { get; set; }
        public string AnalyticsCompanyName { get; set; }
        public List<ContactInputDTO> Contacts { get; set; }
        public string MiscComments { get; set; }
        public string NCRFeeCategory { get; set; }
        public List<MemberDomainInputDTO> Domains { get; set; }
        public bool RequireSRNTesting { get; set; }
    }
}
