using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Camunda.CamundaDTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Camunda.UserServices;

public class NewSrnApplicationUserService
{
    private readonly ConfigSettings _configSettings;
    private readonly AppDbContext _dbContext;
    private readonly IMapper _autoMapper;

    public NewSrnApplicationUserService(IOptions<ConfigSettings> configSettings, AppDbContext dbContext,
        IMapper autoMapper)
    {
        _configSettings = configSettings.Value;
        _dbContext = dbContext;
        _autoMapper = autoMapper;
    }

    private void UpdateSrnStatus(string newStatus, SRN srn)
    {
        if (srn == null) return;

        var newSrnStatus = _dbContext.SRNStatuses
            .AsNoTracking()
            .FirstOrDefault(x => x.Name == newStatus);

        if (newSrnStatus == null)
        {
            throw new Exception($"SRN status {newStatus} was not found.");
        }

        srn.SRNStatusId = newSrnStatus.Id;
        srn.StatusLastUpdatedAt = DateTime.Now;

        if (srn.SRNStatusId != 2)
        {
            var newSrnStatusEntry = new SrnStatusHistory()
            {
                SrnId = srn.Id,
                StatusId = srn.SRNStatusId,
                StatusDate = DateTime.Now,
                StatusReasonId = srn.SRNStatusReasonId
            };

            _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
        }

        _dbContext.SaveChanges();
    }

    private static void CreateEventLog(AppDbContext dbContext, int userId, string changeType, string entityName,
        string entityBlob, string changeBlob, int entityId, string entityTypeName)
    {
        string userFullName;

        if (userId <= 0)
        {
            userFullName = "Internal System";
        }
        else
        {
            var user = dbContext.Users
                .FirstOrDefault(i => i.Id == userId);

            if (user == null)
            {
                throw new Exception($"User ID {userId} was not found.");
            }

            userFullName = user.FirstName + " " + user.LastName;
        }

        var entityTypeId = dbContext.EntityTypes
            .FirstOrDefault(x => x.Name == entityTypeName)
            ?.Id;

        switch (entityTypeId)
        {
            case null:
                throw new Exception($"Entity type {entityTypeName} was not found.");

            case > 0:
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = (int)entityTypeId
                };

                try
                {
                    dbContext.Add(eventLog);
                    dbContext.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw new Exception($"Unable to save event log for {changeType} of {entityName} with ID {entityId}",
                        ex);
                }

                break;
            }
        }
    }

    private TaskGetResource GetCamundaTask(string taskId)
    {
        using var httpClient = new HttpClient();
        var webRequest = new HttpRequestMessage(HttpMethod.Get, _configSettings.CamundaBaseAddress + "/task/" + taskId);
        var httpResult = httpClient.Send(webRequest);
        TaskGetResource taskResource;

        if (httpResult.Content == null)
        {
            throw new Exception("Unable to retrieve camunda task with id " + taskId);
        }

        taskResource = JsonConvert.DeserializeObject<TaskGetResource>(httpResult.Content.ReadAsStringAsync().Result);
        return taskResource;
    }

    private List<VariableInstanceGetResource> GetCamundaTaskVariables(string processInstanceId)
    {
        try
        {
            using var httpClient = new HttpClient();
            var webRequest = new HttpRequestMessage(HttpMethod.Get,
                _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId);
            var httpResult = httpClient.Send(webRequest);
            var variablesResourceList =
                JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(httpResult.Content.ReadAsStringAsync()
                    .Result);

            return variablesResourceList;
        }
        catch (Exception ex)
        {
            throw new Exception("Unable to retrieve variables for process id " + processInstanceId, ex);
        }
    }

    public void CompletePmSrnReviewTask(string taskId, PmSrnReviewInputDto pmSrnReviewInputDto, User user)
    {
        using var httpClient = new HttpClient();
        var selectedTask = GetCamundaTask(taskId);
        var selectedTaskVariables = GetCamundaTaskVariables(selectedTask.ProcessInstanceId);
        var stagingChangeLog = new MemberStagingChangeLogResource();
        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
        {
            {
                "variables",
                new Dictionary<string, Dictionary<string, string>>
                {
                    {
                        "srnVerified1",
                        new Dictionary<string, string>()
                        {
                            { "value", pmSrnReviewInputDto.IsVerified },
                            { "type", "String" }
                        }
                    },
                    {
                        "userId",
                        new Dictionary<string, string>()
                        {
                            { "value", user.Auth0Id },
                            { "type", "string" }
                        }
                    }
                }
            }
        };

        var taskVariablesJson = JsonConvert.SerializeObject(newTaskVariables);
        var webRequest = new HttpRequestMessage(HttpMethod.Post,
            _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete")
        {
            Content = new StringContent(taskVariablesJson, Encoding.UTF8, "application/json")
        };
        var httpResult = httpClient.Send(webRequest);

        httpResult.EnsureSuccessStatusCode();

    }
}