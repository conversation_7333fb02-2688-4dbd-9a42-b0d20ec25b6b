using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Services;

public class MemberUpdateDetailsCamundaService
{
    public IMapper _mapper { get; }
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly GlobalHelper _globalHelper;
    private readonly CamundaServiceHelper _camundaServiceHelper;
    private readonly MemberServiceHelper _memberServiceHelper;

    public MemberUpdateDetailsCamundaService(AppDbContext dbContext, EmailService emailService, GlobalHelper globalHelper, CamundaServiceHelper camundaServiceHelper, MemberServiceHelper memberServiceHelper, IMapper mapper)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _globalHelper = globalHelper;
        _camundaServiceHelper = camundaServiceHelper;
        _memberServiceHelper = memberServiceHelper;
        _mapper = mapper;
    }

    public async Task NotifySHMOfMemberUpdate(int memberId)
    {
        try
        {
            var member = await _dbContext.Members
                .Include(i => i.StakeholderManager)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);

            if (member != null)
            {
                if (member.StakeholderManager != null)
                {
                    var shm = member.StakeholderManager;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                    };

                    _emailService.SendEmail(shm.Email, shm.FirstName, "Member Updated - No Approval Required",
                        "MemberUpdateNoApprovalSHM.html", placeholders);
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email SHM for member update. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void UpdateMemberRequestDetails(int memberId, ExternalTaskInfo camundaTask)
    {
        try
        {
            var variables = _globalHelper.GetVariablesAsync(camundaTask.ProcessInstanceId).Result;
            var reviewDescision = variables.Find(x => x.Name == "memberChangesDecision").Value;
            var userId = variables.Find(x => x.Name == "userId").Value;

            if (variables.Count > 0)
            {
                var memberVariables = variables.FirstOrDefault(i => i.Name == "ChangeRequestId");
                var requestId = (memberVariables != null) ? memberVariables.Value : "0";
                
                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = _camundaServiceHelper.GetMemberChangeRequest(Convert.ToInt32(requestId)).Result;
                    if (changeRequest != null)
                    {
                        memberId = changeRequest.ObjectId;

                        var member = _dbContext.Members
                            .Include(x => x.TradingNames)
                            .AsNoTracking()
                            .FirstOrDefault(i => i.Id == memberId);

                        var user = _globalHelper.GetUserByAuth0Id(userId);

                        if (reviewDescision == "accepted")
                        {
                            if (member != null)
                            {
                                if (changeRequest != null)
                                {
                                    changeRequest.Status = ChangeRequestStatus.Accepted;
                                    _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                                }

                                var modelForUpdate = JsonConvert.DeserializeObject<MemberUpdateInputDTO>(changeRequest.UpdatedDetailsBlob);
                                _memberServiceHelper.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate, user);
                                _camundaServiceHelper.NotifyApplicantOfMemberUpdateAccepted(member.Id);
                            }
                        }

                        else if (reviewDescision == "rejected")
                        {
                            _camundaServiceHelper.NotifyApplicantOfMemberUpdateDecline(member.Id);
                        }

                        _dbContext.Remove(changeRequest);
                    }
                }

                _dbContext.SaveChanges();
            }
        }
        catch (Exception ex)
        {
            var message = $"Unable to update request. Member Id {memberId}";
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw;
        }
    }
}