using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Camunda.Api.Client.User;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Camunda.CamundaServices;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NewSrnApplicationCamundaAgent : BaseCamundaAgent
{
    private readonly CamundaClient _camundaClient;
    public NewSrnApplicationCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
        _camundaClient = camundaClient;
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "create-srn-number":
                await CreateSrnNumber(task, completeExternalTask, serviceScope);
                break;

            case "allocate-stake-holder-manager":
                await AllocateStakeholderManager(task, completeExternalTask, serviceScope);
                break;

            case "notify-applicant-of-srn-application-rejection":
                await NotifyApplicantOfSrnApplicationRejection(task, serviceScope);
                break;

            case "email-bureaus-before-srn-testing":
                await EmailBureausBeforeSrnTesting(task, completeExternalTask, serviceScope);
                break;

            case "update-srn-status-to-live":
                await UpdateSrnStatusToLiveNoTestingRequired(task, serviceScope);
                break;

            case "email-member":
                await EmailMember(task, completeExternalTask, serviceScope);
                break;

            case "call_file_test_subprocess":
                await CallFileTestSubprocess(task, completeExternalTask, serviceScope);
                break;

            case "email-bureaus-on-srn-go-live":
                await EmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;

            case "update-srn-application":
                UpdateSrnApplication(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task<UserProfileInfo> getRandomUser(string group)
    {
        var usersInGroup = await _camundaClient.Users.Query(new UserQuery()
        {
            MemberOfGroup = group
        }).List();
        var randomUser = usersInGroup.PickRandom();

        return randomUser;
    }

    private async Task EmailBureausOnSrnGoLive(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = await getRandomUser("SACRRAAdministrator");

        await GetNewSrnApplicationCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private async Task CallFileTestSubprocess(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        await GetNewSrnApplicationCamundaService(serviceScope).CallFileTestSubprocess(task, serviceScope);
    }

    private async Task EmailMember(ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = await GetRandomUser("SACRRAAdministrator");

        GetNewSrnApplicationCamundaService(serviceScope).EmailMember(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private static async Task UpdateSrnStatusToLiveNoTestingRequired(ExternalTaskResource task,
        IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        // Looks like the stakeHolderManagerAssignee is the last person to interact with the workflow
        // so the updatedByUserId is the stakeHolderManagerAssignee
        var updatedbyUserId = await GetIntegerVariable(task, "stakeHolderManagerAssignee", serviceScope);
        var taskInfo = await task.Get();

        if (taskInfo == null)
            throw new Exception("Task information is null");

        await GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatusToLiveNoTestingRequired(task, srnId, "Live", updatedbyUserId);
    }

    private static async Task EmailBureausBeforeSrnTesting(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).EmailBureausBeforeSrnTesting(srnId, task);
    }

    private static async Task NotifyApplicantOfSrnApplicationRejection(ExternalTaskResource task, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        // GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatus("Rejected", srnId);
        await GetNewSrnApplicationCamundaService(serviceScope).NotifyApplicantOfSrnApplicationRejection(srnId);
    }

    private static async Task AllocateStakeholderManager(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetIntegerVariable(task, "MemberId", serviceScope);
        var member = await GetNewSrnApplicationCamundaService(serviceScope).GetMember(memberId);
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        var processInstanceId = await GetGenericVariable(task, "ProcessInstanceId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatusToPmVerification(srnId, processInstanceId);

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["stakeHolderManagerAssignee"] = VariableValue.FromObject(member.StakeholderManager.Id.ToString())
        };
    }

    private void UpdateSrnApplication(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var selectedTask = task.Get().Result;
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope).Result;

        GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnApplication(selectedTask, srnId);
    }

    private static async Task CreateSrnNumber(ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).CreateSrnNumber(srnId);
    }

    private static NewSrnApplicationCamundaService GetNewSrnApplicationCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<NewSrnApplicationCamundaService>();
    }
}