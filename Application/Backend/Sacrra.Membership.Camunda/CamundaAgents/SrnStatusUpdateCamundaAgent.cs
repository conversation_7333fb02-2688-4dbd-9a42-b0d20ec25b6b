using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Models;
using System;
using System.Threading.Tasks;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateCamundaAgent : BaseCamundaAgent
{
    public SrnStatusUpdateCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "email-member-about-SRN-pending-closure":
                await EmailMemberAboutSrnPendingClosure(task, completeExternalTask, serviceScope);
                break;

            case "email-bureaus-about-srn-pending-closure":
                await EmailBureausAboutSrnPendingClosure(task, completeExternalTask, serviceScope);
                break;

            case "update-srn-status-to-pending-closure":
                await UpdateSrnStatusToPendingClosure(task, completeExternalTask, serviceScope);
                break;

            case "update-srn-status-to-closed":
                await UpdateSrnStatusToClosed(task, completeExternalTask, serviceScope);
                break;

            case "email-bureaus-to-close-the-payment-profile":
                await EmailBureausToCloseThePaymentProfile(task, completeExternalTask, serviceScope);
                break;

            case "email-member-to-confirm-srn-closure":
                await EmailMemberToConfirmSrnClosure(task, completeExternalTask, serviceScope);
                break;

            case "kick-off-member-auto-close-workflow-if-member-has-no-active-SRNs":
                await KickOffMemberAutoCloseWorkflowIfMemberHasNoActiveSrns(task, completeExternalTask, serviceScope);
                break;

            case "set-srn-status-date-to-todays-date":
                await SetSrnStatusDateToTodaysDate(task, completeExternalTask, serviceScope);
                break;

            case "kill_file_test_tasks":
                KillFileTestTasks(task, completeExternalTask, serviceScope);
                break;

            case "update-confirm-submission":
                await UpdateConfirmSubmission(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private void KillFileTestTasks(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        throw new NotImplementedException();
    }

    private async Task SetSrnStatusDateToTodaysDate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).SetSRNStatusDateToCurrentDate(Convert.ToInt32(srnId));
        }
    }

    private async Task KickOffMemberAutoCloseWorkflowIfMemberHasNoActiveSrns(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        await GetSrnStatusUpdateCamundaService(serviceScope).KickoffMemberAutoCloseWorkflowIfMemberHasNoActiveSRNs(srnId);
    }

    private async Task EmailMemberToConfirmSrnClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).EmailMemberToConfirmClosure(Convert.ToInt32(srnId));
        }
    }

    private async Task EmailBureausToCloseThePaymentProfile(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).EmailBureausToClosePaymentProfile(Convert.ToInt32(srnId));
        }
    }

    private async Task UpdateSrnStatusToClosed(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            var taskInfo = await task.Get();

            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }

            await GetSrnStatusUpdateCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), "Closed", 0, processInstanceId);
        }
    }

    private async Task UpdateSrnStatusToPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
        var taskInfo = await task.Get();
        var processInstanceId = string.Empty;

        if (taskInfo != null)
        {
            processInstanceId = taskInfo.ProcessInstanceId;
        }

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), "Closure Pending", 0, processInstanceId);
        }
    }

    private async Task EmailBureausAboutSrnPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).EmailBureausAboutSRNPendingClosure(Convert.ToInt32(srnId));
        }
    }

    private async Task EmailMemberAboutSrnPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);

        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateCamundaService(serviceScope).EmailMemberAboutSRNPendingClosure(Convert.ToInt32(srnId));
        }
    }

    private async Task UpdateConfirmSubmission(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
        var TaskInfo = await task.Get();


        await GetSrnStatusUpdateCamundaService(serviceScope).UpdateConfirmSubmission(int.Parse(srnId), TaskInfo.ProcessInstanceId);
    }


    private static SrnStatusUpdateCamundaService GetSrnStatusUpdateCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateCamundaService>();
    }
}