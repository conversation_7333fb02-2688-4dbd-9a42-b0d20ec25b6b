using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NewMemberTakeonCamundaAgent : BaseCamundaAgent
{
    public NewMemberTakeonCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "disqualify-member-and-email":
                await DisqualifyMemberAndEmail(task, completeExternalTask, serviceScope);
                break;

            case "allocate-stake-holder-administrator":
                await AllocateStakeHolderAdministrator(task, completeExternalTask, serviceScope);
                break;

            case "member-approve":
                await <PERSON><PERSON><PERSON><PERSON>(task, completeExternalTask, serviceScope);
                break;

            case "notify-member-of-application-approval":
                await NotifyMemberOfApplicationApproval(task, completeExternalTask, serviceScope);
                break;

            case "new-membership-update-member-status":
                await NewMembershipUpdateMemberStatus(task, completeExternalTask, serviceScope);
                break;

            case "notify-ALG-leader-on-client-takeon":
                await NotifyALGLeaderOnClientTakeon(task, completeExternalTask, serviceScope);
                break;

            case "notify-financial-admin-and-info-to-create-invoice":
                await NotifyFinancialAdminOfNewInvoice(task, completeExternalTask, serviceScope);
                break;

            case "update-member-to-payment-pending-full-member-invoice":
                await UpdateMemberPaymentPending(task, completeExternalTask, serviceScope);
                break;

            case "update-member-to-payment-pending-for-assessment-invoice":
                await UpdateMemberPaymentPending(task, completeExternalTask, serviceScope);
                break;

            case "update-member-to-payment-pending-for-onboarding-invoice":
                await UpdateMemberPaymentPending(task, completeExternalTask, serviceScope);
                break;

            case "save-non-member-invoice-details":
            case "save-assessment-invoice-details":
            case "save-full-member-invoice-details":
                SaveInvoiceDetails(task, completeExternalTask, serviceScope);
                break;

            case "save-non-member-invoice-paid-details":
            case "save-assessment-invoice-paid-details":
            case "save-full-member-invoice-paid-details":
                SavePaymentDetails(task, completeExternalTask, serviceScope);
                break;

            case "update-member-after-acceptance-of-constitution-review":
                await UpdateMemberAfterInvoicePaid(task, completeExternalTask, serviceScope, topicName);
                break;

            case "update-member-after-assessment-invoice-paid":
                await UpdateMemberAfterInvoicePaid(task, completeExternalTask, serviceScope, topicName);
                break;

            case "update-member-after-onboarding-invoice-paid":
                await UpdateMemberAfterInvoicePaid(task, completeExternalTask, serviceScope, topicName);
                break;

            case "send-member-assessment-payment-reminder":
            case "send-member-application-cancellation-email":
            case "send-payment-and-constitution-reminder":
            case "send-member-onboarding-payment-reminder":
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task NotifyALGLeaderOnClientTakeon(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyALGLeaderOnClientRegistration(memberId);
    }

    private async Task NewMembershipUpdateMemberStatus(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationCompleted, memberId);
    }

    private async Task NotifyMemberOfApplicationApproval(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private async Task NotifyFinancialAdminOfNewInvoice(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyFinancialAdminOfNewInvoice(memberId);
    }

    private async Task MemberApprove(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationApproved, memberId);
        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private async Task AllocateStakeHolderAdministrator(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var randomUser = await GetRandomUser("StakeHolderAdministrator");

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["stakeHolderManagerManagerAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private async Task DisqualifyMemberAndEmail(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationDisqualification(memberId);
    }

    private async Task UpdateMemberPaymentPending(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        var getTask = await task.Get();
        string topicName = getTask.TopicName;

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatusToPaymentPending(memberId, topicName);
    }

    private async Task UpdateMemberAfterInvoicePaid(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope, string topicName)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        var memberTask = await task.Get();
        var memberVariables = await GetCamundaRepository(serviceScope).GetMemberVariables(memberTask.ProcessInstanceId);

        string paymentStatus = "";

        switch (topicName)
        {
            case "update-member-after-acceptance-of-constitution-review":
                var paymentStatusforFullMember = memberVariables.FirstOrDefault(i => i.Name == "fullMemberPaymentReceived");
                paymentStatus = paymentStatusforFullMember != null ? paymentStatusforFullMember.Value : "";
                break;

            case "update-member-after-assessment-invoice-paid":
                var paymentStatusForAssessmentInvoice = memberVariables.FirstOrDefault(i => i.Name == "initialAssessmentInvoicePaid");
                paymentStatus = paymentStatusForAssessmentInvoice != null ? paymentStatusForAssessmentInvoice.Value : "";
                break;

            case "update-member-after-onboarding-invoice-paid":
                var paymentStatusforOnboardingInvoice = memberVariables.FirstOrDefault(i => i.Name == "onboardingPaymentReceived");
                paymentStatus = paymentStatusforOnboardingInvoice != null ? paymentStatusforOnboardingInvoice.Value : "";
                break;
        }

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberAfterInvoicePaid(memberId, paymentStatus);
    }

    private void SaveInvoiceDetails(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope).Result;
        var memberTask = task.Get().Result;
        var memberVariables = GetCamundaRepository(serviceScope).GetMemberVariables(memberTask.ProcessInstanceId).Result;

        GetNewMemberTakeonCamundaService(serviceScope).SaveInvoiceDetails(memberId, memberVariables);
    }

    private void SavePaymentDetails(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope).Result;
        var memberTask = task.Get().Result;
        var memberVariables = GetCamundaRepository(serviceScope).GetMemberVariables(memberTask.ProcessInstanceId).Result;

        GetNewMemberTakeonCamundaService(serviceScope).SavePaymentDetails(memberId, memberVariables);
    }

    private static NewMemberTakeonCamundaService GetNewMemberTakeonCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<NewMemberTakeonCamundaService>();
    }
}