using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class AdHocFileSubmissionsCamundaAgent : BaseCamundaAgent
    {
        public AdHocFileSubmissionsCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "update-adhoc-file-status":
                    await UpdateAdHocFileStatus(task, completeExternalTask, serviceScope);
                    break;
                case "adhoc_file_submission_update_status_declined":
                    await AdhocFileSubmissionUpdateStatusDeclined(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_check_file_submitted_to_dth":
                    await AdhocFileSubmissionCheckFileSubmittedToDth(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_update_status_approved":
                    await AdhocFileSubmissionUpdateStatusApproved(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_bureaus_approved":
                    await AdhocFileSubmissionEmailMemberBureausApproved(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_create_file_ftpserver":
                    await AdhocFileSubmissionCreateFileFtpserver(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_declined":
                    await AdhocFileSubmissionEmailMemberDeclined(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_update_submitted_cancelled":
                    await AdhocFileSubmissionUpdateSubmittedCancelled(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_bureaus_cancelled":
                    await AdhocFileSubmissionEmailMemberBureausCancelled(task, completeExternalTask, serviceScope);
                    break;
            }
            
            await task.Complete(completeExternalTask);
        }

        private async Task UpdateAdHocFileStatus(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).UpdateFileStatus(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionEmailMemberBureausCancelled(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberBureausCancelled(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionUpdateSubmittedCancelled(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            await GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionUpdateFileSubmissionToSubmittedOrCancelled(camundaTask);
        }

        private async Task AdhocFileSubmissionEmailMemberDeclined(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberDeclined(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionCreateFileFtpserver(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionCreateFileOnFTPServer(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionEmailMemberBureausApproved(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberApproved(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionUpdateStatusDeclined(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).UpdateAdHocFileSubmissionStatusToDeclined(camundaTask.ProcessInstanceId);
        }

        private async Task AdhocFileSubmissionCheckFileSubmittedToDth(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionCheckFileSubmittedToDTH(camundaTask);
        }

        private async Task AdhocFileSubmissionUpdateStatusApproved(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = await task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).UpdateAdHocFileSubmissionStatusToApproved(camundaTask.ProcessInstanceId);
        }
        
        private static AdHocFileSubmissionsCamundaService GetAdHocFileSubmissionsCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<AdHocFileSubmissionsCamundaService>();
        }
    }
}