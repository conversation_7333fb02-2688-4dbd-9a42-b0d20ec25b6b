<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="Definitions_0ljajin" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_0gjfm6r">
    <bpmn:participant id="Participant_0pry0ja" name="Member Application" processRef="New-Member-Takeon" />
  </bpmn:collaboration>
  <bpmn:process id="New-Member-Takeon" isExecutable="true" camunda:versionTag="V3.0" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_0lsym2h">
      <bpmn:lane id="Lane_00h5nay" name="Group SHM">
        <bpmn:flowNodeRef>Task_allocateStakeHolderManagerManager</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_financialAdministrator" name="Financial Administrator">
        <bpmn:flowNodeRef>SubProcess_fullMemberSubProcess</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>SubProcess_NonMemberProcess</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>SubProcess_InitialAssessmentInvoice</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>BoundaryEvent_1b24cgj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>capture-cancel-full-member</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>CancelNonMemberBoundEvent</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>BoundaryEvent_0yh8kyy</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_stakeHolderManager" name="Stake Holder Manager">
        <bpmn:flowNodeRef>EndEvent_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_disqualifyMember</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_0qrrie9</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_ReviewMemberApplication</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_qualify</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_shmFinalReview</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_09csx0h</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_applicant" name="Applicantmber Take">
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_allocateGroupStakeholderManager</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0g1ewho</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application form completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="OrganisationID" label="Organisation ID" type="long" />
          <camunda:formField id="OrganisationName" label="Organisation Name" type="string" />
          <camunda:formField id="membershipType" type="enum">
            <camunda:value id="nonMember" name="Non Member" />
            <camunda:value id="fullMember" name="Full Member" />
            <camunda:value id="algClient" name="ALG Client" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1w7teci</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_complete" name="Application complete">
      <bpmn:incoming>SequenceFlow_0fuauda</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0bhnrrg</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1fo2ycc</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0ktrdss</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_disqualifyMember" name="Disqualify applicant and email" camunda:type="external" camunda:topic="disqualify-member-and-email">
      <bpmn:incoming>SequenceFlow_0sdz2lw</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1civem7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_0qrrie9">
      <bpmn:incoming>SequenceFlow_1civem7</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1j82rzu</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_allocateGroupStakeholderManager" name="Allocate to Group Stakeholder Manager" camunda:type="external" camunda:topic="allocate-stake-holder-administrator">
      <bpmn:incoming>SequenceFlow_0cu0ave</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1o947iw</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wkn6l0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_allocateStakeHolderManagerManager" name="Group Stakeholder Manager Allocates SHM" camunda:assignee="${stakeHolderManagerManagerAssignee}" camunda:candidateGroups="StakeHolderAdministrator">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="stakeHolderManagerAssignee" label="Select stakeholder manager" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1wkn6l0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07az7ab</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ReviewMemberApplication" name="Review and facilitate a Member Application&#10;" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="reviewApplicationDecision" label="What is the review decision?" type="enum" defaultValue="disqualified">
            <camunda:value id="disqualified" name="Disqualified" />
            <camunda:value id="nonMember" name="Non-Member" />
            <camunda:value id="fullMember" name="Full Member" />
            <camunda:value id="algClient" name="ALG Client" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07az7ab</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0kb0gt3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xmntch</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_qualify" name="Review Decision">
      <bpmn:incoming>SequenceFlow_0xmntch</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1shf565</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_02smnwx</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0sdz2lw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_03xpzxf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_06n25mm" sourceRef="SubProcess_NonMemberProcess" targetRef="Task_shmFinalReview" />
    <bpmn:sequenceFlow id="SequenceFlow_0cu0ave" sourceRef="SubProcess_InitialAssessmentInvoice" targetRef="Task_allocateGroupStakeholderManager" />
    <bpmn:sequenceFlow id="SequenceFlow_0bndmog" sourceRef="BoundaryEvent_1b24cgj" targetRef="Task_shmFinalReview" />
    <bpmn:sequenceFlow id="SequenceFlow_0ktrdss" sourceRef="BoundaryEvent_0yh8kyy" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1fo2ycc" sourceRef="capture-cancel-full-member" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_0bhnrrg" sourceRef="CancelNonMemberBoundEvent" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1civem7" sourceRef="Task_disqualifyMember" targetRef="EndEvent_0qrrie9" />
    <bpmn:sequenceFlow id="SequenceFlow_0sdz2lw" name="Disqualified" sourceRef="ExclusiveGateway_qualify" targetRef="Task_disqualifyMember">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewApplicationDecision == "disqualified"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_02smnwx" name="Full" sourceRef="ExclusiveGateway_qualify" targetRef="SubProcess_fullMemberSubProcess">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewApplicationDecision == "fullMember"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1shf565" name="Non" sourceRef="ExclusiveGateway_qualify" targetRef="SubProcess_NonMemberProcess">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewApplicationDecision == "nonMember"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0fuauda" sourceRef="Task_complete" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1lkosa1" sourceRef="Task_shmFinalReview" targetRef="Task_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1fghq34" sourceRef="SubProcess_fullMemberSubProcess" targetRef="Task_shmFinalReview" />
    <bpmn:sequenceFlow id="SequenceFlow_0xmntch" sourceRef="Task_ReviewMemberApplication" targetRef="ExclusiveGateway_qualify" />
    <bpmn:sequenceFlow id="SequenceFlow_07az7ab" sourceRef="Task_allocateStakeHolderManagerManager" targetRef="Task_ReviewMemberApplication" />
    <bpmn:sequenceFlow id="SequenceFlow_1wdg5he" name="Non Member" sourceRef="ExclusiveGateway_0g1ewho" targetRef="SubProcess_InitialAssessmentInvoice">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${membershipType == "nonMember"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1w7teci" sourceRef="StartEvent_applicationFormCompleted" targetRef="ExclusiveGateway_0g1ewho" />
    <bpmn:sequenceFlow id="SequenceFlow_1wkn6l0" sourceRef="Task_allocateGroupStakeholderManager" targetRef="Task_allocateStakeHolderManagerManager" />
    <bpmn:subProcess id="SubProcess_fullMemberSubProcess" name="Full Member Sub Process">
      <bpmn:incoming>SequenceFlow_02smnwx</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1fghq34</bpmn:outgoing>
      <bpmn:serviceTask id="Task_approve" name="Appove and email member" camunda:type="external" camunda:topic="member-approve">
        <bpmn:incoming>SequenceFlow_0sh548s</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_020ch0h</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="ExclusiveGateway_wasPaymentReceived" name="Payment received?">
        <bpmn:incoming>SequenceFlow_04pqhgj</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0sh548s</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0q8npmq</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_06mnisl</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Task_18d4zpq" name="Payment and Constitution Reminder" camunda:type="external" camunda:topic="send-payment-and-constitution-reminder">
        <bpmn:incoming>SequenceFlow_06mnisl</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1xk2w0q</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:userTask id="Task_checkIfPaymentReceived" name="Check payment and acceptance of constitution review" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:extensionElements>
          <camunda:formData>
            <camunda:formField id="fullMemberPaymentReceived" label="Payment Status" type="enum">
              <camunda:value id="notReceived" name="Not Received" />
              <camunda:value id="received" name="Received" />
              <camunda:value id="pending" name="Pending" />
            </camunda:formField>
          </camunda:formData>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_1ede031</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_0m9sqzf</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_04pqhgj</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:userTask id="Task_generateInvoice" name="Generate full member invoice&#10;" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_01jesl4</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1ede031</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:startEvent id="StartEvent_fullMemberApplication" name="Full Member  App Started">
        <bpmn:outgoing>SequenceFlow_01jesl4</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_01jesl4" sourceRef="StartEvent_fullMemberApplication" targetRef="Task_generateInvoice" />
      <bpmn:sequenceFlow id="SequenceFlow_1ede031" sourceRef="Task_generateInvoice" targetRef="Task_checkIfPaymentReceived" />
      <bpmn:sequenceFlow id="SequenceFlow_04pqhgj" sourceRef="Task_checkIfPaymentReceived" targetRef="ExclusiveGateway_wasPaymentReceived" />
      <bpmn:sequenceFlow id="SequenceFlow_0sh548s" name="Yes" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_approve">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fullMemberPaymentReceived == "received"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_0q8npmq" name="No" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="EndEvent_CancelFullMemberApplication">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fullMemberPaymentReceived == "notReceived"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_020ch0h" sourceRef="Task_approve" targetRef="EndEvent_0bam2or" />
      <bpmn:endEvent id="EndEvent_0bam2or">
        <bpmn:incoming>SequenceFlow_020ch0h</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="SequenceFlow_06mnisl" name="Pending" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_18d4zpq">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fullMemberPaymentReceived == "pending"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_1xk2w0q" sourceRef="Task_18d4zpq" targetRef="IntermediateThrowEvent_1g401ap" />
      <bpmn:sequenceFlow id="SequenceFlow_0m9sqzf" sourceRef="IntermediateThrowEvent_1g401ap" targetRef="Task_checkIfPaymentReceived" />
      <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1g401ap">
        <bpmn:incoming>SequenceFlow_1xk2w0q</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0m9sqzf</bpmn:outgoing>
        <bpmn:timerEventDefinition>
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:endEvent id="EndEvent_CancelFullMemberApplication" name="Cancel Full Member Application">
        <bpmn:incoming>SequenceFlow_0q8npmq</bpmn:incoming>
        <bpmn:errorEventDefinition errorRef="Error_12uq10m" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:subProcess id="SubProcess_NonMemberProcess" name="Non Member Finance Processs">
      <bpmn:incoming>SequenceFlow_1shf565</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_06n25mm</bpmn:outgoing>
      <bpmn:startEvent id="StartEvent_NonMemberApplication" name="Non-Member  App Started">
        <bpmn:outgoing>SequenceFlow_01n3jrq</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:userTask id="UserTask_0gomw05" name="Create onboarding Invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_01n3jrq</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1urh21r</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_0zk0oqd">
        <bpmn:incoming>SequenceFlow_1urh21r</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_13e9qb2</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_09uoa55</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0lg8uoe">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:endEvent id="EndEvent_1tviaau">
        <bpmn:incoming>SequenceFlow_08wf8j2</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:exclusiveGateway id="ExclusiveGateway_02z37ea" name="Is Paid?">
        <bpmn:incoming>SequenceFlow_1u0awu9</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1fkbr8a</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0iiiauc</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0vk45il</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="ServiceTask_cancelAndEmailApplicant" name="Cancel &#38; Email Applicant" camunda:expression="non-member-cancel-and-email-applicant">
        <bpmn:incoming>SequenceFlow_0vk45il</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1vfex3k</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_1urh21r" sourceRef="UserTask_0gomw05" targetRef="IntermediateCatchEvent_0zk0oqd" />
      <bpmn:sequenceFlow id="SequenceFlow_09uoa55" sourceRef="IntermediateCatchEvent_0zk0oqd" targetRef="Task_0tatwfs" />
      <bpmn:sequenceFlow id="SequenceFlow_1u0awu9" sourceRef="Task_0tatwfs" targetRef="ExclusiveGateway_02z37ea" />
      <bpmn:sequenceFlow id="SequenceFlow_1fkbr8a" name="Pending" sourceRef="ExclusiveGateway_02z37ea" targetRef="Task_1dl0r1t">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${onboardingPaymentReceived == "pending"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_0iiiauc" name="Yes" sourceRef="ExclusiveGateway_02z37ea" targetRef="Task_1exlbax">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${onboardingPaymentReceived == "yes"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_0vk45il" name="No" sourceRef="ExclusiveGateway_02z37ea" targetRef="ServiceTask_cancelAndEmailApplicant">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${onboardingPaymentReceived == "no"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:userTask id="Task_0tatwfs" name="Check if onboarding invoice paid" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:extensionElements>
          <camunda:formData>
            <camunda:formField id="onboardingPaymentReceived" type="enum">
              <camunda:value id="no" name="No" />
              <camunda:value id="yes" name="Yes" />
              <camunda:value id="pending" name="Pending" />
            </camunda:formField>
          </camunda:formData>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_09uoa55</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1u0awu9</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:serviceTask id="Task_1dl0r1t" name="Send payment reminder" camunda:type="external" camunda:topic="send-member-onboarding-payment-reminder">
        <bpmn:incoming>SequenceFlow_1fkbr8a</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_13e9qb2</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_13e9qb2" sourceRef="Task_1dl0r1t" targetRef="IntermediateCatchEvent_0zk0oqd" />
      <bpmn:sequenceFlow id="SequenceFlow_01n3jrq" sourceRef="StartEvent_NonMemberApplication" targetRef="UserTask_0gomw05" />
      <bpmn:sequenceFlow id="SequenceFlow_08wf8j2" sourceRef="Task_1exlbax" targetRef="EndEvent_1tviaau" />
      <bpmn:serviceTask id="Task_1exlbax" name="Email member" camunda:type="external" camunda:topic="notify-member-of-application-approval">
        <bpmn:incoming>SequenceFlow_0iiiauc</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_08wf8j2</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_1vfex3k" sourceRef="ServiceTask_cancelAndEmailApplicant" targetRef="EndEvent_CancelNonMemberApplication" />
      <bpmn:endEvent id="EndEvent_CancelNonMemberApplication" name="Cancel Non Member Application">
        <bpmn:incoming>SequenceFlow_1vfex3k</bpmn:incoming>
        <bpmn:errorEventDefinition errorRef="Error_12uq10m" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="SequenceFlow_1o947iw" name="Full Member" sourceRef="ExclusiveGateway_0g1ewho" targetRef="Task_allocateGroupStakeholderManager">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${membershipType == "fullMember"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0g1ewho" name="Is Full/Non Member?">
      <bpmn:incoming>SequenceFlow_1w7teci</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wdg5he</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1o947iw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0kb0gt3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_shmFinalReview" name="Update member status" camunda:type="external" camunda:topic="new-membership-update-member-status">
      <bpmn:incoming>SequenceFlow_06n25mm</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1fghq34</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0bndmog</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1lkosa1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:manualTask id="Task_complete" name="Complete final take on">
      <bpmn:incoming>SequenceFlow_1lkosa1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0fuauda</bpmn:outgoing>
    </bpmn:manualTask>
    <bpmn:subProcess id="SubProcess_InitialAssessmentInvoice" name="Assessment Invoice Process">
      <bpmn:incoming>SequenceFlow_1wdg5he</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0cu0ave</bpmn:outgoing>
      <bpmn:startEvent id="StartEvent_1qggjo9">
        <bpmn:outgoing>SequenceFlow_0wwquoa</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:userTask id="UserTask_0lowgau" name="Generate assessment invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_0wwquoa</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0aqtl15</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_18zptnn">
        <bpmn:incoming>SequenceFlow_0aqtl15</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_07b2i1i</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_194wie8</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0d2ev40">
          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      <bpmn:userTask id="UserTask_0zkahxr" name="Check if assessement invoice paid" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:extensionElements>
          <camunda:formData>
            <camunda:formField id="initialAssessmentInvoicePaid" type="enum">
              <camunda:value id="yes" name="Yes" />
              <camunda:value id="no" name="No" />
              <camunda:value id="pending" name="Pending" />
            </camunda:formField>
          </camunda:formData>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_194wie8</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1m0cz42</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:exclusiveGateway id="ExclusiveGateway_0ew5w3l" name="Is Paid?">
        <bpmn:incoming>SequenceFlow_1m0cz42</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0b9nzkv</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_18itr3o</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0xzp15x</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="ServiceTask_1kupf1l" name="Send Application Cancellation Email" camunda:type="external" camunda:topic="send-member-application-cancellation-email">
        <bpmn:incoming>SequenceFlow_18itr3o</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0oj546i</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="EndEvent_0c3d83q">
        <bpmn:incoming>SequenceFlow_0b9nzkv</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="SequenceFlow_0wwquoa" sourceRef="StartEvent_1qggjo9" targetRef="UserTask_0lowgau" />
      <bpmn:sequenceFlow id="SequenceFlow_0aqtl15" sourceRef="UserTask_0lowgau" targetRef="IntermediateCatchEvent_18zptnn" />
      <bpmn:sequenceFlow id="SequenceFlow_194wie8" sourceRef="IntermediateCatchEvent_18zptnn" targetRef="UserTask_0zkahxr" />
      <bpmn:sequenceFlow id="SequenceFlow_1m0cz42" sourceRef="UserTask_0zkahxr" targetRef="ExclusiveGateway_0ew5w3l" />
      <bpmn:sequenceFlow id="SequenceFlow_0b9nzkv" name="Yes" sourceRef="ExclusiveGateway_0ew5w3l" targetRef="EndEvent_0c3d83q">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${initialAssessmentInvoicePaid == "yes"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_18itr3o" name="No" sourceRef="ExclusiveGateway_0ew5w3l" targetRef="ServiceTask_1kupf1l">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${initialAssessmentInvoicePaid == "no"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_07b2i1i" sourceRef="Task_0hwrm6w" targetRef="IntermediateCatchEvent_18zptnn" />
      <bpmn:serviceTask id="Task_0hwrm6w" name="Send payment reminder" camunda:type="external" camunda:topic="send-member-assessment-payment-reminder">
        <bpmn:incoming>SequenceFlow_0xzp15x</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_07b2i1i</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_0xzp15x" name="Pending" sourceRef="ExclusiveGateway_0ew5w3l" targetRef="Task_0hwrm6w">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${initialAssessmentInvoicePaid == "pending"}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="SequenceFlow_0oj546i" sourceRef="ServiceTask_1kupf1l" targetRef="EndEvent_CancelNonMemberApplicationAssessment" />
      <bpmn:endEvent id="EndEvent_CancelNonMemberApplicationAssessment" name="Cancel Non Member Application">
        <bpmn:incoming>SequenceFlow_0oj546i</bpmn:incoming>
        <bpmn:errorEventDefinition errorRef="Error_1cbi7eo" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:boundaryEvent id="BoundaryEvent_1b24cgj" name="Overide Message&#10;" attachedToRef="SubProcess_InitialAssessmentInvoice">
      <bpmn:outgoing>SequenceFlow_0bndmog</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1j9r7au" messageRef="Message_025sw22" />
    </bpmn:boundaryEvent>
    <bpmn:serviceTask id="Task_09csx0h" name="Notify ALG Leader" camunda:type="external" camunda:topic="notify-ALG-leader-on-client-takeon">
      <bpmn:incoming>SequenceFlow_03xpzxf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1j82rzu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_03xpzxf" name="ALG" sourceRef="ExclusiveGateway_qualify" targetRef="Task_09csx0h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewApplicationDecision == "algClient"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1j82rzu" sourceRef="Task_09csx0h" targetRef="EndEvent_0qrrie9" />
    <bpmn:sequenceFlow id="SequenceFlow_0kb0gt3" name="ALG Client" sourceRef="ExclusiveGateway_0g1ewho" targetRef="Task_ReviewMemberApplication">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${membershipType == "algClient"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:boundaryEvent id="capture-cancel-full-member" name="Cancel Full Member Application" attachedToRef="SubProcess_fullMemberSubProcess">
      <bpmn:outgoing>SequenceFlow_1fo2ycc</bpmn:outgoing>
      <bpmn:errorEventDefinition errorRef="Error_12uq10m" />
    </bpmn:boundaryEvent>
    <bpmn:boundaryEvent id="CancelNonMemberBoundEvent" name="Cancel Non Member Application" attachedToRef="SubProcess_NonMemberProcess">
      <bpmn:outgoing>SequenceFlow_0bhnrrg</bpmn:outgoing>
      <bpmn:errorEventDefinition errorRef="Error_12uq10m" />
    </bpmn:boundaryEvent>
    <bpmn:boundaryEvent id="BoundaryEvent_0yh8kyy" name="Cancel Non Member Application" attachedToRef="SubProcess_InitialAssessmentInvoice">
      <bpmn:outgoing>SequenceFlow_0ktrdss</bpmn:outgoing>
      <bpmn:errorEventDefinition errorRef="Error_1cbi7eo" />
    </bpmn:boundaryEvent>
    <bpmn:textAnnotation id="TextAnnotation_1jvzeyb">
      <bpmn:text>If Member is on DB and not in draft state Edit Mode is Enabled. If Changes are made to allowable change fields Submit Button Becomes available. Cancel Button is available for bailout. Request for help button is available.</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_13q0r97">
      <bpmn:text>Includes Non Member assessment details if applicant selects Non Member</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_19hve8x" sourceRef="ExclusiveGateway_0g1ewho" targetRef="TextAnnotation_1jvzeyb" />
    <bpmn:association id="Association_1tgyz3o" sourceRef="StartEvent_applicationFormCompleted" targetRef="TextAnnotation_13q0r97" />
  </bpmn:process>
  <bpmn:signal id="Signal_045zjjs" name="cancelnonmemapp" />
  <bpmn:signal id="Signal_1w9rfp4" name="CancelFullMember" />
  <bpmn:message id="Message_025sw22" name="assessment-invoice-exemption-message" />
  <bpmn:error id="Error_12uq10m" name="Error_FullMemberPaymentNotReceived" errorCode="FullMemberPaymentNotReceived" camunda:errorMessage="Cancel Full Member Application" />
  <bpmn:error id="Error_1cbi7eo" name="Error_AssessmentInvoiceNotReceived" errorCode="AssessmentInvoiceNotReceived" camunda:errorMessage="Assessment Invoice Not Received" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0gjfm6r">
      <bpmndi:BPMNShape id="Participant_0pry0ja_di" bpmnElement="Participant_0pry0ja" isHorizontal="true">
        <dc:Bounds x="153" y="81" width="3296" height="1973" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1f6x8yb_di" bpmnElement="Lane_applicant" isHorizontal="true">
        <dc:Bounds x="183" y="81" width="3266" height="340" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_02jojaj_di" bpmnElement="Lane_stakeHolderManager" isHorizontal="true">
        <dc:Bounds x="183" y="560" width="3266" height="333" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_17svdmu_di" bpmnElement="Lane_financialAdministrator" isHorizontal="true">
        <dc:Bounds x="173" y="893" width="3266" height="1161" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_14fq3ur_di" bpmnElement="StartEvent_applicationFormCompleted" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1466" y="122" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1445" y="85" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_11dvgjq_di" bpmnElement="Task_ReviewMemberApplication">
        <dc:Bounds x="1571" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1w7teci_di" bpmnElement="SequenceFlow_1w7teci">
        <di:waypoint x="1484" y="158" />
        <di:waypoint x="1484" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0orik0k_di" bpmnElement="ExclusiveGateway_qualify" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1742" y="612" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1726" y="592" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0wxkgne_di" bpmnElement="Task_disqualifyMember">
        <dc:Bounds x="1888" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0fuauda_di" bpmnElement="SequenceFlow_0fuauda">
        <di:waypoint x="2455" y="677" />
        <di:waypoint x="2455" y="805" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1dhwaud_di" bpmnElement="EndEvent_complete" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="2437" y="805" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2428.5" y="851" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07az7ab_di" bpmnElement="SequenceFlow_07az7ab">
        <di:waypoint x="1621" y="529" />
        <di:waypoint x="1621" y="597" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_13q0r97_di" bpmnElement="TextAnnotation_13q0r97">
        <dc:Bounds x="1674" y="102" width="127" height="75" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1tgyz3o_di" bpmnElement="Association_1tgyz3o">
        <di:waypoint x="1502" y="140" />
        <di:waypoint x="1674" y="139" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ManualTask_0cjz5mf_di" bpmnElement="Task_complete">
        <dc:Bounds x="2405" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0g1ewho_di" bpmnElement="ExclusiveGateway_0g1ewho" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1459" y="221" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1450" y="175" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1wdg5he_di" bpmnElement="SequenceFlow_1wdg5he" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1459" y="246" />
        <di:waypoint x="1370" y="246" />
        <di:waypoint x="1370" y="949" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1297" y="383" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1jvzeyb_di" bpmnElement="TextAnnotation_1jvzeyb">
        <dc:Bounds x="1671" y="190" width="272" height="82" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_19hve8x_di" bpmnElement="Association_19hve8x">
        <di:waypoint x="1508" y="245" />
        <di:waypoint x="1671" y="238" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SubProcess_02dg5ca_di" bpmnElement="SubProcess_NonMemberProcess" isExpanded="true">
        <dc:Bounds x="919" y="1369" width="784" height="417" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0mdz1wq_di" bpmnElement="StartEvent_NonMemberApplication">
        <dc:Bounds x="941" y="1576" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="927" y="1619" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0gomw05_di" bpmnElement="UserTask_0gomw05">
        <dc:Bounds x="1043" y="1554" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0zk0oqd_di" bpmnElement="IntermediateCatchEvent_0zk0oqd">
        <dc:Bounds x="1176" y="1576" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1tviaau_di" bpmnElement="EndEvent_1tviaau">
        <dc:Bounds x="1608" y="1576" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_02z37ea_di" bpmnElement="ExclusiveGateway_02z37ea" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1378" y="1569" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1383" y="1546" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1oej7e3_di" bpmnElement="ServiceTask_cancelAndEmailApplicant">
        <dc:Bounds x="1117" y="1679" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1urh21r_di" bpmnElement="SequenceFlow_1urh21r">
        <di:waypoint x="1143" y="1594" />
        <di:waypoint x="1176" y="1594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09uoa55_di" bpmnElement="SequenceFlow_09uoa55">
        <di:waypoint x="1212" y="1594" />
        <di:waypoint x="1245" y="1594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1u0awu9_di" bpmnElement="SequenceFlow_1u0awu9">
        <di:waypoint x="1345" y="1594" />
        <di:waypoint x="1378" y="1594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1fkbr8a_di" bpmnElement="SequenceFlow_1fkbr8a" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1403" y="1569" />
        <di:waypoint x="1403" y="1489" />
        <di:waypoint x="1256" y="1489" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1312" y="1472" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0iiiauc_di" bpmnElement="SequenceFlow_0iiiauc" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1428" y="1594" />
        <di:waypoint x="1462" y="1594" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1433" y="1575" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0vk45il_di" bpmnElement="SequenceFlow_0vk45il" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1403" y="1619" />
        <di:waypoint x="1403" y="1719" />
        <di:waypoint x="1217" y="1719" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1409" y="1668" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1shf565_di" bpmnElement="SequenceFlow_1shf565" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1742" y="637" />
        <di:waypoint x="1722" y="637" />
        <di:waypoint x="1722" y="812" />
        <di:waypoint x="1113" y="812" />
        <di:waypoint x="1113" y="1369" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1088" y="1077" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02smnwx_di" bpmnElement="SequenceFlow_02smnwx" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1767" y="662" />
        <di:waypoint x="1767" y="780" />
        <di:waypoint x="2951" y="780" />
        <di:waypoint x="2951" y="1347" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2948" y="1340.9999999999673" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0xmntch_di" bpmnElement="SequenceFlow_0xmntch">
        <di:waypoint x="1671" y="637" />
        <di:waypoint x="1687" y="637" />
        <di:waypoint x="1687" y="589" />
        <di:waypoint x="1767" y="589" />
        <di:waypoint x="1767" y="612" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sdz2lw_di" bpmnElement="SequenceFlow_0sdz2lw" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1792" y="637" />
        <di:waypoint x="1888" y="637" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1806" y="620" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1civem7_di" bpmnElement="SequenceFlow_1civem7">
        <di:waypoint x="1988" y="637" />
        <di:waypoint x="2020" y="637" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1bu7vk2_di" bpmnElement="EndEvent_0qrrie9">
        <dc:Bounds x="2020" y="619" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fghq34_di" bpmnElement="SequenceFlow_1fghq34">
        <di:waypoint x="3133" y="1347" />
        <di:waypoint x="3133" y="720" />
        <di:waypoint x="2553" y="720" />
        <di:waypoint x="2553" y="580" />
        <di:waypoint x="2300" y="580" />
        <di:waypoint x="2300" y="680" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1lkosa1_di" bpmnElement="SequenceFlow_1lkosa1">
        <di:waypoint x="2350" y="720" />
        <di:waypoint x="2377" y="720" />
        <di:waypoint x="2377" y="637" />
        <di:waypoint x="2405" y="637" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_00h5nay_di" bpmnElement="Lane_00h5nay" isHorizontal="true">
        <dc:Bounds x="183" y="424" width="3266" height="139" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1a4s1c4_di" bpmnElement="Task_allocateStakeHolderManagerManager">
        <dc:Bounds x="1571" y="449" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_12j2uw5_di" bpmnElement="SubProcess_fullMemberSubProcess" isExpanded="true">
        <dc:Bounds x="2496" y="1347" width="909" height="530" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0bt1y8h_di" bpmnElement="Task_approve">
        <dc:Bounds x="3058" y="1447" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vw6etr_di" bpmnElement="ExclusiveGateway_wasPaymentReceived" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="2980" y="1462" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2981" y="1437" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1hnftqn_di" bpmnElement="Task_18d4zpq">
        <dc:Bounds x="2820" y="1447" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0w457v8_di" bpmnElement="Task_checkIfPaymentReceived">
        <dc:Bounds x="2667" y="1555" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1b6n62o_di" bpmnElement="Task_generateInvoice">
        <dc:Bounds x="2667" y="1447" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1afkhtz_di" bpmnElement="StartEvent_fullMemberApplication">
        <dc:Bounds x="2616" y="1461" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2603" y="1504" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_01jesl4_di" bpmnElement="SequenceFlow_01jesl4">
        <di:waypoint x="2634" y="1461" />
        <di:waypoint x="2634" y="1427" />
        <di:waypoint x="2717" y="1427" />
        <di:waypoint x="2717" y="1447" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1ede031_di" bpmnElement="SequenceFlow_1ede031">
        <di:waypoint x="2717" y="1527" />
        <di:waypoint x="2717" y="1555" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_04pqhgj_di" bpmnElement="SequenceFlow_04pqhgj">
        <di:waypoint x="2767" y="1595" />
        <di:waypoint x="3005" y="1595" />
        <di:waypoint x="3005" y="1512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sh548s_di" bpmnElement="SequenceFlow_0sh548s" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="3030" y="1487" />
        <di:waypoint x="3058" y="1487" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3035" y="1469" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0q8npmq_di" bpmnElement="SequenceFlow_0q8npmq" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="3005" y="1462" />
        <di:waypoint x="3005" y="1400" />
        <di:waypoint x="3207" y="1400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3095" y="1382" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_020ch0h_di" bpmnElement="SequenceFlow_020ch0h">
        <di:waypoint x="3158" y="1487" />
        <di:waypoint x="3207" y="1487" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1tlzqyu_di" bpmnElement="Task_0tatwfs">
        <dc:Bounds x="1245" y="1554" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0bhnrrg_di" bpmnElement="SequenceFlow_0bhnrrg">
        <di:waypoint x="1546" y="1804" />
        <di:waypoint x="1546" y="1880" />
        <di:waypoint x="2455" y="1880" />
        <di:waypoint x="2455" y="841" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1fo2ycc_di" bpmnElement="SequenceFlow_1fo2ycc">
        <di:waypoint x="2696" y="1329" />
        <di:waypoint x="2696" y="823" />
        <di:waypoint x="2473" y="823" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wkn6l0_di" bpmnElement="SequenceFlow_1wkn6l0">
        <di:waypoint x="1621" y="374" />
        <di:waypoint x="1621" y="449" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0d3y1pe_di" bpmnElement="Task_allocateGroupStakeholderManager">
        <dc:Bounds x="1571" y="294" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_0fs2yin_di" bpmnElement="SubProcess_InitialAssessmentInvoice" isExpanded="true">
        <dc:Bounds x="1190" y="949" width="1018" height="383" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_1b24cgj_di" bpmnElement="BoundaryEvent_1b24cgj">
        <dc:Bounds x="2190" y="1113" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2235" y="1152" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1qggjo9_di" bpmnElement="StartEvent_1qggjo9">
        <dc:Bounds x="1310" y="1051" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0lowgau_di" bpmnElement="UserTask_0lowgau">
        <dc:Bounds x="1374" y="1029" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_18zptnn_di" bpmnElement="IntermediateCatchEvent_18zptnn">
        <dc:Bounds x="1492" y="1146" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0zkahxr_di" bpmnElement="UserTask_0zkahxr">
        <dc:Bounds x="1603" y="1124" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0ew5w3l_di" bpmnElement="ExclusiveGateway_0ew5w3l" isMarkerVisible="true">
        <dc:Bounds x="1788" y="1044" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1798" y="1035" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1kupf1l_di" bpmnElement="ServiceTask_1kupf1l">
        <dc:Bounds x="1868" y="1124" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0c3d83q_di" bpmnElement="EndEvent_0c3d83q">
        <dc:Bounds x="2052" y="1051" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0wwquoa_di" bpmnElement="SequenceFlow_0wwquoa">
        <di:waypoint x="1346" y="1069" />
        <di:waypoint x="1374" y="1069" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0aqtl15_di" bpmnElement="SequenceFlow_0aqtl15">
        <di:waypoint x="1424" y="1109" />
        <di:waypoint x="1424" y="1164" />
        <di:waypoint x="1492" y="1164" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_194wie8_di" bpmnElement="SequenceFlow_194wie8">
        <di:waypoint x="1528" y="1164" />
        <di:waypoint x="1603" y="1164" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1m0cz42_di" bpmnElement="SequenceFlow_1m0cz42">
        <di:waypoint x="1703" y="1164" />
        <di:waypoint x="1760" y="1164" />
        <di:waypoint x="1760" y="1069" />
        <di:waypoint x="1788" y="1069" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0b9nzkv_di" bpmnElement="SequenceFlow_0b9nzkv">
        <di:waypoint x="1838" y="1069" />
        <di:waypoint x="2052" y="1069" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1936" y="1051" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18itr3o_di" bpmnElement="SequenceFlow_18itr3o">
        <di:waypoint x="1813" y="1094" />
        <di:waypoint x="1813" y="1148" />
        <di:waypoint x="1868" y="1148" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1828" y="1116" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ktrdss_di" bpmnElement="SequenceFlow_0ktrdss">
        <di:waypoint x="2025" y="1350" />
        <di:waypoint x="2025" y="1390" />
        <di:waypoint x="2380" y="1390" />
        <di:waypoint x="2380" y="823" />
        <di:waypoint x="2437" y="823" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0bndmog_di" bpmnElement="SequenceFlow_0bndmog">
        <di:waypoint x="2226" y="1131" />
        <di:waypoint x="2300" y="1131" />
        <di:waypoint x="2300" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cu0ave_di" bpmnElement="SequenceFlow_0cu0ave">
        <di:waypoint x="2129" y="949" />
        <di:waypoint x="2129" y="334" />
        <di:waypoint x="1671" y="334" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_06n25mm_di" bpmnElement="SequenceFlow_06n25mm">
        <di:waypoint x="1171" y="1369" />
        <di:waypoint x="1171" y="928" />
        <di:waypoint x="2268" y="928" />
        <di:waypoint x="2268" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1j47qot_di" bpmnElement="EndEvent_0bam2or">
        <dc:Bounds x="3207" y="1469" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_06mnisl_di" bpmnElement="SequenceFlow_06mnisl">
        <di:waypoint x="2980" y="1487" />
        <di:waypoint x="2920" y="1487" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2930" y="1469" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1xk2w0q_di" bpmnElement="SequenceFlow_1xk2w0q">
        <di:waypoint x="2870" y="1527" />
        <di:waypoint x="2870" y="1547" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0m9sqzf_di" bpmnElement="SequenceFlow_0m9sqzf">
        <di:waypoint x="2852" y="1565" />
        <di:waypoint x="2767" y="1565" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0b8s59n_di" bpmnElement="IntermediateThrowEvent_1g401ap">
        <dc:Bounds x="2852" y="1547" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07b2i1i_di" bpmnElement="SequenceFlow_07b2i1i">
        <di:waypoint x="1603" y="1069" />
        <di:waypoint x="1510" y="1069" />
        <di:waypoint x="1510" y="1146" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0nx0bix_di" bpmnElement="Task_0hwrm6w">
        <dc:Bounds x="1603" y="1029" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0xzp15x_di" bpmnElement="SequenceFlow_0xzp15x">
        <di:waypoint x="1813" y="1044" />
        <di:waypoint x="1813" y="1035" />
        <di:waypoint x="1725" y="1035" />
        <di:waypoint x="1725" y="1069" />
        <di:waypoint x="1705" y="1069" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1744" y="1019" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0u0x3h5_di" bpmnElement="Task_1dl0r1t">
        <dc:Bounds x="1156" y="1449" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_13e9qb2_di" bpmnElement="SequenceFlow_13e9qb2">
        <di:waypoint x="1195" y="1529" />
        <di:waypoint x="1194" y="1576" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1o947iw_di" bpmnElement="SequenceFlow_1o947iw">
        <di:waypoint x="1509" y="246" />
        <di:waypoint x="1621" y="246" />
        <di:waypoint x="1621" y="294" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1542" y="229" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01n3jrq_di" bpmnElement="SequenceFlow_01n3jrq">
        <di:waypoint x="977" y="1594" />
        <di:waypoint x="1043" y="1594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0374fdh_di" bpmnElement="Task_shmFinalReview">
        <dc:Bounds x="2250" y="680" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_08wf8j2_di" bpmnElement="SequenceFlow_08wf8j2">
        <di:waypoint x="1562" y="1594" />
        <di:waypoint x="1608" y="1594" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_00jf1w5_di" bpmnElement="Task_1exlbax">
        <dc:Bounds x="1462" y="1554" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_070z1t6_di" bpmnElement="Task_09csx0h">
        <dc:Bounds x="1888" y="690" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_03xpzxf_di" bpmnElement="SequenceFlow_03xpzxf">
        <di:waypoint x="1790" y="639" />
        <di:waypoint x="1790" y="730" />
        <di:waypoint x="1888" y="730" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1795" y="682" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1j82rzu_di" bpmnElement="SequenceFlow_1j82rzu">
        <di:waypoint x="1988" y="730" />
        <di:waypoint x="2038" y="730" />
        <di:waypoint x="2038" y="655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0kb0gt3_di" bpmnElement="SequenceFlow_0kb0gt3">
        <di:waypoint x="1484" y="271" />
        <di:waypoint x="1484" y="637" />
        <di:waypoint x="1571" y="637" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1423" y="343" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BoundaryEvent_1d5fszn_di" bpmnElement="capture-cancel-full-member">
        <dc:Bounds x="2678" y="1329" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2672" y="1369" width="56" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_10zdow7_di" bpmnElement="EndEvent_CancelFullMemberApplication">
        <dc:Bounds x="3207" y="1382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3201" y="1425" width="56" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_1ir1jfv_di" bpmnElement="CancelNonMemberBoundEvent" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1528" y="1768" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1521" y="1811" width="58" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1vfex3k_di" bpmnElement="SequenceFlow_1vfex3k">
        <di:waypoint x="1117" y="1719" />
        <di:waypoint x="1068" y="1719" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0nyam56_di" bpmnElement="EndEvent_CancelNonMemberApplication">
        <dc:Bounds x="1032" y="1701" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1022" y="1744" width="58" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0oj546i_di" bpmnElement="SequenceFlow_0oj546i">
        <di:waypoint x="1918" y="1204" />
        <di:waypoint x="1918" y="1252" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0zqo6w9_di" bpmnElement="EndEvent_CancelNonMemberApplicationAssessment">
        <dc:Bounds x="1900" y="1252" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1891" y="1295" width="58" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_1nrwkl7_di" bpmnElement="BoundaryEvent_0yh8kyy">
        <dc:Bounds x="2007" y="1314" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2032" y="1404" width="58" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
