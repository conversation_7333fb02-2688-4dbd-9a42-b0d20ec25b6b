<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_061qjsw" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_1hh6ezi">
    <bpmn:participant id="Participant_0hdz6up" name="SRN Status Update - Non Cancellations" processRef="SRN-Status-Update-Non-Cancellations" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Status-Update-Non-Cancellations" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1gqb0jp">
      <bpmn:lane id="Lane_SacrraAdmin" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_SRN_Status_Changed_Confirm_Updated_DTH</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_13d5sd6</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_Portal" name="Portal">
        <bpmn:flowNodeRef>Task_update_srn_status_for_non_cancellations</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_016muaz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_email_member_about_srn_status_update_for_non_cancellations</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_email_bureaus_about_srn_status_update_for_non_cancellations</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1di4etu</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_SHM" name="SHM">
        <bpmn:flowNodeRef>StartEvent_1yf9ovw</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Task_update_srn_status_for_non_cancellations" name="Update status" camunda:type="external" camunda:topic="update-srn-status-for-non-cancellations">
      <bpmn:incoming>SequenceFlow_1n00ey1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0h3gqjj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_1yf9ovw" name="Start status update">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="SRNId" label="SRN Id" type="long" />
          <camunda:formField id="isLiveOrTest" label="Is Live or Test?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
          <camunda:formField id="SRNStatusId" label="SRN Status Id" type="long" />
          <camunda:formField id="UpdatedByUserId" label="Updated By User Id" type="long" />
          <camunda:formField id="SRNStatusName" label="SRN Status Name" type="string" />
          <camunda:formField id="SRNUpdateType" label="SRN Update Type" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1n00ey1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_016muaz" name="Live/Test?">
      <bpmn:incoming>SequenceFlow_0h3gqjj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0hli833</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0nulo7f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_email_member_about_srn_status_update_for_non_cancellations" name="Email member about status update" camunda:type="external" camunda:topic="email-member-about-srn-status-update-for-non-cancellations">
      <bpmn:incoming>SequenceFlow_0nulo7f</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_12uzns5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1k3ngi8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_email_bureaus_about_srn_status_update_for_non_cancellations" name="Email bureaus about status update" camunda:type="external" camunda:topic="email-bureaus-about-srn-status-update-for-non-cancellations">
      <bpmn:incoming>SequenceFlow_1k3ngi8</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1n00ey1" sourceRef="StartEvent_1yf9ovw" targetRef="Task_update_srn_status_for_non_cancellations" />
    <bpmn:sequenceFlow id="SequenceFlow_0h3gqjj" sourceRef="Task_update_srn_status_for_non_cancellations" targetRef="ExclusiveGateway_016muaz" />
    <bpmn:sequenceFlow id="SequenceFlow_0hli833" name="Yes" sourceRef="ExclusiveGateway_016muaz" targetRef="ExclusiveGateway_1di4etu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLiveOrTest == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0nulo7f" name="No" sourceRef="ExclusiveGateway_016muaz" targetRef="Task_email_member_about_srn_status_update_for_non_cancellations">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLiveOrTest == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1k3ngi8" sourceRef="Task_email_member_about_srn_status_update_for_non_cancellations" targetRef="Task_email_bureaus_about_srn_status_update_for_non_cancellations" />
    <bpmn:sequenceFlow id="SequenceFlow_1b2b7k4" sourceRef="ExclusiveGateway_1di4etu" targetRef="Task_SRN_Status_Changed_Confirm_Updated_DTH" />
    <bpmn:parallelGateway id="ExclusiveGateway_1di4etu">
      <bpmn:incoming>SequenceFlow_0hli833</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1b2b7k4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_12uzns5</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_12uzns5" sourceRef="ExclusiveGateway_1di4etu" targetRef="Task_email_member_about_srn_status_update_for_non_cancellations" />
    <bpmn:userTask id="Task_SRN_Status_Changed_Confirm_Updated_DTH" name="SRN Status Changed - Confirm Updated DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_1b2b7k4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11pukcc</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_13d5sd6">
      <bpmn:incoming>SequenceFlow_11pukcc</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_11pukcc" sourceRef="Task_SRN_Status_Changed_Confirm_Updated_DTH" targetRef="EndEvent_13d5sd6" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1hh6ezi">
      <bpmndi:BPMNShape id="Participant_0hdz6up_di" bpmnElement="Participant_0hdz6up" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="1150" height="560" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_00euzm8_di" bpmnElement="Lane_SacrraAdmin" isHorizontal="true">
        <dc:Bounds x="190" y="490" width="1120" height="150" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0eo72lt_di" bpmnElement="Lane_Portal" isHorizontal="true">
        <dc:Bounds x="190" y="240" width="1120" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_195rlz9_di" bpmnElement="Lane_SHM" isHorizontal="true">
        <dc:Bounds x="190" y="80" width="1120" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1yf9ovw_di" bpmnElement="StartEvent_1yf9ovw">
        <dc:Bounds x="352" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="341" y="124.5" width="57" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1rk3j5b_di" bpmnElement="EndEvent_13d5sd6">
        <dc:Bounds x="782" y="552" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_17nrhph_di" bpmnElement="Task_SRN_Status_Changed_Confirm_Updated_DTH">
        <dc:Bounds x="580" y="530" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1wzqzmy_di" bpmnElement="Task_update_srn_status_for_non_cancellations">
        <dc:Bounds x="320" y="280" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1n00ey1_di" bpmnElement="SequenceFlow_1n00ey1">
        <di:waypoint x="370" y="198" />
        <di:waypoint x="370" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_016muaz_di" bpmnElement="ExclusiveGateway_016muaz" isMarkerVisible="true">
        <dc:Bounds x="605" y="295" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="606" y="265" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0h3gqjj_di" bpmnElement="SequenceFlow_0h3gqjj">
        <di:waypoint x="420" y="320" />
        <di:waypoint x="605" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0hli833_di" bpmnElement="SequenceFlow_0hli833">
        <di:waypoint x="630" y="345" />
        <di:waypoint x="630" y="415" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="636" y="373" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0eoytz5_di" bpmnElement="Task_email_member_about_srn_status_update_for_non_cancellations">
        <dc:Bounds x="770" y="280" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0nulo7f_di" bpmnElement="SequenceFlow_0nulo7f">
        <di:waypoint x="655" y="320" />
        <di:waypoint x="770" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="705" y="302" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1k3ngi8_di" bpmnElement="SequenceFlow_1k3ngi8">
        <di:waypoint x="870" y="320" />
        <di:waypoint x="1010" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_131hzjh_di" bpmnElement="Task_email_bureaus_about_srn_status_update_for_non_cancellations">
        <dc:Bounds x="1010" y="280" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1b2b7k4_di" bpmnElement="SequenceFlow_1b2b7k4">
        <di:waypoint x="630" y="465" />
        <di:waypoint x="630" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ParallelGateway_18u7dcs_di" bpmnElement="ExclusiveGateway_1di4etu">
        <dc:Bounds x="605" y="415" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_12uzns5_di" bpmnElement="SequenceFlow_12uzns5">
        <di:waypoint x="655" y="440" />
        <di:waypoint x="820" y="440" />
        <di:waypoint x="820" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_11pukcc_di" bpmnElement="SequenceFlow_11pukcc">
        <di:waypoint x="680" y="570" />
        <di:waypoint x="782" y="570" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
