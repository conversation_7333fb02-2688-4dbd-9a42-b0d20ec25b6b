<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0iolpds" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.1.2">
  <bpmn:collaboration id="Collaboration_1s2lehq">
    <bpmn:documentation>SACRRA Admin</bpmn:documentation>
    <bpmn:participant id="Participant_1lue96p" processRef="Process_1wbsra4" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1wbsra4" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1ozgkmo">
      <bpmn:lane id="Lane_0o3dfku" name="Mon Mber Reg">
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1qgifkh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1lnc961</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1xsdfat</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1nhkgfb</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0d0zani</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_11yctsg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0xou2s7</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0dr6t7o</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0gsc6zx</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_cancel</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_0ccl0v0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_cancelMemberApplication</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_18txa7c</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1l0vuyf</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1672ggo</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0dcy969</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_0q5bbug</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_0p2y6fi</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1ffmidb</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1iifdaz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_12k2o5l</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_17g1j7g</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1ht9urg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0g2laxg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0ky3i0u</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1p4fuvy</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1yhbmgb</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1d5ux0a</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1mxic76</bpmn:flowNodeRef>
        <bpmn:childLaneSet id="LaneSet_1617eu0">
          <bpmn:lane id="Lane_1uqc2mb" name="Non Member">
            <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1qgifkh</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>ExclusiveGateway_1lnc961</bpmn:flowNodeRef>
          </bpmn:lane>
          <bpmn:lane id="Lane_1ygz6ob" name="SHM">
            <bpmn:flowNodeRef>ExclusiveGateway_0d0zani</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>EndEvent_0ccl0v0</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_18txa7c</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1l0vuyf</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>EndEvent_0p2y6fi</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1ffmidb</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>ExclusiveGateway_1iifdaz</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>EndEvent_12k2o5l</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_0ky3i0u</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>ExclusiveGateway_1p4fuvy</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1yhbmgb</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1mxic76</bpmn:flowNodeRef>
          </bpmn:lane>
          <bpmn:lane id="Lane_0o5zv9m" name="Financial Administrator">
            <bpmn:flowNodeRef>IntermediateThrowEvent_1xsdfat</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1nhkgfb</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_11yctsg</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_0xou2s7</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_0dr6t7o</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>ExclusiveGateway_0gsc6zx</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>EndEvent_cancel</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_cancelMemberApplication</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_1672ggo</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_0dcy969</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>IntermediateThrowEvent_0q5bbug</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>Task_17g1j7g</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>ExclusiveGateway_1ht9urg</bpmn:flowNodeRef>
            <bpmn:flowNodeRef>EndEvent_1d5ux0a</bpmn:flowNodeRef>
          </bpmn:lane>
          <bpmn:lane id="Lane_01x1qo9" name="SACRRA Admin">
            <bpmn:flowNodeRef>Task_0g2laxg</bpmn:flowNodeRef>
          </bpmn:lane>
        </bpmn:childLaneSet>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="MemberId" label="Member Id" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0228fz3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0228fz3" sourceRef="StartEvent_applicationFormCompleted" targetRef="Task_1qgifkh" />
    <bpmn:serviceTask id="Task_1qgifkh" name="Check if member exists" camunda:type="external" camunda:topic="check-if-member-exists">
      <bpmn:incoming>SequenceFlow_0228fz3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0binwyk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0binwyk" sourceRef="Task_1qgifkh" targetRef="ExclusiveGateway_1lnc961" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1lnc961" name="Does member exist?">
      <bpmn:incoming>SequenceFlow_0binwyk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0fotw8k</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1mshf06</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0fotw8k" name="No" sourceRef="ExclusiveGateway_1lnc961" targetRef="Task_11yctsg" />
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1xsdfat" name="wait for [x] working days">
      <bpmn:incoming>SequenceFlow_1dsdh8l</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_05s58yp</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:userTask id="Task_1nhkgfb" name="Check if assessment payment recieved" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="assessmentPayment" label="Payment Status" type="enum" defaultValue="notReceived">
            <camunda:value id="notReceived" name="Not Received" />
            <camunda:value id="received" name="Received" />
            <camunda:value id="expired" name="Expired" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_05s58yp</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0sxlc94</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0d0zani" name="Is full member candidate?">
      <bpmn:incoming>SequenceFlow_0lbip9k</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1mnxvdj</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_10h64o5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_11yctsg" name="Allocate to Financial Administrator">
      <bpmn:incoming>SequenceFlow_0fotw8k</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0sspd5e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_0xou2s7" name="Generate and email member invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:incoming>SequenceFlow_0sspd5e</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0o07fzs</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_0dr6t7o" name="Awaiting assessment payment" camunda:type="external" camunda:topic="awaiting-assessment-payment">
      <bpmn:incoming>SequenceFlow_0o07fzs</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1dsdh8l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0gsc6zx" name="Assessment payment received?">
      <bpmn:incoming>SequenceFlow_0sxlc94</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0t06c5a</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0la6hx8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="EndEvent_cancel" name="Application cancelled due to none payment">
      <bpmn:incoming>SequenceFlow_0ikunzo</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:endEvent id="EndEvent_0ccl0v0" name="Applcation &#10;Cancelled">
      <bpmn:incoming>SequenceFlow_1tlmd3o</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_122au8g</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_cancelMemberApplication" name="Cancel application &#38; email member" camunda:type="external" camunda:topic="cencel-member-application">
      <bpmn:incoming>SequenceFlow_0la6hx8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ikunzo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_18txa7c" name="SHM Carries out Assessment" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="qualify" label="Does member qualify?" type="enum" defaultValue="yes">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1fwwqc5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1791j4e</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_1l0vuyf" name="Start full member application workflow" camunda:expression="start-full-member-application-workflow">
      <bpmn:incoming>SequenceFlow_10h64o5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1vnzta8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_1672ggo" name="Create &#38; email on-boarding&#10;invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:incoming>SequenceFlow_1mnxvdj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0je3n3s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_0dcy969" name="Awaiting on-bording payment" camunda:type="external" camunda:topic="awaiting-onboarding-payment">
      <bpmn:incoming>SequenceFlow_0je3n3s</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_13lgpw5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_0q5bbug" name="wait for [x] working days">
      <bpmn:incoming>SequenceFlow_13lgpw5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0801eao</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:endEvent id="EndEvent_0p2y6fi" name="Member &#10;Accepted">
      <bpmn:incoming>SequenceFlow_06jty8f</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_07q4h4a</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1dsdh8l" sourceRef="Task_0dr6t7o" targetRef="IntermediateThrowEvent_1xsdfat" />
    <bpmn:sequenceFlow id="SequenceFlow_05s58yp" sourceRef="IntermediateThrowEvent_1xsdfat" targetRef="Task_1nhkgfb" />
    <bpmn:sequenceFlow id="SequenceFlow_0sxlc94" sourceRef="Task_1nhkgfb" targetRef="ExclusiveGateway_0gsc6zx" />
    <bpmn:sequenceFlow id="SequenceFlow_1tlmd3o" sourceRef="Task_1mxic76" targetRef="EndEvent_0ccl0v0" />
    <bpmn:sequenceFlow id="SequenceFlow_1mnxvdj" name="No" sourceRef="ExclusiveGateway_0d0zani" targetRef="Task_1672ggo" />
    <bpmn:sequenceFlow id="SequenceFlow_10h64o5" name="Yes" sourceRef="ExclusiveGateway_0d0zani" targetRef="Task_1l0vuyf" />
    <bpmn:sequenceFlow id="SequenceFlow_0sspd5e" sourceRef="Task_11yctsg" targetRef="Task_0xou2s7" />
    <bpmn:sequenceFlow id="SequenceFlow_0o07fzs" sourceRef="Task_0xou2s7" targetRef="Task_0dr6t7o" />
    <bpmn:sequenceFlow id="SequenceFlow_0t06c5a" name="Yes" sourceRef="ExclusiveGateway_0gsc6zx" targetRef="Task_0g2laxg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${assessmentPayment == "received"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0la6hx8" name="No" sourceRef="ExclusiveGateway_0gsc6zx" targetRef="Task_cancelMemberApplication">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${assessmentPayment == "notReceived"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0je3n3s" sourceRef="Task_1672ggo" targetRef="Task_0dcy969" />
    <bpmn:sequenceFlow id="SequenceFlow_13lgpw5" sourceRef="Task_0dcy969" targetRef="IntermediateThrowEvent_0q5bbug" />
    <bpmn:sequenceFlow id="SequenceFlow_1wq5klj" sourceRef="Task_1yhbmgb" targetRef="ExclusiveGateway_1p4fuvy" />
    <bpmn:sequenceFlow id="SequenceFlow_0xa344v" name="No" sourceRef="ExclusiveGateway_1p4fuvy" targetRef="Task_0ky3i0u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${membershipTypeChanged == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1mx7wtr" name="Yes" sourceRef="ExclusiveGateway_1p4fuvy" targetRef="Task_1ffmidb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${membershipTypeChanged == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_06jty8f" sourceRef="Task_0ky3i0u" targetRef="EndEvent_0p2y6fi" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1iifdaz" name="Does member qualify?">
      <bpmn:incoming>SequenceFlow_1791j4e</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_122au8g</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0lbip9k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1791j4e" sourceRef="Task_18txa7c" targetRef="ExclusiveGateway_1iifdaz" />
    <bpmn:sequenceFlow id="SequenceFlow_122au8g" name="No" sourceRef="ExclusiveGateway_1iifdaz" targetRef="EndEvent_0ccl0v0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${qualify == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0lbip9k" name="Yes" sourceRef="ExclusiveGateway_1iifdaz" targetRef="ExclusiveGateway_0d0zani">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${qualify == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="EndEvent_12k2o5l" name="Non-member application ends">
      <bpmn:incoming>SequenceFlow_1vnzta8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1vnzta8" sourceRef="Task_1l0vuyf" targetRef="EndEvent_12k2o5l" />
    <bpmn:sequenceFlow id="SequenceFlow_1mshf06" name="Yes" sourceRef="ExclusiveGateway_1lnc961" targetRef="Task_1mxic76" />
    <bpmn:sequenceFlow id="SequenceFlow_0801eao" sourceRef="IntermediateThrowEvent_0q5bbug" targetRef="Task_17g1j7g" />
    <bpmn:userTask id="Task_17g1j7g" name="Check if on-boarding payment received" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="onboardingPayment" type="enum">
            <camunda:value id="received" name="Received" />
            <camunda:value id="notReceived" name="Not Received" />
            <camunda:value id="expired" name="Expired" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0801eao</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1vl36r7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1ht9urg" name="On-boarding payment received?">
      <bpmn:incoming>SequenceFlow_1vl36r7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1oqbzfr</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_08q4c6z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1vl36r7" sourceRef="Task_17g1j7g" targetRef="ExclusiveGateway_1ht9urg" />
    <bpmn:sequenceFlow id="SequenceFlow_1oqbzfr" name="Yes" sourceRef="ExclusiveGateway_1ht9urg" targetRef="Task_1yhbmgb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${assessmentPayment == "received"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_08q4c6z" name="No" sourceRef="ExclusiveGateway_1ht9urg" targetRef="EndEvent_1d5ux0a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${assessmentPayment == "notReceived"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Task_0g2laxg" name="Allocate to SHM" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_0t06c5a</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1fwwqc5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="SequenceFlow_1fwwqc5" sourceRef="Task_0g2laxg" targetRef="Task_18txa7c" />
    <bpmn:serviceTask id="Task_0ky3i0u" name="Complete final take-on &#38; email member" camunda:type="external" camunda:topic="complete-member-application-final-takeon">
      <bpmn:incoming>SequenceFlow_0xa344v</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_06jty8f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1p4fuvy" name="Did membership type change?">
      <bpmn:incoming>SequenceFlow_1wq5klj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xa344v</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1mx7wtr</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_07q4h4a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_1yhbmgb" name="SHM Review &#38; Update" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="membershipChanged" label="Did membership type change?" type="enum" defaultValue="yes">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1oqbzfr</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wq5klj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1d5ux0a">
      <bpmn:incoming>SequenceFlow_08q4c6z</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0ikunzo" sourceRef="Task_cancelMemberApplication" targetRef="EndEvent_cancel" />
    <bpmn:userTask id="Task_1mxic76" name="Review and Update Membership Details" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_1mshf06</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1tlmd3o</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_1ffmidb" name="Start membership type change workflow" camunda:type="external" camunda:topic="start-membership-type-change-work">
      <bpmn:incoming>SequenceFlow_1mx7wtr</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_07q4h4a" sourceRef="ExclusiveGateway_1p4fuvy" targetRef="EndEvent_0p2y6fi" />
    <bpmn:textAnnotation id="TextAnnotation_16o62hh">
      <bpmn:text>Use full Member Application and add additional fields</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_0dpohjc">
      <bpmn:text>All non Member Applications to be Validated to see if they currently belong to an ALG</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1cdda9r">
      <bpmn:text>Additional Start Event to be added on Full Member</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0o6wh02" sourceRef="EndEvent_0ccl0v0" targetRef="TextAnnotation_1bi5qmc" />
    <bpmn:textAnnotation id="TextAnnotation_1bi5qmc">
      <bpmn:text>With Reason</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1s2lehq">
      <bpmndi:BPMNShape id="Participant_1lue96p_di" bpmnElement="Participant_1lue96p" isHorizontal="true">
        <dc:Bounds x="195" y="81" width="1278" height="941" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_applicationFormCompleted">
        <dc:Bounds x="367" y="145" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="360" y="115" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0o3dfku_di" bpmnElement="Lane_0o3dfku" isHorizontal="true">
        <dc:Bounds x="225" y="81" width="1248" height="941" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1uqc2mb_di" bpmnElement="Lane_1uqc2mb" isHorizontal="true">
        <dc:Bounds x="255" y="81" width="1218" height="251" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0o5zv9m_di" bpmnElement="Lane_0o5zv9m" isHorizontal="true">
        <dc:Bounds x="255" y="452" width="1218" height="230" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1ygz6ob_di" bpmnElement="Lane_1ygz6ob" isHorizontal="true">
        <dc:Bounds x="255" y="682" width="1218" height="340" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_16o62hh_di" bpmnElement="TextAnnotation_16o62hh">
        <dc:Bounds x="833" y="122" width="100" height="82" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_08e06ro_di" bpmnElement="IntermediateThrowEvent_1xsdfat">
        <dc:Bounds x="547" y="494" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="534" y="468" width="64" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0gsc6zx_di" bpmnElement="ExclusiveGateway_0gsc6zx" isMarkerVisible="true">
        <dc:Bounds x="790" y="539" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="785" y="593" width="60" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_05s58yp_di" bpmnElement="SequenceFlow_05s58yp">
        <di:waypoint x="583" y="512" />
        <di:waypoint x="615" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_1lnc961_di" bpmnElement="ExclusiveGateway_1lnc961" isMarkerVisible="true">
        <dc:Bounds x="502" y="237" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="491.5" y="199.5" width="71" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0dpohjc_di" bpmnElement="TextAnnotation_0dpohjc">
        <dc:Bounds x="833" y="214" width="100" height="96" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0z5t7sz_di" bpmnElement="Task_1mxic76">
        <dc:Bounds x="285" y="707" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1tlmd3o_di" bpmnElement="SequenceFlow_1tlmd3o">
        <di:waypoint x="335" y="787" />
        <di:waypoint x="335" y="844" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1bi5qmc_di" bpmnElement="TextAnnotation_1bi5qmc">
        <dc:Bounds x="379" y="792" width="100" height="30" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0o6wh02_di" bpmnElement="Association_0o6wh02">
        <di:waypoint x="350" y="853" />
        <di:waypoint x="403" y="822" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1u1k4t8_di" bpmnElement="Task_1nhkgfb">
        <dc:Bounds x="615" y="472" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0sxlc94_di" bpmnElement="SequenceFlow_0sxlc94">
        <di:waypoint x="715" y="512" />
        <di:waypoint x="743" y="512" />
        <di:waypoint x="743" y="564" />
        <di:waypoint x="790" y="564" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0t06c5a_di" bpmnElement="SequenceFlow_0t06c5a">
        <di:waypoint x="815" y="539" />
        <di:waypoint x="815" y="429" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="794" y="480" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1474hg5_di" bpmnElement="Task_18txa7c">
        <dc:Bounds x="548" y="690" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1wq5klj_di" bpmnElement="SequenceFlow_1wq5klj">
        <di:waypoint x="1085" y="811" />
        <di:waypoint x="1085" y="846" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0d0zani_di" bpmnElement="ExclusiveGateway_0d0zani" isMarkerVisible="true">
        <dc:Bounds x="725" y="746" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="730" y="717" width="71" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1mnxvdj_di" bpmnElement="SequenceFlow_1mnxvdj">
        <di:waypoint x="775" y="771" />
        <di:waypoint x="1014" y="771" />
        <di:waypoint x="1014" y="634" />
        <di:waypoint x="1064" y="634" />
        <di:waypoint x="1064" y="512" />
        <di:waypoint x="1111" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="802" y="755" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_1p4fuvy_di" bpmnElement="ExclusiveGateway_1p4fuvy" isMarkerVisible="true">
        <dc:Bounds x="1060" y="846" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="987.5" y="848" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0xa344v_di" bpmnElement="SequenceFlow_0xa344v">
        <di:waypoint x="1085" y="896" />
        <di:waypoint x="1085" y="926" />
        <di:waypoint x="1049" y="926" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="902" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1cdda9r_di" bpmnElement="TextAnnotation_1cdda9r">
        <dc:Bounds x="847" y="810" width="100" height="68" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0228fz3_di" bpmnElement="SequenceFlow_0228fz3">
        <di:waypoint x="403" y="163" />
        <di:waypoint x="583" y="163" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_14mzvc2_di" bpmnElement="Task_1qgifkh">
        <dc:Bounds x="583" y="123" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0binwyk_di" bpmnElement="SequenceFlow_0binwyk">
        <di:waypoint x="633" y="203" />
        <di:waypoint x="633" y="262" />
        <di:waypoint x="552" y="262" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0y8m0q6_di" bpmnElement="Task_11yctsg">
        <dc:Bounds x="405" y="472" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0fotw8k_di" bpmnElement="SequenceFlow_0fotw8k">
        <di:waypoint x="527" y="287" />
        <di:waypoint x="527" y="420" />
        <di:waypoint x="475" y="420" />
        <di:waypoint x="475" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="509" y="307" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sspd5e_di" bpmnElement="SequenceFlow_0sspd5e">
        <di:waypoint x="455" y="552" />
        <di:waypoint x="455" y="573" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1wnq837_di" bpmnElement="Task_0xou2s7">
        <dc:Bounds x="405" y="573" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0o07fzs_di" bpmnElement="SequenceFlow_0o07fzs">
        <di:waypoint x="505" y="613" />
        <di:waypoint x="615" y="613" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1h8ko54_di" bpmnElement="Task_0dr6t7o">
        <dc:Bounds x="615" y="573" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1dsdh8l_di" bpmnElement="SequenceFlow_1dsdh8l">
        <di:waypoint x="665" y="573" />
        <di:waypoint x="665" y="564" />
        <di:waypoint x="565" y="564" />
        <di:waypoint x="565" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1wqibpk_di" bpmnElement="EndEvent_cancel">
        <dc:Bounds x="879" y="595" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="856" y="636" width="81" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_16zbga9_di" bpmnElement="EndEvent_0ccl0v0">
        <dc:Bounds x="317" y="844" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="308" y="887" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17es0oh_di" bpmnElement="EndEvent_0p2y6fi">
        <dc:Bounds x="1143" y="946" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1138" y="991.5" width="45" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0la6hx8_di" bpmnElement="SequenceFlow_0la6hx8">
        <di:waypoint x="840" y="564" />
        <di:waypoint x="886" y="564" />
        <di:waypoint x="886" y="512" />
        <di:waypoint x="932" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="867" y="530" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0ztotan_di" bpmnElement="Task_cancelMemberApplication">
        <dc:Bounds x="932" y="472" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_10h64o5_di" bpmnElement="SequenceFlow_10h64o5">
        <di:waypoint x="750" y="796" />
        <di:waypoint x="750" y="853" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="756" y="822" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0c2mkkl_di" bpmnElement="Task_1l0vuyf">
        <dc:Bounds x="720" y="853" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1misna7_di" bpmnElement="Task_1672ggo">
        <dc:Bounds x="1111" y="472" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0nsq5if_di" bpmnElement="Task_0dcy969">
        <dc:Bounds x="1260" y="472" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0je3n3s_di" bpmnElement="SequenceFlow_0je3n3s">
        <di:waypoint x="1211" y="512" />
        <di:waypoint x="1260" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0br11l3_di" bpmnElement="IntermediateThrowEvent_0q5bbug">
        <dc:Bounds x="1393" y="494" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1379" y="466" width="64" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_13lgpw5_di" bpmnElement="SequenceFlow_13lgpw5">
        <di:waypoint x="1360" y="512" />
        <di:waypoint x="1393" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1cdzcmv_di" bpmnElement="Task_1yhbmgb">
        <dc:Bounds x="1035" y="731" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1mx7wtr_di" bpmnElement="SequenceFlow_1mx7wtr">
        <di:waypoint x="1110" y="871" />
        <di:waypoint x="1175" y="871" />
        <di:waypoint x="1175" y="844" />
        <di:waypoint x="1240" y="844" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1135" y="874" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_06jty8f_di" bpmnElement="SequenceFlow_06jty8f">
        <di:waypoint x="1047" y="948" />
        <di:waypoint x="1095" y="948" />
        <di:waypoint x="1095" y="964" />
        <di:waypoint x="1143" y="964" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1vxuiih_di" bpmnElement="Task_0ky3i0u">
        <dc:Bounds x="947" y="908" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1orwlmc_di" bpmnElement="Task_1ffmidb">
        <dc:Bounds x="1240" y="804" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_01x1qo9_di" bpmnElement="Lane_01x1qo9" isHorizontal="true">
        <dc:Bounds x="255" y="332" width="1218" height="120" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0qpax0e_di" bpmnElement="Task_0g2laxg">
        <dc:Bounds x="753" y="349" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1iifdaz_di" bpmnElement="ExclusiveGateway_1iifdaz" isMarkerVisible="true">
        <dc:Bounds x="554" y="819" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="543" y="879.5" width="71" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1791j4e_di" bpmnElement="SequenceFlow_1791j4e">
        <di:waypoint x="579" y="770" />
        <di:waypoint x="579" y="819" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_122au8g_di" bpmnElement="SequenceFlow_122au8g">
        <di:waypoint x="554" y="844" />
        <di:waypoint x="454" y="844" />
        <di:waypoint x="454" y="862" />
        <di:waypoint x="353" y="862" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="462" y="850" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0lbip9k_di" bpmnElement="SequenceFlow_0lbip9k">
        <di:waypoint x="604" y="844" />
        <di:waypoint x="692" y="844" />
        <di:waypoint x="692" y="771" />
        <di:waypoint x="725" y="771" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="670" y="797" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_12k2o5l_di" bpmnElement="EndEvent_12k2o5l">
        <dc:Bounds x="635" y="946" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="950" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1vnzta8_di" bpmnElement="SequenceFlow_1vnzta8">
        <di:waypoint x="770" y="933" />
        <di:waypoint x="770" y="964" />
        <di:waypoint x="671" y="964" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1mshf06_di" bpmnElement="SequenceFlow_1mshf06">
        <di:waypoint x="502" y="262" />
        <di:waypoint x="349" y="262" />
        <di:waypoint x="349" y="707" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="351" y="298" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0801eao_di" bpmnElement="SequenceFlow_0801eao">
        <di:waypoint x="1411" y="530" />
        <di:waypoint x="1411" y="613" />
        <di:waypoint x="1360" y="613" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0k8bg91_di" bpmnElement="Task_17g1j7g">
        <dc:Bounds x="1260" y="573" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1ht9urg_di" bpmnElement="ExclusiveGateway_1ht9urg" isMarkerVisible="true">
        <dc:Bounds x="1133" y="599" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1125" y="562" width="62" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1vl36r7_di" bpmnElement="SequenceFlow_1vl36r7">
        <di:waypoint x="1260" y="600" />
        <di:waypoint x="1205" y="600" />
        <di:waypoint x="1205" y="624" />
        <di:waypoint x="1183" y="624" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1oqbzfr_di" bpmnElement="SequenceFlow_1oqbzfr">
        <di:waypoint x="1133" y="624" />
        <di:waypoint x="1085" y="624" />
        <di:waypoint x="1085" y="731" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1092" y="606" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08q4c6z_di" bpmnElement="SequenceFlow_08q4c6z">
        <di:waypoint x="1158" y="649" />
        <di:waypoint x="1158" y="668" />
        <di:waypoint x="1212" y="668" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1174" y="654" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0qebmdq_di" bpmnElement="EndEvent_1d5ux0a">
        <dc:Bounds x="1207" y="638" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fwwqc5_di" bpmnElement="SequenceFlow_1fwwqc5">
        <di:waypoint x="753" y="389" />
        <di:waypoint x="726" y="389" />
        <di:waypoint x="726" y="730" />
        <di:waypoint x="648" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ikunzo_di" bpmnElement="SequenceFlow_0ikunzo">
        <di:waypoint x="982" y="552" />
        <di:waypoint x="982" y="574" />
        <di:waypoint x="897" y="574" />
        <di:waypoint x="897" y="595" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_07q4h4a_di" bpmnElement="SequenceFlow_07q4h4a">
        <di:waypoint x="1110" y="871" />
        <di:waypoint x="1161" y="871" />
        <di:waypoint x="1161" y="946" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
