<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" id="Definitions_053lmvj" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_008vpn1">
    <bpmn:participant id="Participant_1kh7vyo" name="SRN Status Update to Test" processRef="SRN-Status-Update-To-Test" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Status-Update-To-Test" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1bge26e">
      <bpmn:lane id="Lane_0jk0yud" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_CompleteAndUpdateDTH</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_07e2l45</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0587rdi" name="SHM">
        <bpmn:flowNodeRef>UserTask_ConfirmMigrationTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1rfunjz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateCatchEvent_1mv5pv6</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_IsSRNLive</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateCatchEvent_1jy3izs</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0e1bn4z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1uwvrpk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_023p81v</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1f16z8z</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_035400c" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_1yp2wlm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailBureausAfterTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0ozcl8g</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Task_1yp2wlm" name="Email bureaus on Go Live" camunda:type="external" camunda:topic="email-bureaus-on-srn-go-live">
      <bpmn:incoming>SequenceFlow_0cjhodd</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xw4ch6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="UserTask_ConfirmMigrationTesting" name="Is testing/ migration confirmed?" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsTestingMigrationConfirmed" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_156bras</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_01h21a6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0b9pc4r</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1rfunjz" name="Is confirmed?">
      <bpmn:incoming>SequenceFlow_0b9pc4r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0decq40</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1wlnm0p</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_05q07sc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_1mv5pv6" name="Test end date">
      <bpmn:incoming>SequenceFlow_0decq40</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_156bras</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_15zaxxn">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${testEndDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:userTask id="Task_IsSRNLive" name="Is SRN Live?" camunda:assignee="${stakeHolderManagerAssignee}">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsSRNLive" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07o10sh</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_154ifyz</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17c8lit</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_1jy3izs" name="Go live date">
      <bpmn:incoming>SequenceFlow_17u1fi4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07o10sh</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_05iai0i">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${goLiveDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0e1bn4z" name="Is live?">
      <bpmn:incoming>SequenceFlow_17c8lit</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17u1fi4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0cjhodd</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1sl2m65</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_EmailBureausAfterTesting" name="Email bureaus after testing" camunda:type="external" camunda:topic="email-bureaus-after-srn-testing-srn-status-update">
      <bpmn:incoming>SequenceFlow_1wlnm0p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_141cd5l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_CompleteAndUpdateDTH" name="Make it live on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_0xw4ch6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ms3enw</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1uwvrpk">
      <bpmn:incoming>SequenceFlow_141cd5l</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_154ifyz</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${goLiveDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0cjhodd" name="Yes" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="Task_1yp2wlm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0xw4ch6" sourceRef="Task_1yp2wlm" targetRef="Task_CompleteAndUpdateDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_156bras" sourceRef="IntermediateCatchEvent_1mv5pv6" targetRef="UserTask_ConfirmMigrationTesting" />
    <bpmn:sequenceFlow id="SequenceFlow_01h21a6" sourceRef="IntermediateThrowEvent_1f16z8z" targetRef="UserTask_ConfirmMigrationTesting" />
    <bpmn:sequenceFlow id="SequenceFlow_0b9pc4r" sourceRef="UserTask_ConfirmMigrationTesting" targetRef="ExclusiveGateway_1rfunjz" />
    <bpmn:sequenceFlow id="SequenceFlow_1wlnm0p" name="Yes" sourceRef="ExclusiveGateway_1rfunjz" targetRef="Task_EmailBureausAfterTesting">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0decq40" name="No" sourceRef="ExclusiveGateway_1rfunjz" targetRef="IntermediateCatchEvent_1mv5pv6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_07o10sh" sourceRef="IntermediateCatchEvent_1jy3izs" targetRef="Task_IsSRNLive" />
    <bpmn:sequenceFlow id="SequenceFlow_154ifyz" sourceRef="IntermediateThrowEvent_1uwvrpk" targetRef="Task_IsSRNLive" />
    <bpmn:sequenceFlow id="SequenceFlow_17c8lit" sourceRef="Task_IsSRNLive" targetRef="ExclusiveGateway_0e1bn4z" />
    <bpmn:sequenceFlow id="SequenceFlow_17u1fi4" name="No" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="IntermediateCatchEvent_1jy3izs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_141cd5l" sourceRef="Task_EmailBureausAfterTesting" targetRef="IntermediateThrowEvent_1uwvrpk" />
    <bpmn:sequenceFlow id="SequenceFlow_0ms3enw" sourceRef="Task_CompleteAndUpdateDTH" targetRef="EndEvent_07e2l45" />
    <bpmn:endEvent id="EndEvent_07e2l45">
      <bpmn:incoming>SequenceFlow_0ms3enw</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_05q07sc</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1sl2m65</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_187gh3f" sourceRef="StartEvent_023p81v" targetRef="Task_0ozcl8g" />
    <bpmn:sequenceFlow id="SequenceFlow_1gxpru4" sourceRef="Task_0ozcl8g" targetRef="IntermediateThrowEvent_1f16z8z" />
    <bpmn:startEvent id="StartEvent_023p81v">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="testEndDate" label="Test End Date" type="date" />
          <camunda:formField id="srnId" label="SRN Id" type="long" />
          <camunda:formField id="SRNUpdateType" label="SRN Update Type" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_187gh3f</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1f16z8z">
      <bpmn:incoming>SequenceFlow_1gxpru4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_01h21a6</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${testEndDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Task_0ozcl8g" name="Update SRN Status" camunda:type="external" camunda:topic="update-srn-status-to-test">
      <bpmn:incoming>SequenceFlow_187gh3f</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1gxpru4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_05q07sc" name="Cancel" sourceRef="ExclusiveGateway_1rfunjz" targetRef="EndEvent_07e2l45">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1sl2m65" name="Cancel" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="EndEvent_07e2l45">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:textAnnotation id="TextAnnotation_1fh5u05">
      <bpmn:text>Input "Go Live Date"</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1c6gkb2">
      <bpmn:text>Input "Go Live Date"</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_008vpn1">
      <bpmndi:BPMNShape id="Participant_1kh7vyo_di" bpmnElement="Participant_1kh7vyo" isHorizontal="true">
        <dc:Bounds x="140" y="80" width="1290" height="830" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0587rdi_di" bpmnElement="Lane_0587rdi" isHorizontal="true">
        <dc:Bounds x="170" y="80" width="1260" height="418.5" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_035400c_di" bpmnElement="Lane_035400c" isHorizontal="true">
        <dc:Bounds x="170" y="498.5" width="1260" height="221.5" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_09e25ax_di" bpmnElement="Task_CompleteAndUpdateDTH">
        <dc:Bounds x="1060" y="800" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0jk0yud_di" bpmnElement="Lane_0jk0yud" isHorizontal="true">
        <dc:Bounds x="170" y="720" width="1260" height="190" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1wlnm0p_di" bpmnElement="SequenceFlow_1wlnm0p">
        <di:waypoint x="870" y="304" />
        <di:waypoint x="870" y="600" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="849" y="340" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_09nap4b_di" bpmnElement="UserTask_ConfirmMigrationTesting">
        <dc:Bounds x="560" y="239" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1mv5pv6_di" bpmnElement="IntermediateCatchEvent_1mv5pv6" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="763" y="171" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="753" y="153" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1rfunjz_di" bpmnElement="ExclusiveGateway_1rfunjz" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="845" y="254" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="893" y="253" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_156bras_di" bpmnElement="SequenceFlow_156bras">
        <di:waypoint x="763" y="189" />
        <di:waypoint x="610" y="189" />
        <di:waypoint x="610" y="239" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0b9pc4r_di" bpmnElement="SequenceFlow_0b9pc4r">
        <di:waypoint x="660" y="279" />
        <di:waypoint x="845" y="279" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0decq40_di" bpmnElement="SequenceFlow_0decq40" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="870" y="254" />
        <di:waypoint x="870" y="189" />
        <di:waypoint x="799" y="189" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="850" y="204" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1mbfwun_di" bpmnElement="Task_EmailBureausAfterTesting">
        <dc:Bounds x="820" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_141cd5l_di" bpmnElement="SequenceFlow_141cd5l">
        <di:waypoint x="920" y="640" />
        <di:waypoint x="1020" y="640" />
        <di:waypoint x="1020" y="458" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0iycr5t_di" bpmnElement="Task_IsSRNLive">
        <dc:Bounds x="1050" y="260" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0e1bn4z_di" bpmnElement="ExclusiveGateway_0e1bn4z" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1195" y="275" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1252" y="303" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_17c8lit_di" bpmnElement="SequenceFlow_17c8lit">
        <di:waypoint x="1150" y="300" />
        <di:waypoint x="1195" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1jy3izs_di" bpmnElement="IntermediateCatchEvent_1jy3izs" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1082" y="171" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1071" y="147" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_17u1fi4_di" bpmnElement="SequenceFlow_17u1fi4">
        <di:waypoint x="1220" y="275" />
        <di:waypoint x="1220" y="189" />
        <di:waypoint x="1118" y="189" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1202" y="223" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_07o10sh_di" bpmnElement="SequenceFlow_07o10sh">
        <di:waypoint x="1100" y="207" />
        <di:waypoint x="1100" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1nam24e_di" bpmnElement="Task_1yp2wlm">
        <dc:Bounds x="1060" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0xw4ch6_di" bpmnElement="SequenceFlow_0xw4ch6">
        <di:waypoint x="1110" y="680" />
        <di:waypoint x="1110" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cjhodd_di" bpmnElement="SequenceFlow_0cjhodd">
        <di:waypoint x="1220" y="325" />
        <di:waypoint x="1220" y="640" />
        <di:waypoint x="1160" y="640" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1191" y="442" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1fh5u05_di" bpmnElement="TextAnnotation_1fh5u05">
        <dc:Bounds x="1036" y="340" width="127" height="26" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1c6gkb2_di" bpmnElement="TextAnnotation_1c6gkb2">
        <dc:Bounds x="546" y="330" width="127" height="26" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1g9qriu_di" bpmnElement="IntermediateThrowEvent_1f16z8z">
        <dc:Bounds x="432" y="261" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="540" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_154ifyz_di" bpmnElement="SequenceFlow_154ifyz">
        <di:waypoint x="1020" y="422" />
        <di:waypoint x="1020" y="300" />
        <di:waypoint x="1050" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0nuvdve_di" bpmnElement="IntermediateThrowEvent_1uwvrpk">
        <dc:Bounds x="1002" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_023p81v_di" bpmnElement="StartEvent_023p81v">
        <dc:Bounds x="296" y="261" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_01h21a6_di" bpmnElement="SequenceFlow_01h21a6">
        <di:waypoint x="468" y="279" />
        <di:waypoint x="560" y="279" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ms3enw_di" bpmnElement="SequenceFlow_0ms3enw">
        <di:waypoint x="1160" y="840" />
        <di:waypoint x="1252" y="840" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1hi5ye2_di" bpmnElement="EndEvent_07e2l45">
        <dc:Bounds x="1252" y="822" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_187gh3f_di" bpmnElement="SequenceFlow_187gh3f">
        <di:waypoint x="314" y="297" />
        <di:waypoint x="314" y="640" />
        <di:waypoint x="400" y="640" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1gxpru4_di" bpmnElement="SequenceFlow_1gxpru4">
        <di:waypoint x="450" y="600" />
        <di:waypoint x="450" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1ba37yt_di" bpmnElement="Task_0ozcl8g">
        <dc:Bounds x="400" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_05q07sc_di" bpmnElement="SequenceFlow_05q07sc">
        <di:waypoint x="895" y="279" />
        <di:waypoint x="910" y="279" />
        <di:waypoint x="910" y="120" />
        <di:waypoint x="1390" y="120" />
        <di:waypoint x="1390" y="840" />
        <di:waypoint x="1288" y="840" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1135" y="102" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1sl2m65_di" bpmnElement="SequenceFlow_1sl2m65">
        <di:waypoint x="1245" y="300" />
        <di:waypoint x="1270" y="300" />
        <di:waypoint x="1270" y="822" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1272" y="463" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
