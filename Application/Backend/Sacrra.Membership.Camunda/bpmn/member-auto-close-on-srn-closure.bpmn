<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0fsrmhp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_0m698wb">
    <bpmn:participant id="Participant_04nax1p" name="Cancel member on SRN closure - if no active SRNs" processRef="Member-Auto-Close-on-SRN-Closure" />
  </bpmn:collaboration>
  <bpmn:process id="Member-Auto-Close-on-SRN-Closure" name="Member Auto Close on SRN Closure" isExecutable="true" camunda:versionTag="1" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1mvwkge">
      <bpmn:lane id="Lane_1ozx830" name="SHM">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_cancelMember</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1ipmyvg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1tucnra</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_17q26d9</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0hx2red" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_1sdwc7l</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Task_1sdwc7l" name="Kick-off member status update workflow" camunda:type="external" camunda:topic="kickoff-member-status-update-workflow">
      <bpmn:incoming>SequenceFlow_0wsfkoa</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1z01yom</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_1" name="Start">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="memberId" label="Member ID" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1fqwdeb</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_cancelMember" name="Member has no active SRNs. Cancel Member?" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="cancelMember" label="Cancel Member?" type="enum">
            <camunda:value id="dontCancel" name="Don&#39;t Cancel" />
            <camunda:value id="cancel" name="Cancel Member" />
            <camunda:value id="postpone" name="Postpone Cancellation" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1fqwdeb</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_151jw5k</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0mwjse7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1ipmyvg" name="Postpone until date (user input)">
      <bpmn:incoming>SequenceFlow_09f0xpf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_151jw5k</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${postponeDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1tucnra">
      <bpmn:incoming>SequenceFlow_0mwjse7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0wsfkoa</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1494kzi</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_09f0xpf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="EndEvent_17q26d9" name="End">
      <bpmn:incoming>SequenceFlow_1494kzi</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1z01yom</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0mwjse7" name="Cancel member?" sourceRef="Task_cancelMember" targetRef="ExclusiveGateway_1tucnra" />
    <bpmn:sequenceFlow id="SequenceFlow_0wsfkoa" name="Cancel Member" sourceRef="ExclusiveGateway_1tucnra" targetRef="Task_1sdwc7l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelMember == "cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1494kzi" name="Don&#39;t cancel" sourceRef="ExclusiveGateway_1tucnra" targetRef="EndEvent_17q26d9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelMember == "dontCancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_09f0xpf" name="Postpone Cancellation" sourceRef="ExclusiveGateway_1tucnra" targetRef="IntermediateThrowEvent_1ipmyvg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelMember == "postpone"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1z01yom" sourceRef="Task_1sdwc7l" targetRef="EndEvent_17q26d9" />
    <bpmn:sequenceFlow id="SequenceFlow_1fqwdeb" sourceRef="StartEvent_1" targetRef="Task_cancelMember" />
    <bpmn:sequenceFlow id="SequenceFlow_151jw5k" sourceRef="IntermediateThrowEvent_1ipmyvg" targetRef="Task_cancelMember" />
    <bpmn:textAnnotation id="TextAnnotation_0fmo03w">
      <bpmn:text>Kick-off this workflow when member has no active SRNs - on SRN closure.</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0yrf3mk" sourceRef="StartEvent_1" targetRef="TextAnnotation_0fmo03w" />
    <bpmn:textAnnotation id="TextAnnotation_0sbn4rp">
      <bpmn:text>Applicable SRN statuses

  - Rejected
  - Sold
  - Closed
  - Dormant
  - Deactivated
  - Deactivated - Split
  - Deactivated - Merged</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0m698wb">
      <bpmndi:BPMNShape id="Participant_04nax1p_di" bpmnElement="Participant_04nax1p" isHorizontal="true">
        <dc:Bounds x="129" y="80" width="1001" height="460" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="252" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="258" y="225" width="25" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fqwdeb_di" bpmnElement="SequenceFlow_1fqwdeb">
        <di:waypoint x="288" y="200" />
        <di:waypoint x="400" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_1tucnra_di" bpmnElement="ExclusiveGateway_1tucnra" isMarkerVisible="true">
        <dc:Bounds x="675" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0mwjse7_di" bpmnElement="SequenceFlow_0mwjse7">
        <di:waypoint x="500" y="200" />
        <di:waypoint x="675" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="538" y="183" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0wsfkoa_di" bpmnElement="SequenceFlow_0wsfkoa">
        <di:waypoint x="725" y="200" />
        <di:waypoint x="750" y="200" />
        <di:waypoint x="750" y="470" />
        <di:waypoint x="790" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="728" y="343" width="79" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_14p8ony_di" bpmnElement="Task_1sdwc7l">
        <dc:Bounds x="790" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1494kzi_di" bpmnElement="SequenceFlow_1494kzi">
        <di:waypoint x="700" y="175" />
        <di:waypoint x="700" y="160" />
        <di:waypoint x="840" y="160" />
        <di:waypoint x="840" y="182" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="741" y="142" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0fwj69x_di" bpmnElement="EndEvent_17q26d9">
        <dc:Bounds x="822" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="792" y="193" width="20" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_09f0xpf_di" bpmnElement="SequenceFlow_09f0xpf">
        <di:waypoint x="700" y="225" />
        <di:waypoint x="700" y="290" />
        <di:waypoint x="518" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="669" y="255" width="61" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_181nlam_di" bpmnElement="Task_cancelMember">
        <dc:Bounds x="400" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1z01yom_di" bpmnElement="SequenceFlow_1z01yom">
        <di:waypoint x="840" y="430" />
        <di:waypoint x="840" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_151jw5k_di" bpmnElement="SequenceFlow_151jw5k">
        <di:waypoint x="482" y="290" />
        <di:waypoint x="450" y="290" />
        <di:waypoint x="450" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0xyfgee_di" bpmnElement="IntermediateThrowEvent_1ipmyvg">
        <dc:Bounds x="482" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="459" y="306" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1ozx830_di" bpmnElement="Lane_1ozx830" isHorizontal="true">
        <dc:Bounds x="159" y="80" width="971" height="290" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0hx2red_di" bpmnElement="Lane_0hx2red" isHorizontal="true">
        <dc:Bounds x="159" y="370" width="971" height="170" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0fmo03w_di" bpmnElement="TextAnnotation_0fmo03w">
        <dc:Bounds x="220" y="100" width="150" height="82" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0yrf3mk_di" bpmnElement="Association_0yrf3mk">
        <di:waypoint x="277" y="184" />
        <di:waypoint x="278" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_0sbn4rp_di" bpmnElement="TextAnnotation_0sbn4rp">
        <dc:Bounds x="940" y="110" width="170" height="138" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
