<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0ljajin" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.1.2">
  <bpmn:collaboration id="Collaboration_0gjfm6r">
    <bpmn:participant id="Participant_0pry0ja" name="Member Application" processRef="New-Member-Takeon" />
  </bpmn:collaboration>
  <bpmn:process id="New-Member-Takeon" isExecutable="true" camunda:versionTag="V2.0" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_0lsym2h">
      <bpmn:lane id="Lane_financialAdministrator" name="Financial Administrator">
        <bpmn:flowNodeRef>ExclusiveGateway_wasPaymentReceived</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_cancel</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_cancel</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ServiceTask_AllocateFinancialAdministrator</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_generateInvoice</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_awaitingPayment</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_wait3Days</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_checkIfPaymentReceived</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_stakeHolderManager" name="Stake Holder Manager">
        <bpmn:flowNodeRef>EndEvent_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_disqualify</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_disqualify</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_qualify</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_Review</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_allocateStakeHolderManager</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_allocateStakeHolderManagerManager</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_applicant" name="Applicant">
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_emailPaymentReminder</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_approve</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application form completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="OrganisationID" label="Organisation ID" type="long" />
          <camunda:formField id="OrganisationName" label="Organisation Name" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1w7teci</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Task_emailPaymentReminder" name="Payment and/or constitution reminder" camunda:type="external" camunda:topic="member-payment-reminder">
      <bpmn:incoming>SequenceFlow_0oaxpgw</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11vxo8n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_approve" name="Appove and email member" camunda:type="external" camunda:topic="member-approve">
      <bpmn:incoming>SequenceFlow_1g02y94</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0nptotl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_wasPaymentReceived" name="payment received?">
      <bpmn:incoming>SequenceFlow_0f1xkhj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0oaxpgw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1g02y94</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0kio5xm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_cancel" name="Cancel application and email" camunda:type="external" camunda:topic="member-cancel">
      <bpmn:incoming>SequenceFlow_0kio5xm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0gpw5sd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_cancel" name="Application cancelled due to none payment">
      <bpmn:incoming>SequenceFlow_0gpw5sd</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="ServiceTask_AllocateFinancialAdministrator" name="Allocate Financial Administrator" camunda:type="external" camunda:topic="allocate-financial-administrator">
      <bpmn:incoming>SequenceFlow_1vfbiqk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0u9emr7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_generateInvoice" name="Generate and email Member invoice&#10;" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:incoming>SequenceFlow_0u9emr7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1rq26i4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_awaitingPayment" name="Awaiting payment email and constitution accepted email" camunda:type="external" camunda:topic="member-awaiting-payment">
      <bpmn:incoming>SequenceFlow_1rq26i4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0nmdyu1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_wait3Days" name="wait for 3 working days">
      <bpmn:incoming>SequenceFlow_11vxo8n</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0nmdyu1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07r2iwc</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT3S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:userTask id="Task_checkIfPaymentReceived" name="Payment and acceptance of constitution review" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="payment-status">
            <camunda:list>
              <camunda:value>Not received</camunda:value>
              <camunda:value>Received</camunda:value>
              <camunda:value>Expired</camunda:value>
            </camunda:list>
          </camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:formData>
          <camunda:formField id="payment" label="Payment Status" type="enum">
            <camunda:value id="notReceived" name="Not Received" />
            <camunda:value id="received" name="Received" />
            <camunda:value id="expired" name="Expired" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07r2iwc</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0f1xkhj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_complete" name="Application complete">
      <bpmn:incoming>SequenceFlow_0fuauda</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:userTask id="Task_complete" name="Complete final take on" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_0nptotl</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0fuauda</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_disqualify" name="Application disqualified">
      <bpmn:incoming>SequenceFlow_0uz8yc8</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_disqualify" name="Disqualify applicant and email" camunda:type="external" camunda:topic="member-disqualify">
      <bpmn:incoming>SequenceFlow_1w6gzuf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0uz8yc8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_qualify" name="Does applicant qualify?">
      <bpmn:incoming>SequenceFlow_1t0aj10</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1w6gzuf</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1vfbiqk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_Review" name="Review and facilitate a Member Application&#10;" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="qualify" label="Does the applicant qualify?" type="enum" defaultValue="yes">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1vyt2vq</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1t0aj10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_allocateStakeHolderManager" name="Allocate a Member Application task to a Stakeholder Manager" camunda:assignee="${stakeHolderManagerManagerAssignee}" camunda:candidateGroups="StakeHolderAdministrator">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="stakeHolderManagerAssignee" label="Allocate task to assignee" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07az7ab</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1vyt2vq</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_allocateStakeHolderManagerManager" name="Allocate Stake holder administrator" camunda:type="external" camunda:topic="allocate-stake-holder-administrator">
      <bpmn:incoming>SequenceFlow_1w7teci</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07az7ab</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_07az7ab" sourceRef="Task_allocateStakeHolderManagerManager" targetRef="Task_allocateStakeHolderManager" />
    <bpmn:sequenceFlow id="SequenceFlow_1vyt2vq" sourceRef="Task_allocateStakeHolderManager" targetRef="Task_Review" />
    <bpmn:sequenceFlow id="SequenceFlow_1t0aj10" sourceRef="Task_Review" targetRef="ExclusiveGateway_qualify" />
    <bpmn:sequenceFlow id="SequenceFlow_1w6gzuf" name="no" sourceRef="ExclusiveGateway_qualify" targetRef="Task_disqualify">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${qualify == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0uz8yc8" sourceRef="Task_disqualify" targetRef="EndEvent_disqualify" />
    <bpmn:sequenceFlow id="SequenceFlow_0fuauda" sourceRef="Task_complete" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_0u9emr7" sourceRef="ServiceTask_AllocateFinancialAdministrator" targetRef="Task_generateInvoice" />
    <bpmn:sequenceFlow id="SequenceFlow_1rq26i4" sourceRef="Task_generateInvoice" targetRef="Task_awaitingPayment" />
    <bpmn:sequenceFlow id="SequenceFlow_0nmdyu1" sourceRef="Task_awaitingPayment" targetRef="IntermediateThrowEvent_wait3Days" />
    <bpmn:sequenceFlow id="SequenceFlow_07r2iwc" sourceRef="IntermediateThrowEvent_wait3Days" targetRef="Task_checkIfPaymentReceived" />
    <bpmn:sequenceFlow id="SequenceFlow_0f1xkhj" sourceRef="Task_checkIfPaymentReceived" targetRef="ExclusiveGateway_wasPaymentReceived" />
    <bpmn:sequenceFlow id="SequenceFlow_0kio5xm" name="Payment not received within grace period" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_cancel">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${payment == "expired"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0gpw5sd" sourceRef="Task_cancel" targetRef="EndEvent_cancel" />
    <bpmn:sequenceFlow id="SequenceFlow_0nptotl" sourceRef="Task_approve" targetRef="Task_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1g02y94" name="Payment Received" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${payment == "received"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_11vxo8n" sourceRef="Task_emailPaymentReminder" targetRef="IntermediateThrowEvent_wait3Days" />
    <bpmn:sequenceFlow id="SequenceFlow_0oaxpgw" name="Payment not received but within grace period" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_emailPaymentReminder">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${payment == "notReceived"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1vfbiqk" name="yes" sourceRef="ExclusiveGateway_qualify" targetRef="ServiceTask_AllocateFinancialAdministrator">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${qualify == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1w7teci" sourceRef="StartEvent_applicationFormCompleted" targetRef="Task_allocateStakeHolderManagerManager" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0gjfm6r">
      <bpmndi:BPMNShape id="Participant_0pry0ja_di" bpmnElement="Participant_0pry0ja" isHorizontal="true">
        <dc:Bounds x="156" y="81" width="1227" height="477" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1f6x8yb_di" bpmnElement="Lane_applicant" isHorizontal="true">
        <dc:Bounds x="186" y="81" width="1197" height="139" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_02jojaj_di" bpmnElement="Lane_stakeHolderManager" isHorizontal="true">
        <dc:Bounds x="186" y="220" width="1197" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_17svdmu_di" bpmnElement="Lane_financialAdministrator" isHorizontal="true">
        <dc:Bounds x="186" y="360" width="1197" height="198" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_14fq3ur_di" bpmnElement="StartEvent_applicationFormCompleted">
        <dc:Bounds x="263" y="138" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="242" y="101" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_11dvgjq_di" bpmnElement="Task_Review">
        <dc:Bounds x="500" y="256" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1w7teci_di" bpmnElement="SequenceFlow_1w7teci">
        <di:waypoint x="281" y="174" />
        <di:waypoint x="281" y="256" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1vyt2vq_di" bpmnElement="SequenceFlow_1vyt2vq">
        <di:waypoint x="460" y="296" />
        <di:waypoint x="500" y="296" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0orik0k_di" bpmnElement="ExclusiveGateway_qualify" isMarkerVisible="true">
        <dc:Bounds x="632" y="271" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="620" y="234" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1w6gzuf_di" bpmnElement="SequenceFlow_1w6gzuf">
        <di:waypoint x="682" y="296" />
        <di:waypoint x="723" y="296" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="696" y="279" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0wxkgne_di" bpmnElement="Task_disqualify">
        <dc:Bounds x="723" y="256" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0uz8yc8_di" bpmnElement="SequenceFlow_0uz8yc8">
        <di:waypoint x="823" y="294" />
        <di:waypoint x="856" y="294" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1t0aj10_di" bpmnElement="SequenceFlow_1t0aj10">
        <di:waypoint x="600" y="296" />
        <di:waypoint x="632" y="296" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1vfbiqk_di" bpmnElement="SequenceFlow_1vfbiqk">
        <di:waypoint x="657" y="321" />
        <di:waypoint x="657" y="412" />
        <di:waypoint x="281" y="412" />
        <di:waypoint x="281" y="433" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="632" y="320" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1b6n62o_di" bpmnElement="Task_generateInvoice">
        <dc:Bounds x="360" y="433" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0amrg2m_di" bpmnElement="IntermediateThrowEvent_wait3Days">
        <dc:Bounds x="639" y="455" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="615" y="498" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0w457v8_di" bpmnElement="Task_checkIfPaymentReceived">
        <dc:Bounds x="723" y="433" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07r2iwc_di" bpmnElement="SequenceFlow_07r2iwc">
        <di:waypoint x="675" y="473" />
        <di:waypoint x="723" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0oaxpgw_di" bpmnElement="SequenceFlow_0oaxpgw">
        <di:waypoint x="939" y="448" />
        <di:waypoint x="939" y="196" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="949" y="269" width="63" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0f1xkhj_di" bpmnElement="SequenceFlow_0f1xkhj">
        <di:waypoint x="823" y="473" />
        <di:waypoint x="914" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1eyr0mh_di" bpmnElement="Task_emailPaymentReminder">
        <dc:Bounds x="879" y="116" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_11vxo8n_di" bpmnElement="SequenceFlow_11vxo8n">
        <di:waypoint x="929" y="196" />
        <di:waypoint x="929" y="427" />
        <di:waypoint x="657" y="427" />
        <di:waypoint x="657" y="455" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1g02y94_di" bpmnElement="SequenceFlow_1g02y94">
        <di:waypoint x="964" y="473" />
        <di:waypoint x="1083" y="473" />
        <di:waypoint x="1083" y="196" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1031" y="442" width="46" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0bt1y8h_di" bpmnElement="Task_approve">
        <dc:Bounds x="1033" y="116" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_10d3hq8_di" bpmnElement="Task_complete">
        <dc:Bounds x="1148" y="256" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0nptotl_di" bpmnElement="SequenceFlow_0nptotl">
        <di:waypoint x="1133" y="156" />
        <di:waypoint x="1198" y="156" />
        <di:waypoint x="1198" y="256" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0fuauda_di" bpmnElement="SequenceFlow_0fuauda">
        <di:waypoint x="1248" y="296" />
        <di:waypoint x="1304" y="296" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_13qxm0z_di" bpmnElement="EndEvent_disqualify">
        <dc:Bounds x="856" y="276" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="319" width="56" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1dhwaud_di" bpmnElement="EndEvent_complete">
        <dc:Bounds x="1304" y="278" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1296" y="321" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0kio5xm_di" bpmnElement="SequenceFlow_0kio5xm">
        <di:waypoint x="939" y="498" />
        <di:waypoint x="939" y="530" />
        <di:waypoint x="1198" y="530" />
        <di:waypoint x="1198" y="513" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1068" y="484" width="73" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1nj8n50_di" bpmnElement="Task_cancel">
        <dc:Bounds x="1148" y="433" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0pa7s5q_di" bpmnElement="EndEvent_cancel">
        <dc:Bounds x="1304" y="455" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1282" y="498" width="81" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0gpw5sd_di" bpmnElement="SequenceFlow_0gpw5sd">
        <di:waypoint x="1248" y="473" />
        <di:waypoint x="1304" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1rq26i4_di" bpmnElement="SequenceFlow_1rq26i4">
        <di:waypoint x="460" y="473" />
        <di:waypoint x="500" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1nwyczk_di" bpmnElement="Task_awaitingPayment">
        <dc:Bounds x="500" y="433" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0nmdyu1_di" bpmnElement="SequenceFlow_0nmdyu1">
        <di:waypoint x="600" y="473" />
        <di:waypoint x="639" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vw6etr_di" bpmnElement="ExclusiveGateway_wasPaymentReceived" isMarkerVisible="true">
        <dc:Bounds x="914" y="448" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="949" y="421" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_11a70om_di" bpmnElement="ServiceTask_AllocateFinancialAdministrator">
        <dc:Bounds x="231" y="433" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0u9emr7_di" bpmnElement="SequenceFlow_0u9emr7">
        <di:waypoint x="331" y="473" />
        <di:waypoint x="360" y="473" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1rjismp_di" bpmnElement="Task_allocateStakeHolderManager">
        <dc:Bounds x="360" y="256" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hfk2cn_di" bpmnElement="Task_allocateStakeHolderManagerManager">
        <dc:Bounds x="231" y="256" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07az7ab_di" bpmnElement="SequenceFlow_07az7ab">
        <di:waypoint x="331" y="296" />
        <di:waypoint x="360" y="296" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
