<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="Definitions_05qu6ti" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="uncofirmedbuyerprocess">
    <bpmn:participant id="Participant_0lm1kc9" name="Splitting / Merging / Selling SRN" processRef="SRN-Split-Merge-Sell" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Split-Merge-Sell" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_08iuoh9">
      <bpmn:lane id="Lane_0v8wj5f" name="Seller SHM">
        <bpmn:flowNodeRef>Task_124gbtg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_SellerReview</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_0gqc0v3</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0n5z0x8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0bbv1c5</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1ryv7df</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>SubProcess_1v2w46t</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_13l74jx</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_05wyq4l" name="Buyer SHM">
        <bpmn:flowNodeRef>Task_ConfirmMigrationTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_09ogz29</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_01lndbl</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1dhijvk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1ypmxly</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_SHMMergeSplitSaleReview</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0b3k6po</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_0gzbtvt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1672igz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_04626jy</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_03y58jf</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_00kj9qn</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0xircvj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0tzdzlt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0t36pqh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1opd8iv</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_05v6vqq</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>UserTask_13sgjvh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateCatchEvent_19bg4j5</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0i34a4c</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0r6j9i4</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_01oeaj2</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0xqe0uo" name="Seller">
        <bpmn:flowNodeRef>ExclusiveGateway_182gz9z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>merge-or-split-request</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0s0vj6h" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_CompleteAndUpdateDTH</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:userTask id="Task_ConfirmMigrationTesting" name="Confirm completion of testing and/or migration" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsTestingMigrationConfirmed" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_02f8oto</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0vdxkow</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0w9qltk</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_09ogz29" name="User defined date">
      <bpmn:incoming>SequenceFlow_0neh41n</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vdxkow</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT10S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_01lndbl" name="Testing / Migration required?">
      <bpmn:incoming>SequenceFlow_1wbee98</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xuo3pw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_02f8oto</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1dhijvk" name="Is confirmed?">
      <bpmn:incoming>SequenceFlow_0w9qltk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_01zlvko</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0neh41n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_1ypmxly" name="Allocate SRN from seller to buyer member profile" camunda:type="external" camunda:topic="re-allocate-srn-from-seller-to-buyer">
      <bpmn:incoming>SequenceFlow_076l0jq</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0d4fl4o</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_124gbtg" name="Notify request cancellation" camunda:type="external" camunda:topic="notify-member-of-SRN-sale-request-cancellation">
      <bpmn:incoming>SequenceFlow_1tg67q3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1lp40rh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_SellerReview" name="Seller SHM Reviews and Completes Request" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="reviewsale" label="Review Decision" type="enum">
            <camunda:value id="Cancel" name="Not Approved" />
            <camunda:value id="UnconfirmedCreate" name="Create Unconfirmed Buyer" />
            <camunda:value id="Pending" name="Pend Application" />
            <camunda:value id="FinaliseUnconfirmedSale" name="Finalise Unconfirmed Sale" />
            <camunda:value id="Approved" name="Request Approved" />
          </camunda:formField>
          <camunda:formField id="BuyerMemberId" label="Member ID of Buyer" type="long" />
          <camunda:formField id="SRNSaleRequestId" label="SRN Sale Request ID" type="long" />
          <camunda:formField id="ReviewCommentsSeller" label="Review Comments" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_10acc72</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0pohss8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1hqpspk</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_0gqc0v3">
      <bpmn:incoming>SequenceFlow_08uqsxt</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0ptsuoc</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_10acc72</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT10S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0n5z0x8" name="Evaluate Request">
      <bpmn:incoming>SequenceFlow_1hqpspk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1tg67q3</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_08ig7ao</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1341s72</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0f5k404</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0ptsuoc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_0bbv1c5" name="Delink SRN  from seller and advise  Seller(State=Sold unconfirmed)" camunda:type="external" camunda:topic="re-allocate-srn-from-seller-to-buyer">
      <bpmn:incoming>SequenceFlow_1341s72</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0i4f44s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_1ryv7df">
      <bpmn:incoming>SequenceFlow_0i4f44s</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0wp1x7j" sourceRef="ExclusiveGateway_03y58jf" targetRef="Task_00kj9qn" />
    <bpmn:sequenceFlow id="SequenceFlow_0qddjf5" name="Full" sourceRef="ExclusiveGateway_0xircvj" targetRef="ExclusiveGateway_03y58jf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${SaleType=="full"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_00vjhuv" name="Partial" sourceRef="ExclusiveGateway_0xircvj" targetRef="Task_00kj9qn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${SaleType=="partial"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1tg67q3" name="Cancel Request" sourceRef="ExclusiveGateway_0n5z0x8" targetRef="Task_124gbtg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewsale =="Cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1hqpspk" sourceRef="Task_SellerReview" targetRef="ExclusiveGateway_0n5z0x8" />
    <bpmn:sequenceFlow id="SequenceFlow_0tonbie" sourceRef="IntermediateThrowEvent_0gzbtvt" targetRef="Task_SHMMergeSplitSaleReview" />
    <bpmn:sequenceFlow id="SequenceFlow_1lp40rh" sourceRef="Task_124gbtg" targetRef="EndEvent_13l74jx" />
    <bpmn:sequenceFlow id="SequenceFlow_08ig7ao" name="Approved" sourceRef="ExclusiveGateway_0n5z0x8" targetRef="Task_SHMMergeSplitSaleReview">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewsale=="Approved"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0x9i9go" sourceRef="ExclusiveGateway_1672igz" targetRef="Task_04626jy" />
    <bpmn:sequenceFlow id="SequenceFlow_0z0y4uy" sourceRef="ExclusiveGateway_1672igz" targetRef="IntermediateThrowEvent_0gzbtvt" />
    <bpmn:sequenceFlow id="SequenceFlow_0xuo3pw" name="No" sourceRef="ExclusiveGateway_01lndbl" targetRef="ExclusiveGateway_0xircvj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationRequired=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_02f8oto" name="Yes" sourceRef="ExclusiveGateway_01lndbl" targetRef="Task_ConfirmMigrationTesting">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationRequired=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0w9qltk" sourceRef="Task_ConfirmMigrationTesting" targetRef="ExclusiveGateway_1dhijvk" />
    <bpmn:sequenceFlow id="SequenceFlow_01zlvko" name="Yes" sourceRef="ExclusiveGateway_1dhijvk" targetRef="Task_05v6vqq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0neh41n" name="No" sourceRef="ExclusiveGateway_1dhijvk" targetRef="IntermediateThrowEvent_09ogz29">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0vdxkow" sourceRef="IntermediateThrowEvent_09ogz29" targetRef="Task_ConfirmMigrationTesting" />
    <bpmn:sequenceFlow id="SequenceFlow_16qu614" sourceRef="Task_SHMMergeSplitSaleReview" targetRef="ExclusiveGateway_0b3k6po" />
    <bpmn:sequenceFlow id="SequenceFlow_19o9nsy" name="Pending" sourceRef="ExclusiveGateway_0b3k6po" targetRef="ExclusiveGateway_1672igz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${CompatibleSRNExists=="pending"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0i4f44s" sourceRef="Task_0bbv1c5" targetRef="EndEvent_1ryv7df" />
    <bpmn:sequenceFlow id="SequenceFlow_1341s72" name="Shm Finalises Unaccepted Sale" sourceRef="ExclusiveGateway_0n5z0x8" targetRef="Task_0bbv1c5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewsale=="FinaliseUnconfirmedSale"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_10acc72" sourceRef="IntermediateThrowEvent_0gqc0v3" targetRef="Task_SellerReview" />
    <bpmn:sequenceFlow id="SequenceFlow_02zgcle" sourceRef="merge-or-split-request" targetRef="ExclusiveGateway_182gz9z" />
    <bpmn:sequenceFlow id="SequenceFlow_0f5k404" name="Buyer Does Not Exist" sourceRef="ExclusiveGateway_0n5z0x8" targetRef="SubProcess_1v2w46t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewsale =="UnconfirmedCreate"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_08uqsxt" sourceRef="SubProcess_1v2w46t" targetRef="IntermediateThrowEvent_0gqc0v3" />
    <bpmn:sequenceFlow id="SequenceFlow_0ptsuoc" name="Pend Request" sourceRef="ExclusiveGateway_0n5z0x8" targetRef="IntermediateThrowEvent_0gqc0v3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewsale=="Pending"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1s0jp1x" name="Yes" sourceRef="ExclusiveGateway_0b3k6po" targetRef="Task_0t36pqh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${CompatibleSRNExists=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1wbee98" sourceRef="Task_0t36pqh" targetRef="ExclusiveGateway_01lndbl" />
    <bpmn:subProcess id="SubProcess_1v2w46t" name="Unconfirmed Buyer Sub Process">
      <bpmn:incoming>SequenceFlow_0f5k404</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_08uqsxt</bpmn:outgoing>
      <bpmn:serviceTask id="Task_notifySellAboutUnregisteredBuyer" name="Notify seller that buyer is not registered" camunda:type="external" camunda:topic="notify-seller-about-unregistered-buyer">
        <bpmn:incoming>SequenceFlow_14xl0ml</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1mi9ab4</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:userTask id="Task_createUnconfirmedBuyer" name="Create Unconfrmed Buyer" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
        <bpmn:incoming>SequenceFlow_01um0pb</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0w5hfjh</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:manualTask id="Task_00c1yen" name="Notifiy buyer to start new member registration process">
        <bpmn:incoming>SequenceFlow_0il6qj8</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1ce2u8r</bpmn:outgoing>
      </bpmn:manualTask>
      <bpmn:parallelGateway id="ExclusiveGateway_0koppk4">
        <bpmn:incoming>SequenceFlow_09yx0fm</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0il6qj8</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_01um0pb</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_14xl0ml</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:startEvent id="StartEvent_startUnconfirmedBuyerSubProcess">
        <bpmn:outgoing>SequenceFlow_09yx0fm</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_09yx0fm" sourceRef="StartEvent_startUnconfirmedBuyerSubProcess" targetRef="ExclusiveGateway_0koppk4" />
      <bpmn:sequenceFlow id="SequenceFlow_0il6qj8" sourceRef="ExclusiveGateway_0koppk4" targetRef="Task_00c1yen" />
      <bpmn:sequenceFlow id="SequenceFlow_01um0pb" sourceRef="ExclusiveGateway_0koppk4" targetRef="Task_createUnconfirmedBuyer" />
      <bpmn:parallelGateway id="ExclusiveGateway_0qutki5">
        <bpmn:incoming>SequenceFlow_1mi9ab4</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_1ce2u8r</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_0w5hfjh</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_01o0s4j</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:sequenceFlow id="SequenceFlow_1mi9ab4" sourceRef="Task_notifySellAboutUnregisteredBuyer" targetRef="ExclusiveGateway_0qutki5" />
      <bpmn:sequenceFlow id="SequenceFlow_1ce2u8r" sourceRef="Task_00c1yen" targetRef="ExclusiveGateway_0qutki5" />
      <bpmn:sequenceFlow id="SequenceFlow_0w5hfjh" sourceRef="Task_createUnconfirmedBuyer" targetRef="ExclusiveGateway_0qutki5" />
      <bpmn:endEvent id="EndEvent_0yi57c3">
        <bpmn:incoming>SequenceFlow_01o0s4j</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="SequenceFlow_01o0s4j" sourceRef="ExclusiveGateway_0qutki5" targetRef="EndEvent_0yi57c3" />
      <bpmn:sequenceFlow id="SequenceFlow_14xl0ml" sourceRef="ExclusiveGateway_0koppk4" targetRef="Task_notifySellAboutUnregisteredBuyer" />
    </bpmn:subProcess>
    <bpmn:userTask id="Task_SHMMergeSplitSaleReview" name="SHM Reviews and Completes Request" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="CompatibleSRNExists" label="Compatible SRN Exists?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="pending" name="Pending" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
          <camunda:formField id="IsTestingMigrationRequired" label="Is Testing/Migration Required?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
          <camunda:formField id="BuyerSRNId" label="Buyer SRN ID" type="long" />
          <camunda:formField id="ReviewCommentsBuyer" label="Review Comments" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0tonbie</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_08ig7ao</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1kmfgzy</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_16qu614</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0b3k6po" name="Compatible?">
      <bpmn:incoming>SequenceFlow_16qu614</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19o9nsy</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1s0jp1x</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_14pz6lz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_0gzbtvt">
      <bpmn:incoming>SequenceFlow_0z0y4uy</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0tonbie</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT10S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:parallelGateway id="ExclusiveGateway_1672igz">
      <bpmn:incoming>SequenceFlow_19o9nsy</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0x9i9go</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0z0y4uy</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Task_04626jy" name="Send member notification to start new SRN registration process" camunda:type="external" camunda:topic="notify-buyer-member-to-start-new-srn-registration-process">
      <bpmn:incoming>SequenceFlow_0x9i9go</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="ExclusiveGateway_03y58jf">
      <bpmn:incoming>SequenceFlow_0qddjf5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0wp1x7j</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_076l0jq</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Task_00kj9qn" name="Notify DTH,  Bureus and SHM of sale file submissioncompletion" camunda:type="external" camunda:topic="notify-bureus-and-shm-of-sale-file-submission-completion">
      <bpmn:incoming>SequenceFlow_0wp1x7j</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_00vjhuv</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0nvws9p</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0xircvj" name="Is Full/Partial Sale?">
      <bpmn:incoming>SequenceFlow_0xuo3pw</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1k2ep1i</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0qddjf5</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_00vjhuv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_182gz9z">
      <bpmn:incoming>SequenceFlow_02zgcle</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1kmfgzy</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0pohss8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1kmfgzy" name="Split / Merge" sourceRef="ExclusiveGateway_182gz9z" targetRef="Task_SHMMergeSplitSaleReview">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${RequestType=="merge" || RequestType=="split"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="EndEvent_13l74jx" name="Request Cancelled">
      <bpmn:incoming>SequenceFlow_1lp40rh</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0pohss8" name="Sale" sourceRef="ExclusiveGateway_182gz9z" targetRef="Task_SellerReview">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${RequestType=="sale"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="merge-or-split-request" name="Merge / Split / Sell Request">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="MergeToSRNId" label="SRN ID To Merge To" type="long" />
          <camunda:formField id="SRNIdMergeList" label="IDs of SRNs To Be Merged" type="string" />
          <camunda:formField id="SplitFromSRNId" label="SRN ID To Split From" type="long" />
          <camunda:formField id="SRNIdSplitList" label="IDs of SRNs To Be Split To" type="string" />
          <camunda:formField id="RequestType" label="Is it Sale or Split/Merge" type="enum">
            <camunda:value id="sale" name="Sale" />
            <camunda:value id="splitOrMerge" name="Merge / Split" />
          </camunda:formField>
          <camunda:formField id="SRNIdToBeSold" label="SRN ID To Be Sold" type="long" />
          <camunda:formField id="BuyerRegisteredName" type="string" />
          <camunda:formField id="BuyerRegisteredNumber" type="string" />
          <camunda:formField id="SaleType" type="enum">
            <camunda:value id="full" name="Full Sale" />
            <camunda:value id="partial" name="Partial Sale" />
          </camunda:formField>
          <camunda:formField id="InitialStatusId" label="SRN Initial Status Id" type="long" />
          <camunda:formField id="MergeListInitialStatusIds" label="List of SRN Ids and initial status Ids" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_02zgcle</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_076l0jq" sourceRef="ExclusiveGateway_03y58jf" targetRef="Task_1ypmxly" />
    <bpmn:sequenceFlow id="SequenceFlow_0d4fl4o" sourceRef="Task_1ypmxly" targetRef="EndEvent_01oeaj2" />
    <bpmn:sequenceFlow id="SequenceFlow_0nvws9p" sourceRef="Task_00kj9qn" targetRef="UserTask_13sgjvh" />
    <bpmn:serviceTask id="Task_0tzdzlt" name="Notify member about cancellation" camunda:type="external" camunda:topic="notify-member-about-srn-split-or-merge-cancellation">
      <bpmn:incoming>SequenceFlow_14pz6lz</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_01p1lz5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_14pz6lz" name="Cancel" sourceRef="ExclusiveGateway_0b3k6po" targetRef="Task_0tzdzlt">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${CompatibleSRNExists=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_01p1lz5" sourceRef="Task_0tzdzlt" targetRef="EndEvent_1opd8iv" />
    <bpmn:serviceTask id="Task_0t36pqh" name="Notify seller and SHM of sale request commencement" camunda:type="external" camunda:topic="notify-seller-and-shm-of-sale-request-commencement">
      <bpmn:incoming>SequenceFlow_1s0jp1x</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wbee98</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_1opd8iv">
      <bpmn:incoming>SequenceFlow_01p1lz5</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_05v6vqq" name="Update SRN Status to &#34;Test&#34;" camunda:type="external" camunda:topic="split-merge-sale-update-srn-status-after-testing">
      <bpmn:incoming>SequenceFlow_01zlvko</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1k2ep1i</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1k2ep1i" sourceRef="Task_05v6vqq" targetRef="ExclusiveGateway_0xircvj" />
    <bpmn:userTask id="UserTask_13sgjvh" name="Is SRN Live?" camunda:assignee="${stakeHolderManagerAssignee}">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="goLiveDate" label="Go Live Date" type="string" />
          <camunda:formField id="IsSRNLive" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0buazpb</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0nvws9p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1ppprgh</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_19bg4j5" name="User defined date">
      <bpmn:incoming>SequenceFlow_11qso51</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0buazpb</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1mj50uc">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT10S</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0i34a4c" name="Is live?">
      <bpmn:incoming>SequenceFlow_1ppprgh</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11qso51</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0jpfmc0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0buazpb" sourceRef="IntermediateCatchEvent_19bg4j5" targetRef="UserTask_13sgjvh" />
    <bpmn:sequenceFlow id="SequenceFlow_1ppprgh" sourceRef="UserTask_13sgjvh" targetRef="ExclusiveGateway_0i34a4c" />
    <bpmn:sequenceFlow id="SequenceFlow_11qso51" name="No" sourceRef="ExclusiveGateway_0i34a4c" targetRef="IntermediateCatchEvent_19bg4j5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Task_0r6j9i4" name="Email bureaus on Go Live" camunda:type="external" camunda:topic="split-merge-sale-email-bureaus-on-srn-go-live">
      <bpmn:incoming>SequenceFlow_0jpfmc0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0r8v128</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0jpfmc0" name="Yes" sourceRef="ExclusiveGateway_0i34a4c" targetRef="Task_0r6j9i4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_162ktjl" sourceRef="Task_CompleteAndUpdateDTH" targetRef="EndEvent_01oeaj2" />
    <bpmn:sequenceFlow id="SequenceFlow_0r8v128" sourceRef="Task_0r6j9i4" targetRef="Task_CompleteAndUpdateDTH" />
    <bpmn:userTask id="Task_CompleteAndUpdateDTH" name="Update status on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_0r8v128</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_162ktjl</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_01oeaj2">
      <bpmn:incoming>SequenceFlow_0d4fl4o</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_162ktjl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:textAnnotation id="TextAnnotation_06yof3r">
      <bpmn:text>SHM can set follow-up, with maximum 1 month intervals</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1gxfugm">
      <bpmn:text>See Balsamic Request Screen
Seller will indicate if full/partial sale</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_02wx5h8">
      <bpmn:text>If buyer does not exist Approved and Finalise Sale Action Items not available</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1i0msq0" sourceRef="IntermediateThrowEvent_0gzbtvt" targetRef="TextAnnotation_06yof3r" />
    <bpmn:association id="Association_0jh8fpu" sourceRef="Task_SHMMergeSplitSaleReview" targetRef="TextAnnotation_1h3db22" />
    <bpmn:association id="Association_1uiaykt" sourceRef="Task_SellerReview" targetRef="TextAnnotation_02wx5h8" />
    <bpmn:textAnnotation id="TextAnnotation_1h3db22">
      <bpmn:text>Go-live always captured

Migration/Test date optional

Buyer SHM verify SRN types compatible

Checks if test and migration dates required</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1vx9txj">
      <bpmn:text>Based on Dates Input. Only if request is Sale</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_0s72glw">
      <bpmn:text>Input "Go Live Date"</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="uncofirmedbuyerprocess">
      <bpmndi:BPMNShape id="Participant_0lm1kc9_di" bpmnElement="Participant_0lm1kc9" isHorizontal="true">
        <dc:Bounds x="156" y="40" width="1726" height="1340" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1ugo1q6_di" bpmnElement="Task_0t36pqh">
        <dc:Bounds x="669" y="940" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_18m2kl3_di" bpmnElement="Task_1ypmxly">
        <dc:Bounds x="875.5" y="1083" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_1jehxyl_di" bpmnElement="ExclusiveGateway_03y58jf">
        <dc:Bounds x="1090.5" y="1098" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0xircvj_di" bpmnElement="ExclusiveGateway_0xircvj" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1090.5" y="961" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1013" y="1001" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_121grbc_di" bpmnElement="IntermediateThrowEvent_0gzbtvt">
        <dc:Bounds x="440.5" y="935" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_08mxvlw_di" bpmnElement="ExclusiveGateway_1672igz">
        <dc:Bounds x="550.5" y="928" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0sz7qrf_di" bpmnElement="Task_SHMMergeSplitSaleReview">
        <dc:Bounds x="323.5" y="771" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_1gjaoqf_di" bpmnElement="ExclusiveGateway_0koppk4">
        <dc:Bounds x="1324.5" y="480" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0n5z0x8_di" bpmnElement="ExclusiveGateway_0n5z0x8" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="536.5" y="486" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="527.5" y="449" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_08xou45_di" bpmnElement="Task_SellerReview">
        <dc:Bounds x="511.5" y="320" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0as4v9j_di" bpmnElement="Task_124gbtg">
        <dc:Bounds x="381.5" y="320" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0wp1x7j_di" bpmnElement="SequenceFlow_0wp1x7j">
        <di:waypoint x="1140" y="1123" />
        <di:waypoint x="1249" y="1123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0qddjf5_di" bpmnElement="SequenceFlow_0qddjf5" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1116" y="1010" />
        <di:waypoint x="1116" y="1098" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1123" y="1051" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_00vjhuv_di" bpmnElement="SequenceFlow_00vjhuv" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1141" y="986" />
        <di:waypoint x="1300" y="986" />
        <di:waypoint x="1300" y="1083" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1264" y="964" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1tg67q3_di" bpmnElement="SequenceFlow_1tg67q3" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="536.5" y="511" />
        <di:waypoint x="431.5" y="511" />
        <di:waypoint x="431.5" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="445" y="493" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1hqpspk_di" bpmnElement="SequenceFlow_1hqpspk">
        <di:waypoint x="561.5" y="400" />
        <di:waypoint x="561.5" y="486" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0tonbie_di" bpmnElement="SequenceFlow_0tonbie">
        <di:waypoint x="441" y="953" />
        <di:waypoint x="374" y="953" />
        <di:waypoint x="374" y="851" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1lp40rh_di" bpmnElement="SequenceFlow_1lp40rh">
        <di:waypoint x="399" y="400" />
        <di:waypoint x="399" y="559" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08ig7ao_di" bpmnElement="SequenceFlow_08ig7ao" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="562" y="536" />
        <di:waypoint x="562" y="631" />
        <di:waypoint x="412" y="631" />
        <di:waypoint x="412" y="771" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="466" y="639" width="47" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0x9i9go_di" bpmnElement="SequenceFlow_0x9i9go">
        <di:waypoint x="576" y="978" />
        <di:waypoint x="576" y="1047" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0z0y4uy_di" bpmnElement="SequenceFlow_0z0y4uy">
        <di:waypoint x="551" y="953" />
        <di:waypoint x="476" y="953" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1h3db22_di" bpmnElement="TextAnnotation_1h3db22">
        <dc:Bounds x="202.5" y="726" width="100" height="209" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0jh8fpu_di" bpmnElement="Association_0jh8fpu">
        <di:waypoint x="323" y="819" />
        <di:waypoint x="302" y="823" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1gxfugm_di" bpmnElement="TextAnnotation_1gxfugm">
        <dc:Bounds x="638.5" y="80" width="100" height="96" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0v8wj5f_di" bpmnElement="Lane_0v8wj5f" isHorizontal="true">
        <dc:Bounds x="186" y="301" width="1696" height="416" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0xqe0uo_di" bpmnElement="Lane_0xqe0uo" isHorizontal="true">
        <dc:Bounds x="180" y="40" width="1696" height="261" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ManualTask_1mn4ep4_di" bpmnElement="Task_00c1yen">
        <dc:Bounds x="1406.5" y="465" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1sa8hvt_di" bpmnElement="Task_notifySellAboutUnregisteredBuyer">
        <dc:Bounds x="1406.5" y="372" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_06yof3r_di" bpmnElement="TextAnnotation_06yof3r">
        <dc:Bounds x="301.5" y="1045" width="100" height="68" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1i0msq0_di" bpmnElement="Association_1i0msq0">
        <di:waypoint x="448" y="967" />
        <di:waypoint x="380" y="1045" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_05wyq4l_di" bpmnElement="Lane_05wyq4l" isHorizontal="true">
        <dc:Bounds x="186" y="717" width="1696" height="543" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_01lndbl_di" bpmnElement="ExclusiveGateway_01lndbl" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="831.5" y="786" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="833" y="739" width="47" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0xuo3pw_di" bpmnElement="SequenceFlow_0xuo3pw" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="856" y="836" />
        <di:waypoint x="856.5" y="986" />
        <di:waypoint x="1091" y="986" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="865" y="908" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02f8oto_di" bpmnElement="SequenceFlow_02f8oto" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="881.5" y="811" />
        <di:waypoint x="944.5" y="811" />
        <di:waypoint x="944.5" y="857" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="905" y="791" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_1o2v6e5_di" bpmnElement="Task_ConfirmMigrationTesting">
        <dc:Bounds x="923.5" y="857" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1dhijvk_di" bpmnElement="ExclusiveGateway_1dhijvk" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1090.5" y="851" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1120" y="843" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0w9qltk_di" bpmnElement="SequenceFlow_0w9qltk">
        <di:waypoint x="1023.5" y="876" />
        <di:waypoint x="1090.5" y="876" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01zlvko_di" bpmnElement="SequenceFlow_01zlvko" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1141" y="876" />
        <di:waypoint x="1350" y="876" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1209" y="858" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0neh41n_di" bpmnElement="SequenceFlow_0neh41n" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1115.5" y="851" />
        <di:waypoint x="1115.5" y="759" />
        <di:waypoint x="991.5" y="759" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1121" y="779" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0fj0nnr_di" bpmnElement="IntermediateThrowEvent_09ogz29" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="955.5" y="741" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="930.5" y="717" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0vdxkow_di" bpmnElement="SequenceFlow_0vdxkow">
        <di:waypoint x="973.5" y="777" />
        <di:waypoint x="973.5" y="857" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_03ybxli_di" bpmnElement="Task_0bbv1c5">
        <dc:Bounds x="831.5" y="596" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0domf90_di" bpmnElement="Task_createUnconfirmedBuyer">
        <dc:Bounds x="1406.5" y="564" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0b3k6po_di" bpmnElement="ExclusiveGateway_0b3k6po" isMarkerVisible="true">
        <dc:Bounds x="550.5" y="786" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="489" y="793" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_16qu614_di" bpmnElement="SequenceFlow_16qu614">
        <di:waypoint x="423" y="811" />
        <di:waypoint x="550" y="811" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19o9nsy_di" bpmnElement="SequenceFlow_19o9nsy" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="576" y="835" />
        <di:waypoint x="576" y="928" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="529" y="869" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0i4f44s_di" bpmnElement="SequenceFlow_0i4f44s">
        <di:waypoint x="931.5" y="636" />
        <di:waypoint x="1048.5" y="636" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1vx9txj_di" bpmnElement="TextAnnotation_1vx9txj">
        <dc:Bounds x="669" y="1030" width="100" height="53" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hnayte_di" bpmnElement="Task_00kj9qn">
        <dc:Bounds x="1249.5" y="1083" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1341s72_di" bpmnElement="SequenceFlow_1341s72" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="573.5" y="524" />
        <di:waypoint x="686.5" y="646" />
        <di:waypoint x="831.5" y="646" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="694" y="610" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1pma2mt_di" bpmnElement="IntermediateThrowEvent_0gqc0v3">
        <dc:Bounds x="670.5" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_10acc72_di" bpmnElement="SequenceFlow_10acc72">
        <di:waypoint x="670.5" y="360" />
        <di:waypoint x="611.5" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_18l18gv_di" bpmnElement="merge-or-split-request" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="334" y="124" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="328" y="86" width="69" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_02zgcle_di" bpmnElement="SequenceFlow_02zgcle">
        <di:waypoint x="352" y="160" />
        <di:waypoint x="352" y="213" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SubProcess_1v2w46t_di" bpmnElement="SubProcess_1v2w46t" isExpanded="true">
        <dc:Bounds x="1220.5" y="352" width="466" height="341" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1oyd6bs_di" bpmnElement="StartEvent_startUnconfirmedBuyerSubProcess">
        <dc:Bounds x="1240.5" y="487" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_09yx0fm_di" bpmnElement="SequenceFlow_09yx0fm">
        <di:waypoint x="1276.5" y="505" />
        <di:waypoint x="1324.5" y="505" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0il6qj8_di" bpmnElement="SequenceFlow_0il6qj8">
        <di:waypoint x="1374.5" y="505" />
        <di:waypoint x="1406.5" y="505" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01um0pb_di" bpmnElement="SequenceFlow_01um0pb">
        <di:waypoint x="1349.5" y="530" />
        <di:waypoint x="1349.5" y="604" />
        <di:waypoint x="1406.5" y="604" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ParallelGateway_1hn8jp3_di" bpmnElement="ExclusiveGateway_0qutki5">
        <dc:Bounds x="1540.5" y="480" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1mi9ab4_di" bpmnElement="SequenceFlow_1mi9ab4">
        <di:waypoint x="1506.5" y="412" />
        <di:waypoint x="1565.5" y="412" />
        <di:waypoint x="1565.5" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1ce2u8r_di" bpmnElement="SequenceFlow_1ce2u8r">
        <di:waypoint x="1506.5" y="505" />
        <di:waypoint x="1540.5" y="505" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0w5hfjh_di" bpmnElement="SequenceFlow_0w5hfjh">
        <di:waypoint x="1506.5" y="604" />
        <di:waypoint x="1565.5" y="604" />
        <di:waypoint x="1565.5" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0yi57c3_di" bpmnElement="EndEvent_0yi57c3">
        <dc:Bounds x="1624.5" y="487" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_01o0s4j_di" bpmnElement="SequenceFlow_01o0s4j">
        <di:waypoint x="1590.5" y="505" />
        <di:waypoint x="1624.5" y="505" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0f5k404_di" bpmnElement="SequenceFlow_0f5k404" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="586" y="511" />
        <di:waypoint x="1221" y="511" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1010" y="484" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08uqsxt_di" bpmnElement="SequenceFlow_08uqsxt">
        <di:waypoint x="1343" y="352" />
        <di:waypoint x="1343" y="333" />
        <di:waypoint x="856" y="333" />
        <di:waypoint x="856" y="360" />
        <di:waypoint x="706" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ptsuoc_di" bpmnElement="SequenceFlow_0ptsuoc" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="575" y="499" />
        <di:waypoint x="688.5" y="453" />
        <di:waypoint x="689" y="378" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="693" y="408" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0g4vup1_di" bpmnElement="EndEvent_13l74jx" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="381" y="559" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="384" y="521" width="49" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_11h5i7v_di" bpmnElement="EndEvent_1ryv7df">
        <dc:Bounds x="1048.5" y="618" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_02wx5h8_di" bpmnElement="TextAnnotation_02wx5h8">
        <dc:Bounds x="638.5" y="190" width="100" height="96" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1uiaykt_di" bpmnElement="Association_1uiaykt">
        <di:waypoint x="603.5" y="320" />
        <di:waypoint x="638.5" y="286" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0hun7i7_di" bpmnElement="Task_04626jy">
        <dc:Bounds x="525.5" y="1047" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1s0jp1x_di" bpmnElement="SequenceFlow_1s0jp1x">
        <di:waypoint x="601" y="811" />
        <di:waypoint x="620" y="811" />
        <di:waypoint x="620" y="969" />
        <di:waypoint x="669" y="969" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="600" y="850" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wbee98_di" bpmnElement="SequenceFlow_1wbee98">
        <di:waypoint x="769" y="969" />
        <di:waypoint x="800" y="969" />
        <di:waypoint x="800" y="811" />
        <di:waypoint x="832" y="811" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_182gz9z_di" bpmnElement="ExclusiveGateway_182gz9z" isMarkerVisible="true">
        <dc:Bounds x="327" y="213" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1kmfgzy_di" bpmnElement="SequenceFlow_1kmfgzy">
        <di:waypoint x="352" y="263" />
        <di:waypoint x="352" y="771" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="285" y="487" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0pohss8_di" bpmnElement="SequenceFlow_0pohss8">
        <di:waypoint x="377" y="238" />
        <di:waypoint x="562" y="238" />
        <di:waypoint x="562" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="458" y="220" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_14xl0ml_di" bpmnElement="SequenceFlow_14xl0ml">
        <di:waypoint x="1350" y="480" />
        <di:waypoint x="1350" y="412" />
        <di:waypoint x="1407" y="412" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_076l0jq_di" bpmnElement="SequenceFlow_076l0jq">
        <di:waypoint x="1091" y="1123" />
        <di:waypoint x="976" y="1123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0dadofh_di" bpmnElement="EndEvent_01oeaj2">
        <dc:Bounds x="908" y="1192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0d4fl4o_di" bpmnElement="SequenceFlow_0d4fl4o">
        <di:waypoint x="926" y="1163" />
        <di:waypoint x="926" y="1192" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0nvws9p_di" bpmnElement="SequenceFlow_0nvws9p">
        <di:waypoint x="1350" y="1123" />
        <di:waypoint x="1380" y="1123" />
        <di:waypoint x="1380" y="960" />
        <di:waypoint x="1560" y="960" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1lxgy7z_di" bpmnElement="Task_0tzdzlt">
        <dc:Bounds x="669" y="750" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_14pz6lz_di" bpmnElement="SequenceFlow_14pz6lz">
        <di:waypoint x="576" y="786" />
        <di:waypoint x="576" y="770" />
        <di:waypoint x="669" y="770" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="602" y="753" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01p1lz5_di" bpmnElement="SequenceFlow_01p1lz5">
        <di:waypoint x="719" y="830" />
        <di:waypoint x="719" y="862" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_11lv2rj_di" bpmnElement="EndEvent_1opd8iv">
        <dc:Bounds x="701" y="862" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jjn7b3_di" bpmnElement="Task_05v6vqq">
        <dc:Bounds x="1350" y="836" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1k2ep1i_di" bpmnElement="SequenceFlow_1k2ep1i">
        <di:waypoint x="1400" y="916" />
        <di:waypoint x="1400" y="940" />
        <di:waypoint x="1116" y="940" />
        <di:waypoint x="1116" y="961" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_13sgjvh_di" bpmnElement="UserTask_13sgjvh">
        <dc:Bounds x="1560" y="913" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_19bg4j5_di" bpmnElement="IntermediateCatchEvent_19bg4j5" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1592" y="824" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1567" y="800" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0i34a4c_di" bpmnElement="ExclusiveGateway_0i34a4c" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1705" y="928" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1762" y="956" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0s72glw_di" bpmnElement="TextAnnotation_0s72glw">
        <dc:Bounds x="1546" y="993" width="127" height="26" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0buazpb_di" bpmnElement="SequenceFlow_0buazpb">
        <di:waypoint x="1610" y="860" />
        <di:waypoint x="1610" y="913" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1ppprgh_di" bpmnElement="SequenceFlow_1ppprgh">
        <di:waypoint x="1660" y="953" />
        <di:waypoint x="1705" y="953" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_11qso51_di" bpmnElement="SequenceFlow_11qso51">
        <di:waypoint x="1730" y="928" />
        <di:waypoint x="1730" y="842" />
        <di:waypoint x="1628" y="842" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1712" y="876" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0alrwtx_di" bpmnElement="Task_0r6j9i4">
        <dc:Bounds x="1560" y="1080" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0jpfmc0_di" bpmnElement="SequenceFlow_0jpfmc0">
        <di:waypoint x="1730" y="978" />
        <di:waypoint x="1730" y="1120" />
        <di:waypoint x="1660" y="1120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1736" y="1046" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0fesx6q_di" bpmnElement="Task_CompleteAndUpdateDTH">
        <dc:Bounds x="1330" y="1290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_162ktjl_di" bpmnElement="SequenceFlow_162ktjl">
        <di:waypoint x="1330" y="1330" />
        <di:waypoint x="926" y="1330" />
        <di:waypoint x="926" y="1228" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0r8v128_di" bpmnElement="SequenceFlow_0r8v128">
        <di:waypoint x="1610" y="1160" />
        <di:waypoint x="1610" y="1330" />
        <di:waypoint x="1430" y="1330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_0s0vj6h_di" bpmnElement="Lane_0s0vj6h" isHorizontal="true">
        <dc:Bounds x="186" y="1260" width="1696" height="120" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
