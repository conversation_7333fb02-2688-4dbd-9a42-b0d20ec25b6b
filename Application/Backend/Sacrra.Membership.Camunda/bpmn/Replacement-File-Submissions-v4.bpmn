<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1u16m1o" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.33.1">
  <bpmn:collaboration id="Collaboration_08el55p">
    <bpmn:participant id="Participant_18ntsv7" name="Replacement File Submissions" processRef="Replacement-File-Submissions" />
  </bpmn:collaboration>
  <bpmn:process id="Replacement-File-Submissions" isExecutable="true" camunda:versionTag="v1" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_0krkdtg">
      <bpmn:lane id="Lane_18vryie" name="Member (DC)">
        <bpmn:flowNodeRef>StartEvent_0ez8fys</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0dcqyk4" name="SACRRA Connect">
        <bpmn:flowNodeRef>Task_1nnnv9r</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_0sgo8za</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1uv9ysy</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_15mj1cj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_103bixs</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0oegtkl</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1818c1y</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_112vxdz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0vux7fj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_15o5ury</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0f79kht</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_04s2j3h" name="SHM">
        <bpmn:flowNodeRef>Task_0f2g5y0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_12ytprk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1nyudk8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1o8o7h8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1z0kosv</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="SequenceFlow_1cncl0b" sourceRef="StartEvent_0ez8fys" targetRef="Task_0f2g5y0" />
    <bpmn:userTask id="Task_0f2g5y0" name="Review replacement file submission request" camunda:assignee="${SHMId.toString()}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="reviewDecision" label="SHM Review Decision" type="string" />
          <camunda:formField id="reasonForDecline" label="Reason For Decline" type="long" />
          <camunda:formField id="plannedSubmissionDate" label="Planned Submission Date" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1cncl0b</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1tx2xze</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="SequenceFlow_1tx2xze" sourceRef="Task_0f2g5y0" targetRef="ExclusiveGateway_12ytprk" />
    <bpmn:sequenceFlow id="SequenceFlow_0pd5fmw" name="Approved" sourceRef="ExclusiveGateway_12ytprk" targetRef="Task_15mj1cj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewDecision=="approved"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1dvv5ia" name="Declined" sourceRef="ExclusiveGateway_12ytprk" targetRef="Task_1nnnv9r">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${reviewDecision=="declined"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_13pw84h" sourceRef="IntermediateThrowEvent_0sgo8za" targetRef="Task_1uv9ysy" />
    <bpmn:sequenceFlow id="SequenceFlow_1yawf6u" sourceRef="Task_1uv9ysy" targetRef="ExclusiveGateway_0vux7fj" />
    <bpmn:sequenceFlow id="SequenceFlow_03lxzom" name="File not submitted" sourceRef="ExclusiveGateway_0vux7fj" targetRef="Task_1nyudk8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fileSubmittedOnPlannedDate=="notSubmitted"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0a6402x" name="File submitted" sourceRef="ExclusiveGateway_0vux7fj" targetRef="Task_112vxdz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fileSubmittedOnPlannedDate=="submitted"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_12ytprk" name="Approved / Declined?">
      <bpmn:incoming>SequenceFlow_1tx2xze</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0pd5fmw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1dvv5ia</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:startEvent id="StartEvent_0ez8fys" name="Replacement File Submission Requested" camunda:formKey="SRNId">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="SRNId" label="SRN ID" type="long" />
          <camunda:formField id="FileSubmissionRequestId" label="Replacement File Submission Request" type="long" />
          <camunda:formField id="SHMId" label="Stakeholder Manager ID" type="long">
            <camunda:properties />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1cncl0b</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1on14xj" sourceRef="Task_1nyudk8" targetRef="ExclusiveGateway_1o8o7h8" />
    <bpmn:sequenceFlow id="SequenceFlow_02age6v" sourceRef="Task_1nnnv9r" targetRef="Task_0oegtkl" />
    <bpmn:sequenceFlow id="SequenceFlow_10hg484" sourceRef="Task_0oegtkl" targetRef="EndEvent_103bixs" />
    <bpmn:serviceTask id="Task_1nnnv9r" name="Update file submission as Declined" camunda:type="external" camunda:topic="replacement_file_submission_update_status_declined">
      <bpmn:incoming>SequenceFlow_1dvv5ia</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_02age6v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_0sgo8za" name="Wait for planned submit date">
      <bpmn:incoming>SequenceFlow_1uc9kgp</bpmn:incoming>
      <bpmn:incoming>Flow_1sh97yk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_13pw84h</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${dateTime().parse(plannedSubmissionDate).plusDays(1).toDate()}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Task_1uv9ysy" name="Check if file was submitted to the DTH" camunda:type="external" camunda:topic="replacement_file_submission_check_file_submitted_to_dth">
      <bpmn:incoming>SequenceFlow_13pw84h</bpmn:incoming>
      <bpmn:incoming>Flow_1a32vr6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1yawf6u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_1nyudk8" name="Replacement File Submission Due" camunda:assignee="${SHMId.toString()}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData />
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_03lxzom</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1on14xj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1o8o7h8" name="Postponed/Submitted/Cancelled?">
      <bpmn:incoming>SequenceFlow_1on14xj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1uc9kgp</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_154krnk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a32vr6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1uc9kgp" name="Submit date postponed" sourceRef="ExclusiveGateway_1o8o7h8" targetRef="IntermediateThrowEvent_0sgo8za">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fileSubmittedOnPlannedDate=="submitDatePostponed"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_154krnk" name="File not submitted / cancel task" sourceRef="ExclusiveGateway_1o8o7h8" targetRef="Activity_1z0kosv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fileSubmittedOnPlannedDate=="submitted" || fileSubmittedOnPlannedDate=="cancelled"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Task_15mj1cj" name="Update file submission as Approved" camunda:type="external" camunda:topic="replacement_file_submission_update_status_approved">
      <bpmn:incoming>SequenceFlow_0pd5fmw</bpmn:incoming>
      <bpmn:outgoing>Flow_03hmexk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_103bixs">
      <bpmn:incoming>SequenceFlow_10hg484</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_0oegtkl" name="Send email to member on decline" camunda:type="external" camunda:topic="replacement_file_submission_email_member_declined">
      <bpmn:incoming>SequenceFlow_02age6v</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_10hg484</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_1818c1y">
      <bpmn:incoming>SequenceFlow_1kolsc1</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_112vxdz" name="Update file submission as Submitted/Cancelled" camunda:type="external" camunda:topic="replacement_file_submission_update_submitted_cancelled">
      <bpmn:incoming>SequenceFlow_0a6402x</bpmn:incoming>
      <bpmn:incoming>Flow_1qnpsl9</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1kolsc1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1kolsc1" sourceRef="Task_112vxdz" targetRef="EndEvent_1818c1y" />
    <bpmn:serviceTask id="Activity_1z0kosv" name="Email Member &#38; Bureaus On Cancellation" camunda:type="external" camunda:topic="replacement_file_submission_email_member_bureaus_cancelled">
      <bpmn:incoming>SequenceFlow_154krnk</bpmn:incoming>
      <bpmn:outgoing>Flow_1qnpsl9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1qnpsl9" sourceRef="Activity_1z0kosv" targetRef="Task_112vxdz" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_0vux7fj" name="Was the file submitted?">
      <bpmn:incoming>SequenceFlow_1yawf6u</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_03lxzom</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0a6402x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1a32vr6" name="Recheck if submitted to DTH" sourceRef="ExclusiveGateway_1o8o7h8" targetRef="Task_1uv9ysy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${recheckDthSubmission == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_15o5ury" name="Createfile on FTP site" camunda:type="external" camunda:topic="replacement_file_submission_create_file_ftpserver">
      <bpmn:incoming>Flow_03hmexk</bpmn:incoming>
      <bpmn:outgoing>Flow_03xu4m1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_03hmexk" sourceRef="Task_15mj1cj" targetRef="Activity_15o5ury" />
    <bpmn:sequenceFlow id="Flow_03xu4m1" sourceRef="Activity_15o5ury" targetRef="Task_0f79kht" />
    <bpmn:serviceTask id="Task_0f79kht" name="Send email to member &#38; bureaus on approve" camunda:type="external" camunda:topic="replacement_file_submission_email_member_bureaus_approved">
      <bpmn:incoming>Flow_03xu4m1</bpmn:incoming>
      <bpmn:outgoing>Flow_1sh97yk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1sh97yk" sourceRef="Task_0f79kht" targetRef="IntermediateThrowEvent_0sgo8za" />
    <bpmn:association id="Association_1l9mlyy" sourceRef="IntermediateThrowEvent_0sgo8za" targetRef="TextAnnotation_0yv6s5w" />
    <bpmn:textAnnotation id="TextAnnotation_0yv6s5w">
      <bpmn:text>Could be triggered by daily cron job</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_08el55p">
      <bpmndi:BPMNShape id="Participant_18ntsv7_di" bpmnElement="Participant_18ntsv7" isHorizontal="true">
        <dc:Bounds x="129" y="80" width="1899" height="760" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_04s2j3h_di" bpmnElement="Lane_04s2j3h" isHorizontal="true">
        <dc:Bounds x="159" y="590" width="1869" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0dcqyk4_di" bpmnElement="Lane_0dcqyk4" isHorizontal="true">
        <dc:Bounds x="159" y="210" width="1869" height="380" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_18vryie_di" bpmnElement="Lane_18vryie" isHorizontal="true">
        <dc:Bounds x="159" y="80" width="1869" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1b2v9hp_di" bpmnElement="Task_0f2g5y0">
        <dc:Bounds x="270" y="670" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_12ytprk_di" bpmnElement="ExclusiveGateway_12ytprk" isMarkerVisible="true">
        <dc:Bounds x="575" y="685" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="574" y="742" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0ez8fys_di" bpmnElement="StartEvent_0ez8fys">
        <dc:Bounds x="302" y="152" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="277" y="102" width="86" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1c7355i_di" bpmnElement="Task_1nnnv9r">
        <dc:Bounds x="550" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0ked2gj_di" bpmnElement="IntermediateThrowEvent_0sgo8za">
        <dc:Bounds x="1082" y="312" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1060" y="274.5" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1y6bctc_di" bpmnElement="Task_1uv9ysy">
        <dc:Bounds x="1250" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1edc2os_di" bpmnElement="Task_1nyudk8">
        <dc:Bounds x="1250" y="670" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1o8o7h8_di" bpmnElement="ExclusiveGateway_1o8o7h8" isMarkerVisible="true">
        <dc:Bounds x="1135" y="685" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1056" y="726" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0oebwhy_di" bpmnElement="Task_15mj1cj">
        <dc:Bounds x="690" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0bdbxkt_di" bpmnElement="EndEvent_103bixs">
        <dc:Bounds x="422" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_072qtk4_di" bpmnElement="Task_0oegtkl">
        <dc:Bounds x="390" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0abx7nm_di" bpmnElement="EndEvent_1818c1y">
        <dc:Bounds x="1892" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_18sd3d7_di" bpmnElement="Task_112vxdz">
        <dc:Bounds x="1740" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07ue8lm" bpmnElement="Activity_1z0kosv">
        <dc:Bounds x="1540" y="740" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vux7fj_di" bpmnElement="ExclusiveGateway_0vux7fj" isMarkerVisible="true">
        <dc:Bounds x="1445" y="505" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1363" y="520" width="57" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_12togs1_di" bpmnElement="Activity_15o5ury">
        <dc:Bounds x="850" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0mqqlfq_di" bpmnElement="Task_0f79kht">
        <dc:Bounds x="850" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0yv6s5w_di" bpmnElement="TextAnnotation_0yv6s5w">
        <dc:Bounds x="1140" y="363" width="100" height="54" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1cncl0b_di" bpmnElement="SequenceFlow_1cncl0b">
        <di:waypoint x="320" y="188" />
        <di:waypoint x="320" y="670" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1tx2xze_di" bpmnElement="SequenceFlow_1tx2xze">
        <di:waypoint x="370" y="710" />
        <di:waypoint x="575" y="710" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0pd5fmw_di" bpmnElement="SequenceFlow_0pd5fmw">
        <di:waypoint x="625" y="710" />
        <di:waypoint x="740" y="710" />
        <di:waypoint x="740" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="660" y="692" width="47" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1dvv5ia_di" bpmnElement="SequenceFlow_1dvv5ia">
        <di:waypoint x="600" y="685" />
        <di:waypoint x="600" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="549" y="599" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_13pw84h_di" bpmnElement="SequenceFlow_13pw84h">
        <di:waypoint x="1118" y="330" />
        <di:waypoint x="1250" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1yawf6u_di" bpmnElement="SequenceFlow_1yawf6u">
        <di:waypoint x="1350" y="330" />
        <di:waypoint x="1470" y="330" />
        <di:waypoint x="1470" y="505" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_03lxzom_di" bpmnElement="SequenceFlow_03lxzom">
        <di:waypoint x="1470" y="555" />
        <di:waypoint x="1470" y="710" />
        <di:waypoint x="1350" y="710" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1376" y="636" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0a6402x_di" bpmnElement="SequenceFlow_0a6402x">
        <di:waypoint x="1495" y="530" />
        <di:waypoint x="1740" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1590" y="513" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1on14xj_di" bpmnElement="SequenceFlow_1on14xj">
        <di:waypoint x="1250" y="710" />
        <di:waypoint x="1185" y="710" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02age6v_di" bpmnElement="SequenceFlow_02age6v">
        <di:waypoint x="550" y="510" />
        <di:waypoint x="490" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_10hg484_di" bpmnElement="SequenceFlow_10hg484">
        <di:waypoint x="440" y="470" />
        <di:waypoint x="440" y="398" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1uc9kgp_di" bpmnElement="SequenceFlow_1uc9kgp">
        <di:waypoint x="1135" y="710" />
        <di:waypoint x="1100" y="710" />
        <di:waypoint x="1100" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1030" y="626" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_154krnk_di" bpmnElement="SequenceFlow_154krnk">
        <di:waypoint x="1160" y="735" />
        <di:waypoint x="1160" y="780" />
        <di:waypoint x="1540" y="780" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1396" y="786" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1kolsc1_di" bpmnElement="SequenceFlow_1kolsc1">
        <di:waypoint x="1840" y="530" />
        <di:waypoint x="1892" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qnpsl9_di" bpmnElement="Flow_1qnpsl9">
        <di:waypoint x="1640" y="780" />
        <di:waypoint x="1790" y="780" />
        <di:waypoint x="1790" y="570" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a32vr6_di" bpmnElement="Flow_1a32vr6">
        <di:waypoint x="1160" y="685" />
        <di:waypoint x="1160" y="630" />
        <di:waypoint x="1290" y="630" />
        <di:waypoint x="1290" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1185" y="596" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1l9mlyy_di" bpmnElement="Association_1l9mlyy">
        <di:waypoint x="1115" y="340" />
        <di:waypoint x="1150" y="363" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03hmexk_di" bpmnElement="Flow_03hmexk">
        <di:waypoint x="790" y="510" />
        <di:waypoint x="850" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03xu4m1_di" bpmnElement="Flow_03xu4m1">
        <di:waypoint x="900" y="470" />
        <di:waypoint x="900" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sh97yk_di" bpmnElement="Flow_1sh97yk">
        <di:waypoint x="950" y="330" />
        <di:waypoint x="1082" y="330" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
