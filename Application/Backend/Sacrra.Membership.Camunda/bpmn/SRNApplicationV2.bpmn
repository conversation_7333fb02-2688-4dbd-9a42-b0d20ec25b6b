<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_053lmvj" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.10.0">
  <bpmn:collaboration id="Collaboration_008vpn1">
    <bpmn:participant id="Participant_1kh7vyo" name="SRN Application" processRef="New-SRN-Application" />
  </bpmn:collaboration>
  <bpmn:process id="New-SRN-Application" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1bge26e">
      <bpmn:lane id="Lane_0jk0yud" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_AddTestSRNToDTH</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_CompleteAndUpdateDTH</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0587rdi" name="SHM / Member">
        <bpmn:flowNodeRef>Task_SHMReview</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_14q9hgp</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_035400c" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_CreateSRNNumber</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_allocateStakeHolderManager</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailMember</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailBureaus</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1xgcprg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1p0mnwn</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_NotifyApplicantOfSRNApplicationRejection</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1yp2wlm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1qjpd52</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_1ybbrkz</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:userTask id="Task_SHMReview" name="SHM Reviews SRN(s) Application" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="srnVerified1" label="Is the SRN verified?" type="enum" defaultValue="yes">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0v5q0w0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1hhhnnm</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_CreateSRNNumber" name="Create SRN Number" camunda:type="external" camunda:topic="create-srn-number">
      <bpmn:incoming>SequenceFlow_1tdlkv4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0uhq8le</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application form completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="MemberId" label="Member ID" type="long" />
          <camunda:formField id="SRNId" label="SRN ID" type="long" />
          <camunda:formField id="fileType" label="SRN File Type" type="string" />
          <camunda:formField id="requireTesting" label="SRN Testing Required?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0h2lihi</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Task_allocateStakeHolderManager" name="Allocate a SRN Application task to a SHM" camunda:type="external" camunda:topic="allocate-stake-holder-manager">
      <bpmn:incoming>SequenceFlow_0h2lihi</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0v5q0w0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EmailMember" name="Email Member" camunda:type="external" camunda:topic="email-member">
      <bpmn:incoming>SequenceFlow_0awyrts</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1p6gq55</bpmn:outgoing>
      <bpmn:outgoing>Flow_1076960</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EmailBureaus" name="Email Bureaus before testing&#10;" camunda:type="external" camunda:topic="email-bureaus-before-srn-testing">
      <bpmn:incoming>SequenceFlow_03pv4cu</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0awyrts</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1xgcprg" name="Testing Required?">
      <bpmn:incoming>SequenceFlow_0uhq8le</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_03pv4cu</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0sol88w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_AddTestSRNToDTH" name="Create Test SRN on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_1p6gq55</bpmn:incoming>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CompleteAndUpdateDTH" name="Make it live on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_0xw4ch6</bpmn:incoming>
      <bpmn:outgoing>Flow_01gylve</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_1p0mnwn" name="Update SRN Status to Live" camunda:type="external" camunda:topic="update-srn-status-to-live">
      <bpmn:incoming>SequenceFlow_0sol88w</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0jretbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0jretbt" sourceRef="Task_1p0mnwn" targetRef="Task_1yp2wlm" />
    <bpmn:sequenceFlow id="SequenceFlow_0xw4ch6" sourceRef="Task_1yp2wlm" targetRef="Task_CompleteAndUpdateDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_1hhhnnm" sourceRef="Task_SHMReview" targetRef="ExclusiveGateway_14q9hgp" />
    <bpmn:sequenceFlow id="SequenceFlow_1tdlkv4" name="Yes" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_CreateSRNNumber">
      <bpmn:extensionElements>
        <camunda:executionListener expression="" event="take" />
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnVerified1 == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_19mqij6" name="Reject SRN application" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_NotifyApplicantOfSRNApplicationRejection">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnVerified1 == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0v5q0w0" sourceRef="Task_allocateStakeHolderManager" targetRef="Task_SHMReview" />
    <bpmn:sequenceFlow id="SequenceFlow_0uhq8le" sourceRef="Task_CreateSRNNumber" targetRef="ExclusiveGateway_1xgcprg" />
    <bpmn:sequenceFlow id="SequenceFlow_0h2lihi" sourceRef="StartEvent_applicationFormCompleted" targetRef="Task_allocateStakeHolderManager" />
    <bpmn:sequenceFlow id="SequenceFlow_0awyrts" sourceRef="Task_EmailBureaus" targetRef="Task_EmailMember" />
    <bpmn:sequenceFlow id="SequenceFlow_1p6gq55" sourceRef="Task_EmailMember" targetRef="Task_AddTestSRNToDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_03pv4cu" name="Yes" sourceRef="ExclusiveGateway_1xgcprg" targetRef="Task_EmailBureaus">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireTesting=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0sol88w" name="No" sourceRef="ExclusiveGateway_1xgcprg" targetRef="Task_1p0mnwn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireTesting=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1076960" sourceRef="Task_EmailMember" targetRef="Activity_1qjpd52" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_14q9hgp" name="Is Verified">
      <bpmn:incoming>SequenceFlow_1hhhnnm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1tdlkv4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_19mqij6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_NotifyApplicantOfSRNApplicationRejection" name="Notify applicant of SRN application rejection" camunda:type="external" camunda:topic="notify-applicant-of-srn-application-rejection">
      <bpmn:incoming>SequenceFlow_19mqij6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1rp3yrd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_1yp2wlm" name="Email bureaus on Go Live" camunda:type="external" camunda:topic="email-bureaus-on-srn-go-live">
      <bpmn:incoming>SequenceFlow_0jretbt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xw4ch6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1qjpd52" name="Call File Testing Process" camunda:type="external" camunda:topic="call_file_test_subprocess">
      <bpmn:incoming>Flow_1076960</bpmn:incoming>
      <bpmn:outgoing>Flow_1xousjr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_01gylve" sourceRef="Task_CompleteAndUpdateDTH" targetRef="StartEvent_1ybbrkz" />
    <bpmn:sequenceFlow id="Flow_1xousjr" sourceRef="Activity_1qjpd52" targetRef="StartEvent_1ybbrkz" />
    <bpmn:sequenceFlow id="SequenceFlow_1rp3yrd" sourceRef="Task_NotifyApplicantOfSRNApplicationRejection" targetRef="StartEvent_1ybbrkz" />
    <bpmn:endEvent id="StartEvent_1ybbrkz">
      <bpmn:incoming>Flow_01gylve</bpmn:incoming>
      <bpmn:incoming>Flow_1xousjr</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1rp3yrd</bpmn:incoming>
    </bpmn:endEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_008vpn1">
      <bpmndi:BPMNShape id="Participant_1kh7vyo_di" bpmnElement="Participant_1kh7vyo" isHorizontal="true">
        <dc:Bounds x="160" y="82" width="1350" height="1188" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_035400c_di" bpmnElement="Lane_035400c" isHorizontal="true">
        <dc:Bounds x="190" y="630" width="1320" height="460" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0587rdi_di" bpmnElement="Lane_0587rdi" isHorizontal="true">
        <dc:Bounds x="190" y="82" width="1320" height="548" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0jk0yud_di" bpmnElement="Lane_0jk0yud" isHorizontal="true">
        <dc:Bounds x="190" y="1090" width="1320" height="180" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_12x7cdl_di" bpmnElement="Task_SHMReview">
        <dc:Bounds x="780" y="137" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0we9zcl_di" bpmnElement="Task_CreateSRNNumber">
        <dc:Bounds x="270" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0ntmvkx_di" bpmnElement="StartEvent_applicationFormCompleted">
        <dc:Bounds x="462" y="462" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="441" y="425" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0ra0ipd_di" bpmnElement="Task_allocateStakeHolderManager">
        <dc:Bounds x="500" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1d7vd4l_di" bpmnElement="Task_EmailMember">
        <dc:Bounds x="740" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1u6jkua_di" bpmnElement="Task_EmailBureaus">
        <dc:Bounds x="580" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1xgcprg_di" bpmnElement="ExclusiveGateway_1xgcprg" isMarkerVisible="true">
        <dc:Bounds x="435" y="795" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="417" y="773" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1fft1wn_di" bpmnElement="Task_AddTestSRNToDTH">
        <dc:Bounds x="740" y="1140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_09e25ax_di" bpmnElement="Task_CompleteAndUpdateDTH">
        <dc:Bounds x="1200" y="1140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1h7dhkl_di" bpmnElement="Task_1p0mnwn">
        <dc:Bounds x="520" y="980" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_14q9hgp_di" bpmnElement="ExclusiveGateway_14q9hgp" isMarkerVisible="true">
        <dc:Bounds x="1085" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1141" y="162" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_10qh3n4_di" bpmnElement="Task_NotifyApplicantOfSRNApplicationRejection">
        <dc:Bounds x="1060" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1nam24e_di" bpmnElement="Task_1yp2wlm">
        <dc:Bounds x="1200" y="980" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0q8pv93_di" bpmnElement="Activity_1qjpd52">
        <dc:Bounds x="1060" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1cwycfw_di" bpmnElement="StartEvent_1ybbrkz">
        <dc:Bounds x="1232" y="662" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0jretbt_di" bpmnElement="SequenceFlow_0jretbt">
        <di:waypoint x="620" y="1020" />
        <di:waypoint x="1200" y="1020" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0xw4ch6_di" bpmnElement="SequenceFlow_0xw4ch6">
        <di:waypoint x="1250" y="1060" />
        <di:waypoint x="1250" y="1140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1hhhnnm_di" bpmnElement="SequenceFlow_1hhhnnm">
        <di:waypoint x="880" y="177" />
        <di:waypoint x="1085" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1tdlkv4_di" bpmnElement="SequenceFlow_1tdlkv4">
        <di:waypoint x="1110" y="152" />
        <di:waypoint x="1110" y="110" />
        <di:waypoint x="320" y="110" />
        <di:waypoint x="320" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="772" y="115" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19mqij6_di" bpmnElement="SequenceFlow_19mqij6">
        <di:waypoint x="1110" y="202" />
        <di:waypoint x="1110" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1041" y="381" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0v5q0w0_di" bpmnElement="SequenceFlow_0v5q0w0">
        <di:waypoint x="600" y="690" />
        <di:waypoint x="620" y="690" />
        <di:waypoint x="620" y="177" />
        <di:waypoint x="780" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0uhq8le_di" bpmnElement="SequenceFlow_0uhq8le">
        <di:waypoint x="320" y="730" />
        <di:waypoint x="320" y="820" />
        <di:waypoint x="435" y="820" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0h2lihi_di" bpmnElement="SequenceFlow_0h2lihi">
        <di:waypoint x="480" y="498" />
        <di:waypoint x="480" y="690" />
        <di:waypoint x="500" y="690" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1rp3yrd_di" bpmnElement="SequenceFlow_1rp3yrd">
        <di:waypoint x="1160" y="680" />
        <di:waypoint x="1232" y="680" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0awyrts_di" bpmnElement="SequenceFlow_0awyrts">
        <di:waypoint x="680" y="820" />
        <di:waypoint x="740" y="820" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1p6gq55_di" bpmnElement="SequenceFlow_1p6gq55">
        <di:waypoint x="790" y="860" />
        <di:waypoint x="790" y="1140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_03pv4cu_di" bpmnElement="SequenceFlow_03pv4cu">
        <di:waypoint x="485" y="820" />
        <di:waypoint x="580" y="820" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="524" y="802" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sol88w_di" bpmnElement="SequenceFlow_0sol88w">
        <di:waypoint x="460" y="845" />
        <di:waypoint x="460" y="1020" />
        <di:waypoint x="520" y="1020" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="473" y="893" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1076960_di" bpmnElement="Flow_1076960">
        <di:waypoint x="840" y="820" />
        <di:waypoint x="1060" y="820" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xousjr_di" bpmnElement="Flow_1xousjr">
        <di:waypoint x="1160" y="820" />
        <di:waypoint x="1250" y="820" />
        <di:waypoint x="1250" y="698" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01gylve_di" bpmnElement="Flow_01gylve">
        <di:waypoint x="1300" y="1180" />
        <di:waypoint x="1340" y="1180" />
        <di:waypoint x="1340" y="680" />
        <di:waypoint x="1268" y="680" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
