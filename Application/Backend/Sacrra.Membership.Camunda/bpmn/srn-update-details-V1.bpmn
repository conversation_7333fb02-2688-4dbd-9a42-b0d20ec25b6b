<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_041nw4y" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.1.2">
  <bpmn:collaboration id="Collaboration_0bp2lp8">
    <bpmn:participant id="Participant_044zdc4" processRef="SRN-Update-Details" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Update-Details" isExecutable="true" camunda:versionTag="V1" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_0qmjmsz">
      <bpmn:lane id="Lane_1669tta" name="SHM">
        <bpmn:flowNodeRef>ExclusiveGateway_0vedjqy</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_NotifyMemberChangeDecline</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_memberUpdateDeclined</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_reviewSRNDetailsUpdate</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_AssignMemberUpdateToSHM</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0jolkl0</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1v4dwje" name="Member">
        <bpmn:flowNodeRef>StartEvent_memberUpdateInitiated</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_doChangesRequireApproval</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_UpdateMemberDetails</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_memberUpdateCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0auxooy</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1p6uacp" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_1ksp7mw</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1fcnsas</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0vedjqy" name="Is update approved?">
      <bpmn:incoming>SequenceFlow_1sdhdsr</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0lmpg92</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1h892ub</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_10qn237</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0y796el" sourceRef="StartEvent_memberUpdateInitiated" targetRef="ExclusiveGateway_doChangesRequireApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_0albqnb" name="Yes" sourceRef="ExclusiveGateway_doChangesRequireApproval" targetRef="ExclusiveGateway_0jolkl0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireApproval == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0f1hhok" name="No" sourceRef="ExclusiveGateway_doChangesRequireApproval" targetRef="Task_UpdateMemberDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireApproval == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1sdhdsr" sourceRef="Task_reviewSRNDetailsUpdate" targetRef="ExclusiveGateway_0vedjqy" />
    <bpmn:sequenceFlow id="SequenceFlow_1h892ub" name="Yes" sourceRef="ExclusiveGateway_0vedjqy" targetRef="Task_0auxooy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnChangesDecision == "accepted"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_10qn237" name="No" sourceRef="ExclusiveGateway_0vedjqy" targetRef="Task_NotifyMemberChangeDecline">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnChangesDecision == "rejected"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_09c5cb7" sourceRef="Task_UpdateMemberDetails" targetRef="EndEvent_memberUpdateCompleted" />
    <bpmn:sequenceFlow id="SequenceFlow_1jp6bi8" sourceRef="Task_NotifyMemberChangeDecline" targetRef="EndEvent_memberUpdateDeclined" />
    <bpmn:serviceTask id="Task_NotifyMemberChangeDecline" name="Notify member" camunda:type="external" camunda:topic="notify-member-of-srn-details-change-decline">
      <bpmn:incoming>SequenceFlow_10qn237</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1jp6bi8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_memberUpdateDeclined" name="Update declined">
      <bpmn:incoming>SequenceFlow_1jp6bi8</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:userTask id="Task_reviewSRNDetailsUpdate" name="SHM review and approves update" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="srnChangesDecision" label="Is the update accepted?" type="enum">
            <camunda:value id="accepted" name="Accepted" />
            <camunda:value id="rejected" name="Rejected" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0vk6xm3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1sdhdsr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="SequenceFlow_0vk6xm3" sourceRef="Task_AssignMemberUpdateToSHM" targetRef="Task_reviewSRNDetailsUpdate" />
    <bpmn:startEvent id="StartEvent_memberUpdateInitiated" name="Update initiated">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="SRNId" label="SRNId" type="long" />
          <camunda:formField id="requireApproval" label="Is approval required?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
          <camunda:formField id="toBeReviewBy" type="enum">
            <camunda:value id="shm" name="SHM" />
            <camunda:value id="sacrraadmin" name="SACRRA Admin" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0y796el</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Task_AssignMemberUpdateToSHM" name="Assign to SHM" camunda:type="external" camunda:topic="assign-srn-details-update-to-shm">
      <bpmn:incoming>SequenceFlow_1du158p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vk6xm3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_doChangesRequireApproval" name="Is approval required?">
      <bpmn:incoming>SequenceFlow_0y796el</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0albqnb</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0f1hhok</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_UpdateMemberDetails" name="Notify SHM" camunda:type="external" camunda:topic="notify-shm-of-srn-update">
      <bpmn:incoming>SequenceFlow_0f1hhok</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_09c5cb7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_memberUpdateCompleted" name="Update completed">
      <bpmn:incoming>SequenceFlow_09c5cb7</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0svjk3g</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0svjk3g" sourceRef="Task_0auxooy" targetRef="EndEvent_memberUpdateCompleted" />
    <bpmn:serviceTask id="Task_0auxooy" name="Apply changes &#38; notify member" camunda:type="external" camunda:topic="apply-srn-details-update">
      <bpmn:incoming>SequenceFlow_1h892ub</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0svjk3g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_1ksp7mw" name="Review SRN update" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="srnChangesDecision" type="enum">
            <camunda:value id="accepted" name="Accepted" />
            <camunda:value id="rejected" name="Rejected" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_00kwic2</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0lmpg92</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0jolkl0" name="To be reviewed by who?">
      <bpmn:incoming>SequenceFlow_0albqnb</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1du158p</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1wvtd4x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1du158p" name="SHM" sourceRef="ExclusiveGateway_0jolkl0" targetRef="Task_AssignMemberUpdateToSHM">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${toBeReviewBy == "shm"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1wvtd4x" name="Admin" sourceRef="ExclusiveGateway_0jolkl0" targetRef="Task_1fcnsas">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${toBeReviewBy == "sacrraadmin"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Task_1fcnsas" name="Assign SACRRA Admin" camunda:type="external" camunda:topic="assign-srn-update-review-to-sacrra-admin">
      <bpmn:incoming>SequenceFlow_1wvtd4x</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_00kwic2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_00kwic2" sourceRef="Task_1fcnsas" targetRef="Task_1ksp7mw" />
    <bpmn:sequenceFlow id="SequenceFlow_0lmpg92" sourceRef="Task_1ksp7mw" targetRef="ExclusiveGateway_0vedjqy" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0bp2lp8">
      <bpmndi:BPMNShape id="Participant_044zdc4_di" bpmnElement="Participant_044zdc4" isHorizontal="true">
        <dc:Bounds x="147" y="80" width="1015" height="540" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_memberUpdateInitiated">
        <dc:Bounds x="217" y="192" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="197" y="235" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0qsp07r_di" bpmnElement="ExclusiveGateway_doChangesRequireApproval" isMarkerVisible="true">
        <dc:Bounds x="448" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="445" y="148" width="55" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vedjqy_di" bpmnElement="ExclusiveGateway_0vedjqy" isMarkerVisible="true">
        <dc:Bounds x="755" y="342" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="756" y="402" width="52" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0y796el_di" bpmnElement="SequenceFlow_0y796el">
        <di:waypoint x="253" y="210" />
        <di:waypoint x="448" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0albqnb_di" bpmnElement="SequenceFlow_0albqnb">
        <di:waypoint x="473" y="235" />
        <di:waypoint x="473" y="293" />
        <di:waypoint x="324" y="293" />
        <di:waypoint x="324" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="445" y="241" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0f1hhok_di" bpmnElement="SequenceFlow_0f1hhok">
        <di:waypoint x="498" y="210" />
        <di:waypoint x="599" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="523" y="192" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1sdhdsr_di" bpmnElement="SequenceFlow_1sdhdsr">
        <di:waypoint x="663" y="367" />
        <di:waypoint x="755" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1h892ub_di" bpmnElement="SequenceFlow_1h892ub">
        <di:waypoint x="780" y="342" />
        <di:waypoint x="780" y="210" />
        <di:waypoint x="839" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="800" y="192" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_10qn237_di" bpmnElement="SequenceFlow_10qn237">
        <di:waypoint x="805" y="367" />
        <di:waypoint x="881" y="367" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="836" y="349" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09c5cb7_di" bpmnElement="SequenceFlow_09c5cb7">
        <di:waypoint x="649" y="170" />
        <di:waypoint x="649" y="111" />
        <di:waypoint x="783" y="111" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_1669tta_di" bpmnElement="Lane_1669tta" isHorizontal="true">
        <dc:Bounds x="177" y="270" width="985" height="180" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1v4dwje_di" bpmnElement="Lane_1v4dwje" isHorizontal="true">
        <dc:Bounds x="177" y="80" width="985" height="190" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0lnzwfh_di" bpmnElement="Task_UpdateMemberDetails">
        <dc:Bounds x="599" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_14qa7uc_di" bpmnElement="Task_reviewSRNDetailsUpdate">
        <dc:Bounds x="563" y="327" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1jp6bi8_di" bpmnElement="SequenceFlow_1jp6bi8">
        <di:waypoint x="981" y="367" />
        <di:waypoint x="1043" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0yr44kl_di" bpmnElement="Task_NotifyMemberChangeDecline">
        <dc:Bounds x="881" y="327" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1jeehxd_di" bpmnElement="EndEvent_memberUpdateCompleted">
        <dc:Bounds x="783" y="93" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="756" y="136" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_148c6n3_di" bpmnElement="EndEvent_memberUpdateDeclined">
        <dc:Bounds x="1043" y="349" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1022" y="392" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_04g635k_di" bpmnElement="Task_AssignMemberUpdateToSHM">
        <dc:Bounds x="415" y="327" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0vk6xm3_di" bpmnElement="SequenceFlow_0vk6xm3">
        <di:waypoint x="515" y="367" />
        <di:waypoint x="563" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0svjk3g_di" bpmnElement="SequenceFlow_0svjk3g">
        <di:waypoint x="889" y="170" />
        <di:waypoint x="889" y="111" />
        <di:waypoint x="819" y="111" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_11czjk1_di" bpmnElement="Task_0auxooy">
        <dc:Bounds x="839" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1p6uacp_di" bpmnElement="Lane_1p6uacp" isHorizontal="true">
        <dc:Bounds x="177" y="450" width="985" height="170" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_07q1pk1_di" bpmnElement="Task_1ksp7mw">
        <dc:Bounds x="563" y="498" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0jolkl0_di" bpmnElement="ExclusiveGateway_0jolkl0" isMarkerVisible="true">
        <dc:Bounds x="299" y="342" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="288" y="299" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1du158p_di" bpmnElement="SequenceFlow_1du158p">
        <di:waypoint x="349" y="367" />
        <di:waypoint x="415" y="367" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="370" y="349" width="25" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wvtd4x_di" bpmnElement="SequenceFlow_1wvtd4x">
        <di:waypoint x="324" y="392" />
        <di:waypoint x="324" y="538" />
        <di:waypoint x="415" y="538" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="335" y="463" width="31" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0bvn8tu_di" bpmnElement="Task_1fcnsas">
        <dc:Bounds x="415" y="498" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_00kwic2_di" bpmnElement="SequenceFlow_00kwic2">
        <di:waypoint x="515" y="538" />
        <di:waypoint x="563" y="538" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0lmpg92_di" bpmnElement="SequenceFlow_0lmpg92">
        <di:waypoint x="663" y="538" />
        <di:waypoint x="780" y="538" />
        <di:waypoint x="780" y="392" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
