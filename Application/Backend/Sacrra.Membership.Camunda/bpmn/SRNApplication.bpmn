<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" id="Definitions_053lmvj" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_008vpn1">
    <bpmn:participant id="Participant_1kh7vyo" name="SRN Application" processRef="New-SRN-Application" />
  </bpmn:collaboration>
  <bpmn:process id="New-SRN-Application" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1bge26e">
      <bpmn:lane id="Lane_035400c" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_1yp2wlm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailBureausAfterTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_CreateSRNNumber</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_UpdateSRNStatus</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_allocateStakeHolderManager</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_1ybbrkz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_NotifyApplicantOfSRNApplicationRejection</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailMember</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailBureaus</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1xgcprg</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1gm5tpr</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1p0mnwn</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0587rdi" name="SHM / Member">
        <bpmn:flowNodeRef>UserTask_ConfirmMigrationTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1rfunjz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateCatchEvent_1mv5pv6</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_IsSRNLive</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateCatchEvent_1jy3izs</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0e1bn4z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1f16z8z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_14q9hgp</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_SHMReview</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1uwvrpk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0jk0yud" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_AddTestSRNToDTH</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_CompleteAndUpdateDTH</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="SequenceFlow_0h2lihi" sourceRef="StartEvent_applicationFormCompleted" targetRef="Task_allocateStakeHolderManager" />
    <bpmn:sequenceFlow id="SequenceFlow_1hhhnnm" sourceRef="Task_SHMReview" targetRef="ExclusiveGateway_14q9hgp" />
    <bpmn:sequenceFlow id="SequenceFlow_0v5q0w0" sourceRef="Task_allocateStakeHolderManager" targetRef="Task_SHMReview" />
    <bpmn:sequenceFlow id="SequenceFlow_1tdlkv4" name="Yes" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_CreateSRNNumber">
      <bpmn:extensionElements>
        <camunda:executionListener expression="" event="take" />
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnVerified1 == "yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0uhq8le" sourceRef="Task_CreateSRNNumber" targetRef="Task_UpdateSRNStatus" />
    <bpmn:sequenceFlow id="SequenceFlow_19mqij6" name="Reject SRN application" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_NotifyApplicantOfSRNApplicationRejection">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${srnVerified1 == "no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0zirsqc" sourceRef="Task_CompleteAndUpdateDTH" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1x9zyto" sourceRef="Task_UpdateSRNStatus" targetRef="ExclusiveGateway_1xgcprg" />
    <bpmn:sequenceFlow id="SequenceFlow_0awyrts" sourceRef="Task_EmailBureaus" targetRef="Task_EmailMember" />
    <bpmn:sequenceFlow id="SequenceFlow_1wlnm0p" name="Yes" sourceRef="ExclusiveGateway_1rfunjz" targetRef="Task_EmailBureausAfterTesting">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1p6gq55" sourceRef="Task_EmailMember" targetRef="ExclusiveGateway_1gm5tpr" />
    <bpmn:sequenceFlow id="SequenceFlow_0dovdso" name="Cancel" sourceRef="ExclusiveGateway_1rfunjz" targetRef="EndEvent_complete">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_141cd5l" sourceRef="Task_EmailBureausAfterTesting" targetRef="IntermediateThrowEvent_1uwvrpk" />
    <bpmn:serviceTask id="Task_1yp2wlm" name="Email bureaus on Go Live" camunda:type="external" camunda:topic="email-bureaus-on-srn-go-live">
      <bpmn:incoming>SequenceFlow_0cjhodd</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0jretbt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xw4ch6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0xw4ch6" sourceRef="Task_1yp2wlm" targetRef="Task_CompleteAndUpdateDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_0cjhodd" name="Yes" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="Task_1yp2wlm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0q8yna1" name="Cancel" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="EndEvent_complete">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="UserTask_ConfirmMigrationTesting" name="Is testing/ migration confirmed?" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsTestingMigrationConfirmed" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_156bras</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1ob28oy</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0b9pc4r</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1rfunjz" name="Is confirmed?">
      <bpmn:incoming>SequenceFlow_0b9pc4r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0decq40</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0dovdso</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1wlnm0p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_1mv5pv6" name="User defined date">
      <bpmn:incoming>SequenceFlow_0decq40</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_156bras</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_15zaxxn">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${testEndDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="SequenceFlow_156bras" sourceRef="IntermediateCatchEvent_1mv5pv6" targetRef="UserTask_ConfirmMigrationTesting" />
    <bpmn:sequenceFlow id="SequenceFlow_0b9pc4r" sourceRef="UserTask_ConfirmMigrationTesting" targetRef="ExclusiveGateway_1rfunjz" />
    <bpmn:sequenceFlow id="SequenceFlow_0decq40" name="No" sourceRef="ExclusiveGateway_1rfunjz" targetRef="IntermediateCatchEvent_1mv5pv6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="EndEvent_complete" name="SRN Application Complete">
      <bpmn:incoming>SequenceFlow_0zirsqc</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0dovdso</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0q8yna1</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:userTask id="Task_IsSRNLive" name="Is SRN Live?" camunda:assignee="${stakeHolderManagerAssignee}">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsSRNLive" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07o10sh</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_154ifyz</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17c8lit</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_1jy3izs" name="User defined date">
      <bpmn:incoming>SequenceFlow_17u1fi4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07o10sh</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_05iai0i">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${goLiveDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0e1bn4z" name="Is live?">
      <bpmn:incoming>SequenceFlow_17c8lit</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17u1fi4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0cjhodd</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0q8yna1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_07o10sh" sourceRef="IntermediateCatchEvent_1jy3izs" targetRef="Task_IsSRNLive" />
    <bpmn:sequenceFlow id="SequenceFlow_17c8lit" sourceRef="Task_IsSRNLive" targetRef="ExclusiveGateway_0e1bn4z" />
    <bpmn:sequenceFlow id="SequenceFlow_17u1fi4" name="No" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="IntermediateCatchEvent_1jy3izs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Task_EmailBureausAfterTesting" name="Email bureaus after testing" camunda:type="external" camunda:topic="email-bureaus-after-srn-testing">
      <bpmn:incoming>SequenceFlow_1wlnm0p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_141cd5l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1g1n034" sourceRef="ExclusiveGateway_1gm5tpr" targetRef="IntermediateThrowEvent_1f16z8z" />
    <bpmn:sequenceFlow id="SequenceFlow_1wuqnj9" sourceRef="ExclusiveGateway_1gm5tpr" targetRef="Task_AddTestSRNToDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_1ob28oy" sourceRef="IntermediateThrowEvent_1f16z8z" targetRef="UserTask_ConfirmMigrationTesting" />
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1f16z8z">
      <bpmn:incoming>SequenceFlow_1g1n034</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1ob28oy</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${testEndDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="SequenceFlow_154ifyz" sourceRef="IntermediateThrowEvent_1uwvrpk" targetRef="Task_IsSRNLive" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_14q9hgp" name="Is Verified">
      <bpmn:incoming>SequenceFlow_1hhhnnm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1tdlkv4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_19mqij6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_SHMReview" name="SHM Reviews SRN(s) Application" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="srnVerified1" label="Is the SRN verified?" type="enum" defaultValue="yes">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0v5q0w0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1hhhnnm</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_CreateSRNNumber" name="Create SRN Number" camunda:type="external" camunda:topic="create-srn-number">
      <bpmn:incoming>SequenceFlow_1tdlkv4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0uhq8le</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1uwvrpk">
      <bpmn:incoming>SequenceFlow_141cd5l</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_154ifyz</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${goLiveDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Task_UpdateSRNStatus" name="Update SRN Status" camunda:type="external" camunda:topic="update-srn-status">
      <bpmn:incoming>SequenceFlow_0uhq8le</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1x9zyto</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application form completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="MemberId" label="Member ID" type="long" />
          <camunda:formField id="SRNId" label="SRN ID" type="long" />
          <camunda:formField id="fileType" label="SRN File Type" type="string" />
          <camunda:formField id="requireTesting" label="SRN Testing Required?" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0h2lihi</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Task_allocateStakeHolderManager" name="Allocate a SRN Application task to a SHM" camunda:type="external" camunda:topic="allocate-stake-holder-manager">
      <bpmn:incoming>SequenceFlow_0h2lihi</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0v5q0w0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="StartEvent_1ybbrkz">
      <bpmn:incoming>SequenceFlow_1rp3yrd</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1rp3yrd" sourceRef="Task_NotifyApplicantOfSRNApplicationRejection" targetRef="StartEvent_1ybbrkz" />
    <bpmn:serviceTask id="Task_NotifyApplicantOfSRNApplicationRejection" name="Notify applicant of SRN application rejection" camunda:type="external" camunda:topic="notify-applicant-of-srn-application-rejection">
      <bpmn:incoming>SequenceFlow_19mqij6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1rp3yrd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EmailMember" name="Email Member" camunda:type="external" camunda:topic="email-member">
      <bpmn:incoming>SequenceFlow_0awyrts</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1p6gq55</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EmailBureaus" name="Email Bureaus before testing&#10;" camunda:type="external" camunda:topic="email-bureaus-before-srn-testing">
      <bpmn:incoming>SequenceFlow_03pv4cu</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0awyrts</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1xgcprg" name="Testing Required?">
      <bpmn:incoming>SequenceFlow_1x9zyto</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_03pv4cu</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0sol88w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_03pv4cu" name="Yes" sourceRef="ExclusiveGateway_1xgcprg" targetRef="Task_EmailBureaus">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireTesting=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Task_AddTestSRNToDTH" name="Create Test SRN on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_1wuqnj9</bpmn:incoming>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CompleteAndUpdateDTH" name="Make it live on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_0xw4ch6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0zirsqc</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:parallelGateway id="ExclusiveGateway_1gm5tpr">
      <bpmn:incoming>SequenceFlow_1p6gq55</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1g1n034</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1wuqnj9</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0sol88w" name="No" sourceRef="ExclusiveGateway_1xgcprg" targetRef="Task_1p0mnwn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requireTesting=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0jretbt" sourceRef="Task_1p0mnwn" targetRef="Task_1yp2wlm" />
    <bpmn:serviceTask id="Task_1p0mnwn" name="Update SRN Status to Live" camunda:type="external" camunda:topic="update-srn-status-to-live">
      <bpmn:incoming>SequenceFlow_0sol88w</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0jretbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:textAnnotation id="TextAnnotation_0xhk4ak">
      <bpmn:text>To "Test" or "Live" status</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1fh5u05">
      <bpmn:text>Input "Go Live Date"</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_1c6gkb2">
      <bpmn:text>Input "Go Live Date"</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_008vpn1">
      <bpmndi:BPMNShape id="Participant_1kh7vyo_di" bpmnElement="Participant_1kh7vyo" isHorizontal="true">
        <dc:Bounds x="140" y="82" width="1350" height="1188" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0587rdi_di" bpmnElement="Lane_0587rdi" isHorizontal="true">
        <dc:Bounds x="170" y="82" width="1320" height="548" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0ntmvkx_di" bpmnElement="StartEvent_applicationFormCompleted">
        <dc:Bounds x="442" y="462" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="421" y="424.5" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0h2lihi_di" bpmnElement="SequenceFlow_0h2lihi">
        <di:waypoint x="460" y="498" />
        <di:waypoint x="460" y="690" />
        <di:waypoint x="480" y="690" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_12x7cdl_di" bpmnElement="Task_SHMReview">
        <dc:Bounds x="760" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_14q9hgp_di" bpmnElement="ExclusiveGateway_14q9hgp" isMarkerVisible="true">
        <dc:Bounds x="995" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1051" y="162" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1hhhnnm_di" bpmnElement="SequenceFlow_1hhhnnm">
        <di:waypoint x="860" y="177" />
        <di:waypoint x="995" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0we9zcl_di" bpmnElement="Task_CreateSRNNumber">
        <dc:Bounds x="250" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_035400c_di" bpmnElement="Lane_035400c" isHorizontal="true">
        <dc:Bounds x="170" y="630" width="1320" height="460" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0yorbl1_di" bpmnElement="Task_UpdateSRNStatus">
        <dc:Bounds x="250" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0v5q0w0_di" bpmnElement="SequenceFlow_0v5q0w0">
        <di:waypoint x="580" y="690" />
        <di:waypoint x="600" y="690" />
        <di:waypoint x="600" y="177" />
        <di:waypoint x="760" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1tdlkv4_di" bpmnElement="SequenceFlow_1tdlkv4">
        <di:waypoint x="1020" y="152" />
        <di:waypoint x="1020" y="110" />
        <di:waypoint x="300" y="110" />
        <di:waypoint x="300" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="711" y="115" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0uhq8le_di" bpmnElement="SequenceFlow_0uhq8le">
        <di:waypoint x="300" y="730" />
        <di:waypoint x="300" y="780" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_06h39wz_di" bpmnElement="EndEvent_complete">
        <dc:Bounds x="1363" y="180" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1341" y="149" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0ra0ipd_di" bpmnElement="Task_allocateStakeHolderManager">
        <dc:Bounds x="480" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_19mqij6_di" bpmnElement="SequenceFlow_19mqij6">
        <di:waypoint x="1020" y="202" />
        <di:waypoint x="1020" y="270" />
        <di:waypoint x="750" y="270" />
        <di:waypoint x="750" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="721" y="419" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0zirsqc_di" bpmnElement="SequenceFlow_0zirsqc">
        <di:waypoint x="1280" y="1170" />
        <di:waypoint x="1430" y="1170" />
        <di:waypoint x="1430" y="198" />
        <di:waypoint x="1399" y="198" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_09e25ax_di" bpmnElement="Task_CompleteAndUpdateDTH">
        <dc:Bounds x="1180" y="1140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1x9zyto_di" bpmnElement="SequenceFlow_1x9zyto">
        <di:waypoint x="350" y="820" />
        <di:waypoint x="415" y="820" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1u6jkua_di" bpmnElement="Task_EmailBureaus">
        <dc:Bounds x="560" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_10qh3n4_di" bpmnElement="Task_NotifyApplicantOfSRNApplicationRejection">
        <dc:Bounds x="670" y="650" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0awyrts_di" bpmnElement="SequenceFlow_0awyrts">
        <di:waypoint x="660" y="820" />
        <di:waypoint x="720" y="820" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_0jk0yud_di" bpmnElement="Lane_0jk0yud" isHorizontal="true">
        <dc:Bounds x="170" y="1090" width="1320" height="180" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1wlnm0p_di" bpmnElement="SequenceFlow_1wlnm0p">
        <di:waypoint x="1043" y="474" />
        <di:waypoint x="1043" y="790" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1022" y="553" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1d7vd4l_di" bpmnElement="Task_EmailMember">
        <dc:Bounds x="720" y="780" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_09nap4b_di" bpmnElement="UserTask_ConfirmMigrationTesting">
        <dc:Bounds x="851" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1mv5pv6_di" bpmnElement="IntermediateCatchEvent_1mv5pv6" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="883" y="341" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="858" y="317" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1rfunjz_di" bpmnElement="ExclusiveGateway_1rfunjz" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1018" y="424" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1065.5" y="423" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_156bras_di" bpmnElement="SequenceFlow_156bras">
        <di:waypoint x="901" y="377" />
        <di:waypoint x="901" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0b9pc4r_di" bpmnElement="SequenceFlow_0b9pc4r">
        <di:waypoint x="951" y="449" />
        <di:waypoint x="1018" y="449" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0decq40_di" bpmnElement="SequenceFlow_0decq40" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1043" y="424" />
        <di:waypoint x="1043" y="359" />
        <di:waypoint x="919" y="359" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1023" y="374" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1p6gq55_di" bpmnElement="SequenceFlow_1p6gq55">
        <di:waypoint x="770" y="860" />
        <di:waypoint x="770" y="905" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1mbfwun_di" bpmnElement="Task_EmailBureausAfterTesting">
        <dc:Bounds x="993" y="790" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0dovdso_di" bpmnElement="SequenceFlow_0dovdso">
        <di:waypoint x="1068" y="449" />
        <di:waypoint x="1100" y="449" />
        <di:waypoint x="1100" y="200" />
        <di:waypoint x="1363" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1116" y="203" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_141cd5l_di" bpmnElement="SequenceFlow_141cd5l">
        <di:waypoint x="1093" y="830" />
        <di:waypoint x="1140" y="830" />
        <di:waypoint x="1140" y="598" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0iycr5t_di" bpmnElement="Task_IsSRNLive">
        <dc:Bounds x="1170" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0e1bn4z_di" bpmnElement="ExclusiveGateway_0e1bn4z" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1315" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1371.5" y="473" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_17c8lit_di" bpmnElement="SequenceFlow_17c8lit">
        <di:waypoint x="1270" y="470" />
        <di:waypoint x="1315" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1jy3izs_di" bpmnElement="IntermediateCatchEvent_1jy3izs" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1202" y="341" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1177" y="317" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_17u1fi4_di" bpmnElement="SequenceFlow_17u1fi4">
        <di:waypoint x="1340" y="445" />
        <di:waypoint x="1340" y="359" />
        <di:waypoint x="1238" y="359" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1322" y="393" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_07o10sh_di" bpmnElement="SequenceFlow_07o10sh">
        <di:waypoint x="1220" y="377" />
        <di:waypoint x="1220" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1nam24e_di" bpmnElement="Task_1yp2wlm">
        <dc:Bounds x="1180" y="790" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0xw4ch6_di" bpmnElement="SequenceFlow_0xw4ch6">
        <di:waypoint x="1230" y="870" />
        <di:waypoint x="1230" y="1140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cjhodd_di" bpmnElement="SequenceFlow_0cjhodd">
        <di:waypoint x="1340" y="495" />
        <di:waypoint x="1340" y="830" />
        <di:waypoint x="1280" y="830" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1311" y="603" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1fh5u05_di" bpmnElement="TextAnnotation_1fh5u05">
        <dc:Bounds x="1156" y="510" width="127" height="26" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0q8yna1_di" bpmnElement="SequenceFlow_0q8yna1">
        <di:waypoint x="1365" y="470" />
        <di:waypoint x="1381" y="470" />
        <di:waypoint x="1381" y="216" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1382" y="413" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1c6gkb2_di" bpmnElement="TextAnnotation_1c6gkb2">
        <dc:Bounds x="837" y="510" width="127" height="26" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0xhk4ak_di" bpmnElement="TextAnnotation_0xhk4ak">
        <dc:Bounds x="240" y="880" width="123" height="39" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1fft1wn_di" bpmnElement="Task_AddTestSRNToDTH">
        <dc:Bounds x="720" y="1140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1g1n034_di" bpmnElement="SequenceFlow_1g1n034">
        <di:waypoint x="795" y="930" />
        <di:waypoint x="980" y="930" />
        <di:waypoint x="980" y="568" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wuqnj9_di" bpmnElement="SequenceFlow_1wuqnj9">
        <di:waypoint x="770" y="955" />
        <di:waypoint x="770" y="1140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ParallelGateway_0wuftez_di" bpmnElement="ExclusiveGateway_1gm5tpr">
        <dc:Bounds x="745" y="905" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1ob28oy_di" bpmnElement="SequenceFlow_1ob28oy">
        <di:waypoint x="980" y="532" />
        <di:waypoint x="980" y="470" />
        <di:waypoint x="951" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1g9qriu_di" bpmnElement="IntermediateThrowEvent_1f16z8z">
        <dc:Bounds x="962" y="532" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="540" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_154ifyz_di" bpmnElement="SequenceFlow_154ifyz">
        <di:waypoint x="1140" y="562" />
        <di:waypoint x="1140" y="470" />
        <di:waypoint x="1170" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0nuvdve_di" bpmnElement="IntermediateThrowEvent_1uwvrpk">
        <dc:Bounds x="1122" y="562" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1sn0jnj_di" bpmnElement="StartEvent_1ybbrkz">
        <dc:Bounds x="862" y="672" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1rp3yrd_di" bpmnElement="SequenceFlow_1rp3yrd">
        <di:waypoint x="770" y="690" />
        <di:waypoint x="862" y="690" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_1xgcprg_di" bpmnElement="ExclusiveGateway_1xgcprg" isMarkerVisible="true">
        <dc:Bounds x="415" y="795" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="397" y="773" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_03pv4cu_di" bpmnElement="SequenceFlow_03pv4cu">
        <di:waypoint x="465" y="820" />
        <di:waypoint x="560" y="820" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="504" y="802" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sol88w_di" bpmnElement="SequenceFlow_0sol88w">
        <di:waypoint x="440" y="845" />
        <di:waypoint x="440" y="1020" />
        <di:waypoint x="500" y="1020" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="453" y="893" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0jretbt_di" bpmnElement="SequenceFlow_0jretbt">
        <di:waypoint x="600" y="1020" />
        <di:waypoint x="1160" y="1020" />
        <di:waypoint x="1160" y="830" />
        <di:waypoint x="1180" y="830" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1h7dhkl_di" bpmnElement="Task_1p0mnwn">
        <dc:Bounds x="500" y="980" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
