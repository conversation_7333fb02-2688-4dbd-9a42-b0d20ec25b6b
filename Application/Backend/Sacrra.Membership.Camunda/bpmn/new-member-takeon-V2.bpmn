<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="Definitions_0ljajin" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.1.2">
  <bpmn:collaboration id="Collaboration_0gjfm6r">
    <bpmn:participant id="Participant_0pry0ja" name="Member Application" processRef="New-Member-Takeon" />
  </bpmn:collaboration>
  <bpmn:process id="New-Member-Takeon" isExecutable="true" camunda:versionTag="V2.0" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_0lsym2h">
      <bpmn:lane id="Lane_applicant" name="Applicantmber Take">
        <bpmn:flowNodeRef>StartEvent_applicationFormCompleted</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0g1ewho</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_checkIfChangesRequireApproval</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0jvhffm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1cu4bsp</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_stakeHolderManager" name="Stake Holder Manager">
        <bpmn:flowNodeRef>ExclusiveGateway_qualify</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_complete</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_disqualifyMember</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_ReviewMemberApplication</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_0qrrie9</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_reviewMemberChanges</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_02wd636</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_shmFinalReview</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_00h5nay" name="Group SHM">
        <bpmn:flowNodeRef>Task_allocateStakeHolderManagerManager</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_financialAdministrator" name="Financial Administrator">
        <bpmn:flowNodeRef>SubProcess_NonMemberProcess</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>SubProcess_fullMemberSubProcess</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="SequenceFlow_0k4330h" name="Yes" sourceRef="ExclusiveGateway_0g1ewho" targetRef="Task_checkIfChangesRequireApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_1w7teci" sourceRef="StartEvent_applicationFormCompleted" targetRef="ExclusiveGateway_0g1ewho" />
    <bpmn:sequenceFlow id="SequenceFlow_1wdg5he" name="No" sourceRef="ExclusiveGateway_0g1ewho" targetRef="Task_allocateStakeHolderManagerManager" />
    <bpmn:sequenceFlow id="SequenceFlow_1jj5efx" sourceRef="Task_reviewMemberChanges" targetRef="EndEvent_02wd636" />
    <bpmn:sequenceFlow id="SequenceFlow_07az7ab" sourceRef="Task_allocateStakeHolderManagerManager" targetRef="Task_ReviewMemberApplication" />
    <bpmn:sequenceFlow id="SequenceFlow_0xmntch" sourceRef="Task_ReviewMemberApplication" targetRef="ExclusiveGateway_qualify" />
    <bpmn:sequenceFlow id="SequenceFlow_1fghq34" sourceRef="SubProcess_fullMemberSubProcess" targetRef="Task_shmFinalReview" />
    <bpmn:sequenceFlow id="SequenceFlow_0w3obry" sourceRef="SubProcess_NonMemberProcess" targetRef="Task_shmFinalReview" />
    <bpmn:sequenceFlow id="SequenceFlow_1lkosa1" sourceRef="Task_shmFinalReview" targetRef="Task_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_0fuauda" sourceRef="Task_complete" targetRef="EndEvent_complete" />
    <bpmn:sequenceFlow id="SequenceFlow_1shf565" name="Non" sourceRef="ExclusiveGateway_qualify" targetRef="SubProcess_NonMemberProcess" />
    <bpmn:sequenceFlow id="SequenceFlow_02smnwx" name="Full" sourceRef="ExclusiveGateway_qualify" targetRef="SubProcess_fullMemberSubProcess" />
    <bpmn:sequenceFlow id="SequenceFlow_0sdz2lw" name="Disqualified" sourceRef="ExclusiveGateway_qualify" targetRef="Task_disqualifyMember" />
    <bpmn:sequenceFlow id="SequenceFlow_1civem7" sourceRef="Task_disqualifyMember" targetRef="EndEvent_0qrrie9" />
    <bpmn:serviceTask id="Task_checkIfChangesRequireApproval" name="Check if changes require approval" camunda:type="external" camunda:topic="check-if-member-changes-require-approval">
      <bpmn:incoming>SequenceFlow_0k4330h</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1m7rwdt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0g1ewho" name="Exists on Database">
      <bpmn:incoming>SequenceFlow_1w7teci</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wdg5he</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0k4330h</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:startEvent id="StartEvent_applicationFormCompleted" name="Application form completed">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="OrganisationID" label="Organisation ID" type="long" />
          <camunda:formField id="OrganisationName" label="Organisation Name" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1w7teci</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_qualify" name="Review Decision">
      <bpmn:incoming>SequenceFlow_0xmntch</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1shf565</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_02smnwx</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0sdz2lw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:manualTask id="Task_complete" name="Complete final take on">
      <bpmn:incoming>SequenceFlow_1lkosa1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0fuauda</bpmn:outgoing>
    </bpmn:manualTask>
    <bpmn:endEvent id="EndEvent_complete" name="Application complete">
      <bpmn:incoming>SequenceFlow_0fuauda</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_disqualifyMember" name="Disqualify applicant and email" camunda:type="external" camunda:topic="disqualify-member-and-email">
      <bpmn:incoming>SequenceFlow_0sdz2lw</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1civem7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_ReviewMemberApplication" name="Review and facilitate a Member Application&#10;" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="reviewApplicationDecision" label="Does the applicant qualify?" type="enum" defaultValue="yes">
            <camunda:value id="disqualified" name="Disqualified" />
            <camunda:value id="nonMember" name="Non-Member" />
            <camunda:value id="fullMember" name="Full Member" />
          </camunda:formField>
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_07az7ab</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xmntch</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_allocateStakeHolderManagerManager" name="Group Stake Holder Manager Allocates SHM" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_1wdg5he</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_07az7ab</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0jvhffm" name="Do changes require approval?">
      <bpmn:incoming>SequenceFlow_1m7rwdt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1kj1x78</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_15e2z1p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1m7rwdt" sourceRef="Task_checkIfChangesRequireApproval" targetRef="ExclusiveGateway_0jvhffm" />
    <bpmn:sequenceFlow id="SequenceFlow_1kj1x78" name="Yes" sourceRef="ExclusiveGateway_0jvhffm" targetRef="Task_reviewMemberChanges" />
    <bpmn:sequenceFlow id="SequenceFlow_15e2z1p" name="No" sourceRef="ExclusiveGateway_0jvhffm" targetRef="EndEvent_1cu4bsp" />
    <bpmn:endEvent id="EndEvent_1cu4bsp">
      <bpmn:incoming>SequenceFlow_15e2z1p</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:endEvent id="EndEvent_0qrrie9">
      <bpmn:incoming>SequenceFlow_1civem7</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:userTask id="Task_reviewMemberChanges" name="Review and Accepts or Decline Changes">
      <bpmn:incoming>SequenceFlow_1kj1x78</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1jj5efx</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_02wd636">
      <bpmn:incoming>SequenceFlow_1jj5efx</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:userTask id="Task_shmFinalReview" name="SHM final Review" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_1fghq34</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0w3obry</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1lkosa1</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:subProcess id="SubProcess_NonMemberProcess" name="Non Member Finance Processs">
      <bpmn:incoming>SequenceFlow_1shf565</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0w3obry</bpmn:outgoing>
      <bpmn:startEvent id="StartEvent_0mdz1wq">
        <bpmn:outgoing>SequenceFlow_0wl0agp</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_1bgdnh2">
        <bpmn:incoming>SequenceFlow_0aojake</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_1k8f9vr</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_01w9t59</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_083b5kq" />
      </bpmn:intermediateCatchEvent>
      <bpmn:userTask id="UserTask_1e74ukf" name="Check If Invoice Paid If Cycles Exceeded Cancel Process" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_01w9t59</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_01kh8m5</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:exclusiveGateway id="ExclusiveGateway_1bmvd6t" name="Is payment received">
        <bpmn:incoming>SequenceFlow_01kh8m5</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1k8f9vr</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_00g5obi</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_12bo4xn</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:userTask id="UserTask_14f3dqh" name="Create Assessment Invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_0wl0agp</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0aojake</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:userTask id="UserTask_0gomw05" name="Create and email Onboarding Invoice" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_00g5obi</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_1fkbr8a</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1urh21r</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:intermediateCatchEvent id="IntermediateCatchEvent_0zk0oqd">
        <bpmn:incoming>SequenceFlow_1urh21r</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_09uoa55</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0lg8uoe" />
      </bpmn:intermediateCatchEvent>
      <bpmn:endEvent id="EndEvent_1tviaau">
        <bpmn:incoming>SequenceFlow_0iiiauc</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:exclusiveGateway id="ExclusiveGateway_02z37ea" name="Is onboardin fee paid?">
        <bpmn:incoming>SequenceFlow_1u0awu9</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1fkbr8a</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0iiiauc</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0vk45il</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:endEvent id="EndEvent_0ehhc54" name="Aoplication Terminated">
        <bpmn:incoming>SequenceFlow_0t4slqd</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_1ga6kbq" />
      </bpmn:endEvent>
      <bpmn:serviceTask id="ServiceTask_1oej7e3" name="Cancel &#38; Email Applicant">
        <bpmn:incoming>SequenceFlow_12bo4xn</bpmn:incoming>
        <bpmn:incoming>SequenceFlow_0vk45il</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0t4slqd</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_0wl0agp" sourceRef="StartEvent_0mdz1wq" targetRef="UserTask_14f3dqh" />
      <bpmn:sequenceFlow id="SequenceFlow_0aojake" sourceRef="UserTask_14f3dqh" targetRef="IntermediateCatchEvent_1bgdnh2" />
      <bpmn:sequenceFlow id="SequenceFlow_01kh8m5" name="Paid" sourceRef="UserTask_1e74ukf" targetRef="ExclusiveGateway_1bmvd6t" />
      <bpmn:sequenceFlow id="SequenceFlow_01w9t59" sourceRef="IntermediateCatchEvent_1bgdnh2" targetRef="UserTask_1e74ukf" />
      <bpmn:sequenceFlow id="SequenceFlow_1k8f9vr" sourceRef="ExclusiveGateway_1bmvd6t" targetRef="IntermediateCatchEvent_1bgdnh2" />
      <bpmn:sequenceFlow id="SequenceFlow_00g5obi" name="Yes" sourceRef="ExclusiveGateway_1bmvd6t" targetRef="UserTask_0gomw05" />
      <bpmn:sequenceFlow id="SequenceFlow_1urh21r" sourceRef="UserTask_0gomw05" targetRef="IntermediateCatchEvent_0zk0oqd" />
      <bpmn:sequenceFlow id="SequenceFlow_09uoa55" sourceRef="IntermediateCatchEvent_0zk0oqd" targetRef="Task_0tatwfs" />
      <bpmn:sequenceFlow id="SequenceFlow_1u0awu9" sourceRef="Task_0tatwfs" targetRef="ExclusiveGateway_02z37ea" />
      <bpmn:sequenceFlow id="SequenceFlow_1fkbr8a" name="No" sourceRef="ExclusiveGateway_02z37ea" targetRef="UserTask_0gomw05" />
      <bpmn:sequenceFlow id="SequenceFlow_0iiiauc" name="Yes" sourceRef="ExclusiveGateway_02z37ea" targetRef="EndEvent_1tviaau" />
      <bpmn:sequenceFlow id="SequenceFlow_12bo4xn" name="No" sourceRef="ExclusiveGateway_1bmvd6t" targetRef="ServiceTask_1oej7e3" />
      <bpmn:sequenceFlow id="SequenceFlow_0t4slqd" sourceRef="ServiceTask_1oej7e3" targetRef="EndEvent_0ehhc54" />
      <bpmn:sequenceFlow id="SequenceFlow_0vk45il" name="No" sourceRef="ExclusiveGateway_02z37ea" targetRef="ServiceTask_1oej7e3" />
      <bpmn:userTask id="Task_0tatwfs" name="Check if Onboarding Invoice has been Paid" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_09uoa55</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1u0awu9</bpmn:outgoing>
      </bpmn:userTask>
    </bpmn:subProcess>
    <bpmn:subProcess id="SubProcess_fullMemberSubProcess" name="Full Member">
      <bpmn:incoming>SequenceFlow_02smnwx</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1fghq34</bpmn:outgoing>
      <bpmn:endEvent id="EndEvent_cancel">
        <bpmn:incoming>SequenceFlow_18f55zp</bpmn:incoming>
        <bpmn:terminateEventDefinition />
      </bpmn:endEvent>
      <bpmn:serviceTask id="Task_cancel" name="Cancel application and email" camunda:type="external" camunda:topic="member-cancel">
        <bpmn:incoming>SequenceFlow_0q8npmq</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_18f55zp</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Task_approve" name="Appove and email member" camunda:type="external" camunda:topic="member-approve">
        <bpmn:incoming>SequenceFlow_0sh548s</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_020ch0h</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="ExclusiveGateway_wasPaymentReceived" name="Payment received?">
        <bpmn:incoming>SequenceFlow_04pqhgj</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0sh548s</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0q8npmq</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Task_18d4zpq" name="Payment and Constitution Reminder" camunda:type="external" camunda:topic="send-payment-and-constitution-reminder">
        <bpmn:incoming>SequenceFlow_0g9j32g</bpmn:incoming>
      </bpmn:serviceTask>
      <bpmn:userTask id="Task_checkIfPaymentReceived" name="Payment and acceptance of constitution review" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="payment-status">
              <camunda:list>
                <camunda:value>Not received</camunda:value>
                <camunda:value>Received</camunda:value>
                <camunda:value>Expired</camunda:value>
              </camunda:list>
            </camunda:outputParameter>
          </camunda:inputOutput>
          <camunda:formData>
            <camunda:formField id="payment" label="Payment Status" type="enum" defaultValue="notReceived">
              <camunda:value id="notReceived" name="Not Received" />
              <camunda:value id="received" name="Received" />
              <camunda:value id="expired" name="Expired" />
            </camunda:formField>
          </camunda:formData>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_1ede031</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_04pqhgj</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:boundaryEvent id="BoundaryEvent_0vqwqxl" cancelActivity="false" attachedToRef="Task_checkIfPaymentReceived">
        <bpmn:outgoing>SequenceFlow_0g9j32g</bpmn:outgoing>
        <bpmn:timerEventDefinition>
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression" />
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:userTask id="Task_generateInvoice" name="Generate and email Member invoice&#10;" camunda:assignee="${FinancialAdministratorAssignee}" camunda:candidateGroups="FinancialAdministrator">
        <bpmn:incoming>SequenceFlow_01jesl4</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1ede031</bpmn:outgoing>
      </bpmn:userTask>
      <bpmn:startEvent id="StartEvent_1afkhtz">
        <bpmn:outgoing>SequenceFlow_01jesl4</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_01jesl4" sourceRef="StartEvent_1afkhtz" targetRef="Task_generateInvoice" />
      <bpmn:sequenceFlow id="SequenceFlow_1ede031" sourceRef="Task_generateInvoice" targetRef="Task_checkIfPaymentReceived" />
      <bpmn:sequenceFlow id="SequenceFlow_0g9j32g" sourceRef="BoundaryEvent_0vqwqxl" targetRef="Task_18d4zpq" />
      <bpmn:sequenceFlow id="SequenceFlow_04pqhgj" sourceRef="Task_checkIfPaymentReceived" targetRef="ExclusiveGateway_wasPaymentReceived" />
      <bpmn:sequenceFlow id="SequenceFlow_0sh548s" name="Yes" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_approve" />
      <bpmn:sequenceFlow id="SequenceFlow_0q8npmq" name="No" sourceRef="ExclusiveGateway_wasPaymentReceived" targetRef="Task_cancel" />
      <bpmn:sequenceFlow id="SequenceFlow_18f55zp" sourceRef="Task_cancel" targetRef="EndEvent_cancel" />
      <bpmn:sequenceFlow id="SequenceFlow_020ch0h" sourceRef="Task_approve" targetRef="EndEvent_0bam2or" />
      <bpmn:endEvent id="EndEvent_0bam2or">
        <bpmn:incoming>SequenceFlow_020ch0h</bpmn:incoming>
        <bpmn:terminateEventDefinition />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:association id="Association_19hve8x" sourceRef="ExclusiveGateway_0g1ewho" targetRef="TextAnnotation_1jvzeyb" />
    <bpmn:association id="Association_1tgyz3o" sourceRef="StartEvent_applicationFormCompleted" targetRef="TextAnnotation_13q0r97" />
    <bpmn:textAnnotation id="TextAnnotation_1jvzeyb">
      <bpmn:text>If Member is on DB and not in draft state Edit Mode is Enabled. If Changes are made to allowable change fields Submit Button Becomes available. Cancel Button is available for bailout. Request for help button is available.</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_13q0r97">
      <bpmn:text>Includes Non Member assessment details if applicant selects Non Member</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_15qsz6u">
      <bpmn:text>Changes can Be rolled Back or Applied</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0gjfm6r">
      <bpmndi:BPMNShape id="Participant_0pry0ja_di" bpmnElement="Participant_0pry0ja" isHorizontal="true">
        <dc:Bounds x="156" y="81" width="1505" height="1237" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1f6x8yb_di" bpmnElement="Lane_applicant" isHorizontal="true">
        <dc:Bounds x="186" y="81" width="1475" height="340" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_02jojaj_di" bpmnElement="Lane_stakeHolderManager" isHorizontal="true">
        <dc:Bounds x="186" y="560" width="1475" height="333" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_17svdmu_di" bpmnElement="Lane_financialAdministrator" isHorizontal="true">
        <dc:Bounds x="176" y="893" width="1475" height="425" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_14fq3ur_di" bpmnElement="StartEvent_applicationFormCompleted" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="378" y="122" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="357" y="85" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_11dvgjq_di" bpmnElement="Task_ReviewMemberApplication">
        <dc:Bounds x="444" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1w7teci_di" bpmnElement="SequenceFlow_1w7teci">
        <di:waypoint x="396" y="158" />
        <di:waypoint x="396" y="192" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0orik0k_di" bpmnElement="ExclusiveGateway_qualify" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="654" y="612" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="639" y="592" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0wxkgne_di" bpmnElement="Task_disqualifyMember">
        <dc:Bounds x="800" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0fuauda_di" bpmnElement="SequenceFlow_0fuauda">
        <di:waypoint x="1367" y="760" />
        <di:waypoint x="1367" y="805" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1dhwaud_di" bpmnElement="EndEvent_complete" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1349" y="805" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1340" y="851" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07az7ab_di" bpmnElement="SequenceFlow_07az7ab">
        <di:waypoint x="494" y="533" />
        <di:waypoint x="494" y="597" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_13q0r97_di" bpmnElement="TextAnnotation_13q0r97">
        <dc:Bounds x="586" y="102" width="127" height="75" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1tgyz3o_di" bpmnElement="Association_1tgyz3o">
        <di:waypoint x="414" y="140" />
        <di:waypoint x="586" y="139" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ManualTask_0cjz5mf_di" bpmnElement="Task_complete">
        <dc:Bounds x="1317" y="680" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0g1ewho_di" bpmnElement="ExclusiveGateway_0g1ewho" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="371" y="192" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="336" y="158" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1wdg5he_di" bpmnElement="SequenceFlow_1wdg5he" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="421" y="217" />
        <di:waypoint x="472" y="217" />
        <di:waypoint x="472" y="453" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="445" y="198" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1jvzeyb_di" bpmnElement="TextAnnotation_1jvzeyb">
        <dc:Bounds x="583" y="190" width="272" height="82" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_19hve8x_di" bpmnElement="Association_19hve8x">
        <di:waypoint x="420" y="218" />
        <di:waypoint x="583" y="226" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0sxjr5s_di" bpmnElement="Task_reviewMemberChanges">
        <dc:Bounds x="245" y="597" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_15qsz6u_di" bpmnElement="TextAnnotation_15qsz6u">
        <dc:Bounds x="246" y="779" width="97" height="54" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_02dg5ca_di" bpmnElement="SubProcess_NonMemberProcess" isExpanded="false">
        <dc:Bounds x="669" y="967" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0mdz1wq_di" bpmnElement="StartEvent_0mdz1wq">
        <dc:Bounds x="240" y="953" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1bgdnh2_di" bpmnElement="IntermediateCatchEvent_1bgdnh2">
        <dc:Bounds x="436" y="953" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1e74ukf_di" bpmnElement="UserTask_1e74ukf">
        <dc:Bounds x="525" y="931" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1bmvd6t_di" bpmnElement="ExclusiveGateway_1bmvd6t" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="657" y="946" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="640" y="934" width="55" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_14f3dqh_di" bpmnElement="UserTask_14f3dqh">
        <dc:Bounds x="311" y="931" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0gomw05_di" bpmnElement="UserTask_0gomw05">
        <dc:Bounds x="744" y="931" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0zk0oqd_di" bpmnElement="IntermediateCatchEvent_0zk0oqd">
        <dc:Bounds x="877" y="953" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1tviaau_di" bpmnElement="EndEvent_1tviaau">
        <dc:Bounds x="1162" y="953" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_02z37ea_di" bpmnElement="ExclusiveGateway_02z37ea" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1079" y="946" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1033" y="940" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0ehhc54_di" bpmnElement="EndEvent_0ehhc54" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1065" y="1046" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1056" y="1089" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1oej7e3_di" bpmnElement="ServiceTask_1oej7e3">
        <dc:Bounds x="798" y="1024" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0wl0agp_di" bpmnElement="SequenceFlow_0wl0agp">
        <di:waypoint x="276" y="971" />
        <di:waypoint x="311" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0aojake_di" bpmnElement="SequenceFlow_0aojake">
        <di:waypoint x="411" y="971" />
        <di:waypoint x="436" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01kh8m5_di" bpmnElement="SequenceFlow_01kh8m5" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="625" y="971" />
        <di:waypoint x="657" y="971" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="638" y="991" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01w9t59_di" bpmnElement="SequenceFlow_01w9t59">
        <di:waypoint x="472" y="971" />
        <di:waypoint x="525" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1k8f9vr_di" bpmnElement="SequenceFlow_1k8f9vr">
        <di:waypoint x="682" y="946" />
        <di:waypoint x="682" y="916" />
        <di:waypoint x="454" y="916" />
        <di:waypoint x="454" y="953" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_00g5obi_di" bpmnElement="SequenceFlow_00g5obi" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="707" y="971" />
        <di:waypoint x="744" y="971" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="718" y="953" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1urh21r_di" bpmnElement="SequenceFlow_1urh21r">
        <di:waypoint x="844" y="971" />
        <di:waypoint x="877" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09uoa55_di" bpmnElement="SequenceFlow_09uoa55">
        <di:waypoint x="913" y="971" />
        <di:waypoint x="946" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1u0awu9_di" bpmnElement="SequenceFlow_1u0awu9">
        <di:waypoint x="1046" y="971" />
        <di:waypoint x="1079" y="971" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1fkbr8a_di" bpmnElement="SequenceFlow_1fkbr8a" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1104" y="946" />
        <di:waypoint x="1104" y="910" />
        <di:waypoint x="794" y="910" />
        <di:waypoint x="794" y="931" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1115" y="927" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0iiiauc_di" bpmnElement="SequenceFlow_0iiiauc" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1129" y="971" />
        <di:waypoint x="1162" y="971" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1134" y="952" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12bo4xn_di" bpmnElement="SequenceFlow_12bo4xn" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="682" y="996" />
        <di:waypoint x="682" y="1064" />
        <di:waypoint x="798" y="1064" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="700" y="1024" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0t4slqd_di" bpmnElement="SequenceFlow_0t4slqd">
        <di:waypoint x="898" y="1064" />
        <di:waypoint x="1065" y="1064" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0vk45il_di" bpmnElement="SequenceFlow_0vk45il" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="1104" y="996" />
        <di:waypoint x="1104" y="1035" />
        <di:waypoint x="898" y="1035" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1128" y="998" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1shf565_di" bpmnElement="SequenceFlow_1shf565" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="654" y="637" />
        <di:waypoint x="654" y="803" />
        <di:waypoint x="719" y="803" />
        <di:waypoint x="719" y="967" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="617" y="773" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02smnwx_di" bpmnElement="SequenceFlow_02smnwx" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="679" y="662" />
        <di:waypoint x="679" y="743" />
        <di:waypoint x="891" y="743" />
        <di:waypoint x="891" y="967" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="871" y="764" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0xmntch_di" bpmnElement="SequenceFlow_0xmntch">
        <di:waypoint x="544" y="637" />
        <di:waypoint x="654" y="637" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sdz2lw_di" bpmnElement="SequenceFlow_0sdz2lw" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="704" y="637" />
        <di:waypoint x="800" y="637" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="718" y="620" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1civem7_di" bpmnElement="SequenceFlow_1civem7">
        <di:waypoint x="900" y="637" />
        <di:waypoint x="932" y="637" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1bu7vk2_di" bpmnElement="EndEvent_0qrrie9">
        <dc:Bounds x="932" y="619" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fghq34_di" bpmnElement="SequenceFlow_1fghq34">
        <di:waypoint x="941" y="1007" />
        <di:waypoint x="1191" y="1007" />
        <di:waypoint x="1191" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0w3obry_di" bpmnElement="SequenceFlow_0w3obry">
        <di:waypoint x="746" y="1047" />
        <di:waypoint x="746" y="1092" />
        <di:waypoint x="1236" y="1092" />
        <di:waypoint x="1236" y="760" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1lkosa1_di" bpmnElement="SequenceFlow_1lkosa1">
        <di:waypoint x="1262" y="720" />
        <di:waypoint x="1317" y="720" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0blgtwj_di" bpmnElement="EndEvent_02wd636">
        <dc:Bounds x="277" y="723" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1jj5efx_di" bpmnElement="SequenceFlow_1jj5efx">
        <di:waypoint x="295" y="677" />
        <di:waypoint x="295" y="723" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_00h5nay_di" bpmnElement="Lane_00h5nay" isHorizontal="true">
        <dc:Bounds x="186" y="421" width="1475" height="139" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1a4s1c4_di" bpmnElement="Task_allocateStakeHolderManagerManager">
        <dc:Bounds x="444" y="453" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0j5kp4j_di" bpmnElement="Task_checkIfChangesRequireApproval">
        <dc:Bounds x="226" y="177" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0k4330h_di" bpmnElement="SequenceFlow_0k4330h">
        <di:waypoint x="371" y="217" />
        <di:waypoint x="326" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="340" y="199" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0jvhffm_di" bpmnElement="ExclusiveGateway_0jvhffm" isMarkerVisible="true">
        <dc:Bounds x="234" y="309" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="261" y="270" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1m7rwdt_di" bpmnElement="SequenceFlow_1m7rwdt">
        <di:waypoint x="259" y="257" />
        <di:waypoint x="259" y="309" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1kj1x78_di" bpmnElement="SequenceFlow_1kj1x78">
        <di:waypoint x="284" y="334" />
        <di:waypoint x="327" y="334" />
        <di:waypoint x="327" y="478" />
        <di:waypoint x="284" y="478" />
        <di:waypoint x="284" y="597" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="286" y="488" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_15e2z1p_di" bpmnElement="SequenceFlow_15e2z1p">
        <di:waypoint x="259" y="359" />
        <di:waypoint x="259" y="379" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="232" y="359" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0ryd3ja_di" bpmnElement="EndEvent_1cu4bsp">
        <dc:Bounds x="241" y="379" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_12j2uw5_di" bpmnElement="SubProcess_fullMemberSubProcess" isExpanded="false">
        <dc:Bounds x="840.5" y="967" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0pa7s5q_di" bpmnElement="EndEvent_cancel" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1051" y="1049" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1008" y="504" width="81" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1nj8n50_di" bpmnElement="Task_cancel">
        <dc:Bounds x="885" y="1027" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0bt1y8h_di" bpmnElement="Task_approve">
        <dc:Bounds x="1019" y="907" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vw6etr_di" bpmnElement="ExclusiveGateway_wasPaymentReceived" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="941" y="922" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="942" y="890" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1hnftqn_di" bpmnElement="Task_18d4zpq">
        <dc:Bounds x="644" y="1015" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0w457v8_di" bpmnElement="Task_checkIfPaymentReceived">
        <dc:Bounds x="795" y="907" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_0o1s2cw_di" bpmnElement="BoundaryEvent_0vqwqxl">
        <dc:Bounds x="840" y="969" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1b6n62o_di" bpmnElement="Task_generateInvoice">
        <dc:Bounds x="652" y="907" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1afkhtz_di" bpmnElement="StartEvent_1afkhtz">
        <dc:Bounds x="577" y="921" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_01jesl4_di" bpmnElement="SequenceFlow_01jesl4">
        <di:waypoint x="613" y="939" />
        <di:waypoint x="633" y="939" />
        <di:waypoint x="633" y="947" />
        <di:waypoint x="652" y="947" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1ede031_di" bpmnElement="SequenceFlow_1ede031">
        <di:waypoint x="752" y="947" />
        <di:waypoint x="795" y="947" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0g9j32g_di" bpmnElement="SequenceFlow_0g9j32g">
        <di:waypoint x="858" y="1005" />
        <di:waypoint x="858" y="1055" />
        <di:waypoint x="744" y="1055" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_04pqhgj_di" bpmnElement="SequenceFlow_04pqhgj">
        <di:waypoint x="895" y="947" />
        <di:waypoint x="941" y="947" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sh548s_di" bpmnElement="SequenceFlow_0sh548s" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="991" y="947" />
        <di:waypoint x="1019" y="947" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="996" y="929" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0q8npmq_di" bpmnElement="SequenceFlow_0q8npmq" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="966" y="972" />
        <di:waypoint x="966" y="994" />
        <di:waypoint x="935" y="994" />
        <di:waypoint x="935" y="1027" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="943" y="976" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18f55zp_di" bpmnElement="SequenceFlow_18f55zp">
        <di:waypoint x="985" y="1067" />
        <di:waypoint x="1051" y="1067" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_020ch0h_di" bpmnElement="SequenceFlow_020ch0h">
        <di:waypoint x="1119" y="947" />
        <di:waypoint x="1168" y="947" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_14evg48_di" bpmnElement="Task_shmFinalReview">
        <dc:Bounds x="1162" y="680" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0f53tst_di" bpmnElement="EndEvent_0bam2or">
        <dc:Bounds x="1168" y="929" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1tlzqyu_di" bpmnElement="Task_0tatwfs">
        <dc:Bounds x="946" y="931" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
