<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0fss88k" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.38.1">
  <bpmn:collaboration id="Collaboration_04op5yz">
    <bpmn:participant id="Participant_0snvjg0" name="Member Status Update" processRef="Member-Status-Update" />
  </bpmn:collaboration>
  <bpmn:process id="Member-Status-Update" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_120wjrw">
      <bpmn:lane id="Lane_1bgj7a0" name="Financial Administrator">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0cwgipl</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0wv90mj" name="SACRRA Administrator">
        <bpmn:flowNodeRef>Task_RemoveMemberUsersFromDTH</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_15a2b14" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_EmailMemberToStateRemoval</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_01o5uah</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_095j66g</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_122w3zv</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="Flow_1rvyj85" sourceRef="StartEvent_1" targetRef="ExclusiveGateway_0cwgipl" />
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="memberId" label="MemberId" type="long" />
          <camunda:formField id="memberStatus" label="Member Status" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1rvyj85</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:parallelGateway id="ExclusiveGateway_0cwgipl">
      <bpmn:incoming>Flow_1rvyj85</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0heq0i7</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1vsl84s</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_08ipi3a</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0heq0i7" sourceRef="ExclusiveGateway_0cwgipl" targetRef="Task_EmailMemberToStateRemoval" />
    <bpmn:sequenceFlow id="SequenceFlow_1vsl84s" sourceRef="ExclusiveGateway_0cwgipl" targetRef="Task_RemoveMemberUsersFromDTH" />
    <bpmn:serviceTask id="Task_EmailMemberToStateRemoval" name="Email member to state removal" camunda:type="external" camunda:topic="email-member-to-state-removal">
      <bpmn:incoming>SequenceFlow_0heq0i7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1gkyjhf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_RemoveMemberUsersFromDTH" name="Add/remove member user(s) from DTH" camunda:assignee="${stakeHolderManagerManagerAssignee}" camunda:candidateGroups="StakeHolderAdministrator">
      <bpmn:incoming>SequenceFlow_1vsl84s</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1e4xc6s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="SequenceFlow_1e4xc6s" sourceRef="Task_RemoveMemberUsersFromDTH" targetRef="Event_122w3zv" />
    <bpmn:serviceTask id="Task_01o5uah" name="Enable/Disabe member users" camunda:type="external" camunda:topic="enable-or-disable-member-users">
      <bpmn:incoming>SequenceFlow_08ipi3a</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19mnr21</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_08ipi3a" sourceRef="ExclusiveGateway_0cwgipl" targetRef="Task_01o5uah" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_095j66g">
      <bpmn:incoming>SequenceFlow_19mnr21</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1gkyjhf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0bu3ouk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0bu3ouk" sourceRef="ExclusiveGateway_095j66g" targetRef="Event_122w3zv" />
    <bpmn:endEvent id="Event_122w3zv">
      <bpmn:incoming>SequenceFlow_1e4xc6s</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0bu3ouk</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_19mnr21" sourceRef="Task_01o5uah" targetRef="ExclusiveGateway_095j66g" />
    <bpmn:sequenceFlow id="SequenceFlow_1gkyjhf" sourceRef="Task_EmailMemberToStateRemoval" targetRef="ExclusiveGateway_095j66g" />
    <bpmn:textAnnotation id="TextAnnotation_10t2a87">
      <bpmn:text>Trigger for this workflow is when the member status is changed to "Cancelled"</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1jrsreg" sourceRef="StartEvent_1" targetRef="TextAnnotation_10t2a87" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_04op5yz">
      <bpmndi:BPMNShape id="Participant_0snvjg0_di" bpmnElement="Participant_0snvjg0" isHorizontal="true">
        <dc:Bounds x="160" y="52" width="780" height="698" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_15a2b14_di" bpmnElement="Lane_15a2b14" isHorizontal="true">
        <dc:Bounds x="190" y="302" width="750" height="248" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0wv90mj_di" bpmnElement="Lane_0wv90mj" isHorizontal="true">
        <dc:Bounds x="190" y="550" width="750" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1bgj7a0_di" bpmnElement="Lane_1bgj7a0" isHorizontal="true">
        <dc:Bounds x="190" y="52" width="750" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="307" y="159" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_14crai8_di" bpmnElement="ExclusiveGateway_0cwgipl">
        <dc:Bounds x="425" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mex7pa_di" bpmnElement="Task_EmailMemberToStateRemoval">
        <dc:Bounds x="570" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0t6yhpz_di" bpmnElement="Task_RemoveMemberUsersFromDTH">
        <dc:Bounds x="570" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1s1oncg_di" bpmnElement="Task_01o5uah">
        <dc:Bounds x="570" y="440" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_095j66g_di" bpmnElement="ExclusiveGateway_095j66g" isMarkerVisible="true">
        <dc:Bounds x="745" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_16ebwuw_di" bpmnElement="Event_122w3zv">
        <dc:Bounds x="862" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_10t2a87_di" bpmnElement="TextAnnotation_10t2a87">
        <dc:Bounds x="230" y="80" width="230" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1rvyj85_di" bpmnElement="Flow_1rvyj85">
        <di:waypoint x="343" y="177" />
        <di:waypoint x="425" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0heq0i7_di" bpmnElement="SequenceFlow_0heq0i7">
        <di:waypoint x="475" y="177" />
        <di:waypoint x="620" y="177" />
        <di:waypoint x="620" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1vsl84s_di" bpmnElement="SequenceFlow_1vsl84s">
        <di:waypoint x="450" y="202" />
        <di:waypoint x="450" y="620" />
        <di:waypoint x="570" y="620" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1e4xc6s_di" bpmnElement="SequenceFlow_1e4xc6s">
        <di:waypoint x="670" y="620" />
        <di:waypoint x="880" y="620" />
        <di:waypoint x="880" y="388" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08ipi3a_di" bpmnElement="SequenceFlow_08ipi3a">
        <di:waypoint x="450" y="202" />
        <di:waypoint x="450" y="480" />
        <di:waypoint x="570" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0bu3ouk_di" bpmnElement="SequenceFlow_0bu3ouk">
        <di:waypoint x="795" y="370" />
        <di:waypoint x="862" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19mnr21_di" bpmnElement="SequenceFlow_19mnr21">
        <di:waypoint x="670" y="480" />
        <di:waypoint x="770" y="480" />
        <di:waypoint x="770" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1gkyjhf_di" bpmnElement="SequenceFlow_1gkyjhf">
        <di:waypoint x="670" y="370" />
        <di:waypoint x="745" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1jrsreg_di" bpmnElement="Association_1jrsreg">
        <di:waypoint x="316" y="161" />
        <di:waypoint x="299" y="130" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
