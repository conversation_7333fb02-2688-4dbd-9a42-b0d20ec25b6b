<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_10y4abu" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_189nhi4">
    <bpmn:participant id="Participant_0mfwl7o" name="New DW Exception" processRef="New-DW-Exception" />
  </bpmn:collaboration>
  <bpmn:process id="New-DW-Exception" isExecutable="true" camunda:versionTag="V1" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1yhe2ra">
      <bpmn:lane id="Lane_0uz9udl" name="SHM">
        <bpmn:flowNodeRef>StartEvent_NewDWExceptionRecived</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_CompleteException</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_ExceptionCompleted</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_NewDWExceptionRecived" name="New DW Exception Recived">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="FctWarehouseExceptionID" label="Fct Warehouse Exception ID" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1sczu8r</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_CompleteException" name="Complete DW exception" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_1sczu8r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11t2tb4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ExceptionCompleted">
      <bpmn:incoming>SequenceFlow_11t2tb4</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_11t2tb4" sourceRef="Task_CompleteException" targetRef="EndEvent_ExceptionCompleted" />
    <bpmn:sequenceFlow id="SequenceFlow_1sczu8r" sourceRef="StartEvent_NewDWExceptionRecived" targetRef="Task_CompleteException" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_189nhi4">
      <bpmndi:BPMNShape id="Participant_0mfwl7o_di" bpmnElement="Participant_0mfwl7o" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="770" height="350" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_16imuqx_di" bpmnElement="StartEvent_NewDWExceptionRecived">
        <dc:Bounds x="262" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="256" y="215" width="49" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1sczu8r_di" bpmnElement="SequenceFlow_1sczu8r">
        <di:waypoint x="298" y="190" />
        <di:waypoint x="620" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0xpum2c_di" bpmnElement="Task_CompleteException">
        <dc:Bounds x="620" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_11t2tb4_di" bpmnElement="SequenceFlow_11t2tb4">
        <di:waypoint x="670" y="230" />
        <di:waypoint x="670" y="322" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0ywy2ds_di" bpmnElement="EndEvent_ExceptionCompleted">
        <dc:Bounds x="652" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0uz9udl_di" bpmnElement="Lane_0uz9udl" isHorizontal="true">
        <dc:Bounds x="190" y="80" width="740" height="350" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
