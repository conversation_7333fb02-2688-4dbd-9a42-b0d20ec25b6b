<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="Definitions_053lmvj" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="2.2.4">
  <bpmn:collaboration id="Collaboration_008vpn1">
    <bpmn:participant id="Participant_1kh7vyo" name="SRN Requests" processRef="Process_0vnhxm7" />
  </bpmn:collaboration>
  <bpmn:process id="Process_0vnhxm7" isExecutable="false" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1bge26e">
      <bpmn:lane id="Lane_0587rdi" name="SHM 1">
        <bpmn:flowNodeRef>StartEvent_0ntmvkx</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_14q9hgp</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_13fduj8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1jqsdbq</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_0q7a5fu</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1c25lj8" name="SHM 2">
        <bpmn:flowNodeRef>Task_0tpk26s</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0p85ned</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0z8rtoo</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_03luzz5</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_035400c" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_1ydmkyf</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0qgfww3" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_0aitsfv</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_04z8c36</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_0ntmvkx" name="SRN(s) Request">
      <bpmn:outgoing>SequenceFlow_0h2lihi</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_14q9hgp">
      <bpmn:incoming>SequenceFlow_1hhhnnm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ngaezn</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_08yax28</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_13fduj8" name="SHM Reviews SRN(s) Application">
      <bpmn:incoming>SequenceFlow_0h2lihi</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_08yax28</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1hhhnnm</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_0tpk26s" name="SHM Second Review">
      <bpmn:incoming>SequenceFlow_0ngaezn</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_170go15</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_140lotf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0p85ned">
      <bpmn:incoming>SequenceFlow_140lotf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_170go15</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0e86z0g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_0z8rtoo" name="SRN Guid Created">
      <bpmn:incoming>SequenceFlow_0e86z0g</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0hmay9o</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_03luzz5" name="All Documents Provided">
      <bpmn:incoming>SequenceFlow_0hmay9o</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ojgg4r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0h2lihi" sourceRef="StartEvent_0ntmvkx" targetRef="Task_13fduj8" />
    <bpmn:sequenceFlow id="SequenceFlow_1hhhnnm" sourceRef="Task_13fduj8" targetRef="ExclusiveGateway_14q9hgp" />
    <bpmn:sequenceFlow id="SequenceFlow_0ngaezn" name="SHM Approved" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_0tpk26s" />
    <bpmn:sequenceFlow id="SequenceFlow_08yax28" name="SHM Amends" sourceRef="ExclusiveGateway_14q9hgp" targetRef="Task_13fduj8" />
    <bpmn:sequenceFlow id="SequenceFlow_140lotf" sourceRef="Task_0tpk26s" targetRef="ExclusiveGateway_0p85ned" />
    <bpmn:sequenceFlow id="SequenceFlow_170go15" name="SHM Amends" sourceRef="ExclusiveGateway_0p85ned" targetRef="Task_0tpk26s" />
    <bpmn:sequenceFlow id="SequenceFlow_0e86z0g" sourceRef="ExclusiveGateway_0p85ned" targetRef="Task_0z8rtoo" />
    <bpmn:sequenceFlow id="SequenceFlow_0hmay9o" sourceRef="Task_0z8rtoo" targetRef="ExclusiveGateway_03luzz5" />
    <bpmn:sequenceFlow id="SequenceFlow_0ojgg4r" sourceRef="ExclusiveGateway_03luzz5" targetRef="Task_1ydmkyf" />
    <bpmn:serviceTask id="Task_1ydmkyf" name="eMail Bureaus &#38; Update Portal Views">
      <bpmn:incoming>SequenceFlow_0ojgg4r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0e8m6kc</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_07v22hd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0e8m6kc" sourceRef="Task_1ydmkyf" targetRef="Task_0aitsfv" />
    <bpmn:sequenceFlow id="SequenceFlow_07v22hd" sourceRef="Task_1ydmkyf" targetRef="Task_1jqsdbq" />
    <bpmn:manualTask id="Task_0aitsfv" name="Update DTH">
      <bpmn:incoming>SequenceFlow_0e8m6kc</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_04fxz16</bpmn:outgoing>
    </bpmn:manualTask>
    <bpmn:endEvent id="EndEvent_04z8c36" name="DTH Take on Complete">
      <bpmn:incoming>SequenceFlow_04fxz16</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_04fxz16" sourceRef="Task_0aitsfv" targetRef="EndEvent_04z8c36" />
    <bpmn:manualTask id="Task_1jqsdbq" name="Take On Process">
      <bpmn:incoming>SequenceFlow_07v22hd</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_06xow2e</bpmn:outgoing>
    </bpmn:manualTask>
    <bpmn:endEvent id="EndEvent_0q7a5fu" name="SRN Application Complete">
      <bpmn:incoming>SequenceFlow_06xow2e</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_06xow2e" sourceRef="Task_1jqsdbq" targetRef="EndEvent_0q7a5fu" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_008vpn1">
      <bpmndi:BPMNShape id="Participant_1kh7vyo_di" bpmnElement="Participant_1kh7vyo">
        <dc:Bounds x="236" y="-1213" width="845" height="715" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0587rdi_di" bpmnElement="Lane_0587rdi">
        <dc:Bounds x="266" y="-1213" width="815" height="194" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1c25lj8_di" bpmnElement="Lane_1c25lj8">
        <dc:Bounds x="266" y="-1019" width="815" height="182" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0ntmvkx_di" bpmnElement="StartEvent_0ntmvkx">
        <dc:Bounds x="339" y="-1135" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="316" y="-1092" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0h2lihi_di" bpmnElement="SequenceFlow_0h2lihi">
        <di:waypoint x="375" y="-1117" />
        <di:waypoint x="428" y="-1117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_12x7cdl_di" bpmnElement="Task_13fduj8">
        <dc:Bounds x="428" y="-1157" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_14q9hgp_di" bpmnElement="ExclusiveGateway_14q9hgp" isMarkerVisible="true">
        <dc:Bounds x="583" y="-1142" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1hhhnnm_di" bpmnElement="SequenceFlow_1hhhnnm">
        <di:waypoint x="528" y="-1117" />
        <di:waypoint x="583" y="-1117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ngaezn_di" bpmnElement="SequenceFlow_0ngaezn">
        <di:waypoint x="608" y="-1092" />
        <di:waypoint x="608" y="-1044" />
        <di:waypoint x="464" y="-1044" />
        <di:waypoint x="464" y="-945" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="500" y="-1062" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_115rzl7_di" bpmnElement="Task_0tpk26s">
        <dc:Bounds x="414" y="-945" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_08yax28_di" bpmnElement="SequenceFlow_08yax28">
        <di:waypoint x="608" y="-1142" />
        <di:waypoint x="608" y="-1188" />
        <di:waypoint x="478" y="-1188" />
        <di:waypoint x="478" y="-1159" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="509" y="-1206" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0p85ned_di" bpmnElement="ExclusiveGateway_0p85ned" isMarkerVisible="true">
        <dc:Bounds x="573" y="-930" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_140lotf_di" bpmnElement="SequenceFlow_140lotf">
        <di:waypoint x="514" y="-905" />
        <di:waypoint x="573" y="-905" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_170go15_di" bpmnElement="SequenceFlow_170go15">
        <di:waypoint x="598" y="-930" />
        <di:waypoint x="598" y="-987" />
        <di:waypoint x="483" y="-987" />
        <di:waypoint x="483" y="-947" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="506" y="-1005" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0e86z0g_di" bpmnElement="SequenceFlow_0e86z0g">
        <di:waypoint x="623" y="-905" />
        <di:waypoint x="682" y="-905" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0we9zcl_di" bpmnElement="Task_0z8rtoo">
        <dc:Bounds x="682" y="-945" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_03luzz5_di" bpmnElement="ExclusiveGateway_03luzz5" isMarkerVisible="true">
        <dc:Bounds x="841" y="-930" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="830" y="-969" width="71" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0hmay9o_di" bpmnElement="SequenceFlow_0hmay9o">
        <di:waypoint x="782" y="-905" />
        <di:waypoint x="841" y="-905" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Lane_035400c_di" bpmnElement="Lane_035400c">
        <dc:Bounds x="266" y="-837" width="815" height="183" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0ojgg4r_di" bpmnElement="SequenceFlow_0ojgg4r">
        <di:waypoint x="866" y="-880" />
        <di:waypoint x="866" y="-816" />
        <di:waypoint x="449" y="-816" />
        <di:waypoint x="449" y="-769" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0yorbl1_di" bpmnElement="Task_1ydmkyf">
        <dc:Bounds x="399" y="-769" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0qgfww3_di" bpmnElement="Lane_0qgfww3">
        <dc:Bounds x="266" y="-654" width="815" height="156" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0e8m6kc_di" bpmnElement="SequenceFlow_0e8m6kc">
        <di:waypoint x="449" y="-689" />
        <di:waypoint x="449" y="-615" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ManualTask_0vqscvr_di" bpmnElement="Task_0aitsfv">
        <dc:Bounds x="399" y="-615" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_07v22hd_di" bpmnElement="SequenceFlow_07v22hd">
        <di:waypoint x="499" y="-729" />
        <di:waypoint x="918" y="-729" />
        <di:waypoint x="918" y="-1076" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ManualTask_1oi9ozf_di" bpmnElement="Task_1jqsdbq">
        <dc:Bounds x="868" y="-1156" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_04z8c36_di" bpmnElement="EndEvent_04z8c36">
        <dc:Bounds x="549" y="-593" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="535" y="-550" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_04fxz16_di" bpmnElement="SequenceFlow_04fxz16">
        <di:waypoint x="499" y="-575" />
        <di:waypoint x="549" y="-575" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0q7a5fu_di" bpmnElement="EndEvent_0q7a5fu">
        <dc:Bounds x="1018" y="-1134" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="996" y="-1091" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_06xow2e_di" bpmnElement="SequenceFlow_06xow2e">
        <di:waypoint x="968" y="-1116" />
        <di:waypoint x="1018" y="-1116" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
