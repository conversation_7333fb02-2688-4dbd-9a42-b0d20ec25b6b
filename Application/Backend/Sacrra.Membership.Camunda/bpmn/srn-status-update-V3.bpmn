<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_061qjsw" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.38.1">
  <bpmn:collaboration id="Collaboration_1hh6ezi">
    <bpmn:participant id="Participant_0hdz6up" name="SRN Status Update" processRef="SRN-Status-Update" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Status-Update" isExecutable="true" camunda:versionTag="V3.0" camunda:historyTimeToLive="30">
    <bpmn:laneSet id="LaneSet_1gqb0jp">
      <bpmn:lane id="Lane_SHM" name="SHM">
        <bpmn:flowNodeRef>StartEvent_1yf9ovw</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_19bogx4</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_19hcmn0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_0hfuhxh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_1dique0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0rxcf39</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_Portal" name="Portal">
        <bpmn:flowNodeRef>Task_email_member_about_SRN_pending_closure</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_email_bureaus_about_srn_pending_closure</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_update_srn_status_to_pending_closure</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0fxr395</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_update_srn_status_to_closed</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_email_bureaus_to_close_the_payment_profile</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_email_member_to_confirm_closure</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_set_srn_status_date_to_todays_date</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_06t17wf</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0s8oszt</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_SacrraAdmin" name="SACRRA Admin">
        <bpmn:flowNodeRef>EndEvent_13d5sd6</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_16s7ny3</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_1yf9ovw" name="Start status update">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="updateType" label="Update Type" type="enum">
            <camunda:value id="lastSubmission" name="Last Submission" />
            <camunda:value id="bureauInstruction" name="Bureau Instruction" />
          </camunda:formField>
          <camunda:formField id="lastSubmissionDate" label="Last Submission Date" type="enum">
            <camunda:value id="inThePast" name="In The Past" />
            <camunda:value id="inTheFuture" name="In The Future" />
          </camunda:formField>
          <camunda:formField id="statusDate" type="enum">
            <camunda:value id="inThePast" name="In The Past" />
            <camunda:value id="inTheFuture" name="In The Future" />
          </camunda:formField>
          <camunda:formField id="SRNId" label="SRN Id" type="long" />
          <camunda:formField id="isLiveFileSubmissionsSuspended" label="Is Live File Submissions Suspended" type="boolean" />
          <camunda:formField id="SRNUpdateType" label="SRN Update Type" type="long" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0crh8zm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1i3kvji" name="In the future" sourceRef="ExclusiveGateway_1dique0" targetRef="Task_update_srn_status_to_pending_closure">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${lastSubmissionDate == "inTheFuture"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0gpor7e" name="In the past" sourceRef="ExclusiveGateway_1dique0" targetRef="Task_0hfuhxh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${lastSubmissionDate == "inThePast"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0crh8zm" sourceRef="StartEvent_1yf9ovw" targetRef="ExclusiveGateway_19bogx4" />
    <bpmn:sequenceFlow id="SequenceFlow_04s56k3" sourceRef="Task_update_srn_status_to_pending_closure" targetRef="Task_email_bureaus_about_srn_pending_closure" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_19bogx4" name="Is it last submission or bureau instruction or SRN &#34;recalled&#34;?">
      <bpmn:incoming>SequenceFlow_0crh8zm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0b9d2vg</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0r01mpf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qf00pk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0b9d2vg" name="Last submission" sourceRef="ExclusiveGateway_19bogx4" targetRef="ExclusiveGateway_1dique0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${updateType == "lastSubmission"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0r01mpf" name="Bureau instruction" sourceRef="ExclusiveGateway_19bogx4" targetRef="ExclusiveGateway_19hcmn0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${updateType == "bureauInstruction"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_19hcmn0" name="Is status date in the past or future?">
      <bpmn:incoming>SequenceFlow_0r01mpf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1bcfjjj</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1eeu0ex</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1bcfjjj" name="In the past" sourceRef="ExclusiveGateway_19hcmn0" targetRef="ExclusiveGateway_0fxr395">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${statusDate == "inThePast"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1eeu0ex" name="In the future" sourceRef="ExclusiveGateway_19hcmn0" targetRef="Task_update_srn_status_to_pending_closure">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${statusDate == "inTheFuture"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_18kqx18" sourceRef="Task_update_srn_status_to_closed" targetRef="Task_email_bureaus_to_close_the_payment_profile" />
    <bpmn:sequenceFlow id="SequenceFlow_1qj9hcf" sourceRef="Task_email_bureaus_to_close_the_payment_profile" targetRef="Task_email_member_to_confirm_closure" />
    <bpmn:sequenceFlow id="SequenceFlow_1wycq0j" sourceRef="Task_email_member_to_confirm_closure" targetRef="Task_16s7ny3" />
    <bpmn:sequenceFlow id="SequenceFlow_0e6gqn2" sourceRef="Task_16s7ny3" targetRef="EndEvent_13d5sd6" />
    <bpmn:sequenceFlow id="SequenceFlow_1p2f7ot" sourceRef="Task_email_bureaus_about_srn_pending_closure" targetRef="Task_email_member_about_SRN_pending_closure" />
    <bpmn:sequenceFlow id="SequenceFlow_1qr27sl" sourceRef="Task_email_member_about_SRN_pending_closure" targetRef="Task_0hfuhxh" />
    <bpmn:userTask id="Task_0hfuhxh" name="Confirm file submission" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_0gpor7e</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1qr27sl</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_06vv1r1</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_email_member_about_SRN_pending_closure" name="Email member about SRN pending closure" camunda:type="external" camunda:topic="email-member-about-SRN-pending-closure">
      <bpmn:incoming>SequenceFlow_1p2f7ot</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1qr27sl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_email_bureaus_about_srn_pending_closure" name="Email bureaus about SRN pending closure" camunda:type="external" camunda:topic="email-bureaus-about-srn-pending-closure">
      <bpmn:incoming>SequenceFlow_04s56k3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1p2f7ot</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_update_srn_status_to_pending_closure" name="Update status to &#34;Closure Pending&#34;" camunda:type="external" camunda:topic="update-srn-status-to-pending-closure">
      <bpmn:incoming>SequenceFlow_1i3kvji</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1eeu0ex</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_04s56k3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1dique0" name="Is last submission date is the past or future?">
      <bpmn:incoming>SequenceFlow_0b9d2vg</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1i3kvji</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0gpor7e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_06vv1r1" sourceRef="Task_0hfuhxh" targetRef="Activity_0rxcf39" />
    <bpmn:parallelGateway id="ExclusiveGateway_0fxr395">
      <bpmn:incoming>SequenceFlow_1bcfjjj</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_03cncr5</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_15k3vh9</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_03cncr5" sourceRef="ExclusiveGateway_0fxr395" targetRef="Task_update_srn_status_to_closed" />
    <bpmn:sequenceFlow id="SequenceFlow_15k3vh9" sourceRef="ExclusiveGateway_0fxr395" targetRef="Task_set_srn_status_date_to_todays_date" />
    <bpmn:serviceTask id="Task_update_srn_status_to_closed" name="Update status to &#34;Closed&#34;" camunda:type="external" camunda:topic="update-srn-status-to-closed">
      <bpmn:incoming>SequenceFlow_03cncr5</bpmn:incoming>
      <bpmn:incoming>Flow_1814h3p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_18kqx18</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="EndEvent_13d5sd6">
      <bpmn:incoming>SequenceFlow_0e6gqn2</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Task_email_bureaus_to_close_the_payment_profile" name="Email bureaus to close the payment profile" camunda:type="external" camunda:topic="email-bureaus-to-close-the-payment-profile">
      <bpmn:incoming>SequenceFlow_18kqx18</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1qj9hcf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_email_member_to_confirm_closure" name="Email member to confirm closure" camunda:type="external" camunda:topic="email-member-to-confirm-srn-closure">
      <bpmn:incoming>SequenceFlow_1qj9hcf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wycq0j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_16s7ny3" name="SRN Status Changed - Confirm Updated DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:incoming>SequenceFlow_1wycq0j</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0e6gqn2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_set_srn_status_date_to_todays_date" name="Set SRN status date to today&#39;s date" camunda:type="external" camunda:topic="set-srn-status-date-to-todays-date">
      <bpmn:incoming>SequenceFlow_15k3vh9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0qf00pk" name="Recalled" sourceRef="ExclusiveGateway_19bogx4" targetRef="Activity_06t17wf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${SRNStatus=="Recalled"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_06t17wf" name="Check for and kill file testing tasks related to this SRN" camunda:type="external" camunda:topic="kill_file_test_tasks">
      <bpmn:incoming>Flow_0qf00pk</bpmn:incoming>
      <bpmn:outgoing>Flow_0mm6ivx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0mm6ivx" sourceRef="Activity_06t17wf" targetRef="Event_0s8oszt" />
    <bpmn:endEvent id="Event_0s8oszt">
      <bpmn:incoming>Flow_0mm6ivx</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_1o7qlj4" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1814h3p" sourceRef="Activity_0rxcf39" targetRef="Task_update_srn_status_to_closed" />
    <bpmn:serviceTask id="Activity_0rxcf39" name="Update Confirm File Submission" camunda:type="external" camunda:topic="update-confirm-submission">
      <bpmn:incoming>SequenceFlow_06vv1r1</bpmn:incoming>
      <bpmn:outgoing>Flow_1814h3p</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:textAnnotation id="TextAnnotation_1omq0wn">
      <bpmn:text>Confirm if file has been submitted</bpmn:text>
    </bpmn:textAnnotation>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1hh6ezi">
      <bpmndi:BPMNShape id="Participant_0hdz6up_di" bpmnElement="Participant_0hdz6up" isHorizontal="true">
        <dc:Bounds x="160" y="60" width="1290" height="910" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_00euzm8_di" bpmnElement="Lane_SacrraAdmin" isHorizontal="true">
        <dc:Bounds x="190" y="840" width="1260" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0eo72lt_di" bpmnElement="Lane_Portal" isHorizontal="true">
        <dc:Bounds x="190" y="430" width="1260" height="410" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_195rlz9_di" bpmnElement="Lane_SHM" isHorizontal="true">
        <dc:Bounds x="190" y="60" width="1260" height="370" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1yf9ovw_di" bpmnElement="StartEvent_1yf9ovw">
        <dc:Bounds x="252" y="192" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="243" y="235" width="57" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_19bogx4_di" bpmnElement="ExclusiveGateway_19bogx4" isMarkerVisible="true">
        <dc:Bounds x="425" y="125" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="411" y="57" width="88" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_19hcmn0_di" bpmnElement="ExclusiveGateway_19hcmn0" isMarkerVisible="true">
        <dc:Bounds x="425" y="305" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="325.5" y="316" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0arb0ia_di" bpmnElement="Task_0hfuhxh">
        <dc:Bounds x="1070" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1t4oxjk_di" bpmnElement="Task_email_member_about_SRN_pending_closure">
        <dc:Bounds x="1080" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_09ctpbt_di" bpmnElement="Task_email_bureaus_about_srn_pending_closure">
        <dc:Bounds x="890" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1bzitj2_di" bpmnElement="Task_update_srn_status_to_pending_closure">
        <dc:Bounds x="710" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1dique0_di" bpmnElement="ExclusiveGateway_1dique0" isMarkerVisible="true">
        <dc:Bounds x="735" y="125" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="718" y="80" width="89" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_1h9llll_di" bpmnElement="ExclusiveGateway_0fxr395">
        <dc:Bounds x="425" y="455" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1si4n20_di" bpmnElement="Task_update_srn_status_to_closed">
        <dc:Bounds x="500" y="550" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1rk3j5b_di" bpmnElement="EndEvent_13d5sd6">
        <dc:Bounds x="922" y="902" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1rzbdy0_di" bpmnElement="Task_email_bureaus_to_close_the_payment_profile">
        <dc:Bounds x="500" y="750" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_145pn98_di" bpmnElement="Task_email_member_to_confirm_closure">
        <dc:Bounds x="720" y="750" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_17nrhph_di" bpmnElement="Task_16s7ny3">
        <dc:Bounds x="720" y="880" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0e7gt3k_di" bpmnElement="Task_set_srn_status_date_to_todays_date">
        <dc:Bounds x="340" y="550" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yoxa0z_di" bpmnElement="Activity_06t17wf">
        <dc:Bounds x="220" y="550" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ji62oe_di" bpmnElement="Event_0s8oszt">
        <dc:Bounds x="252" y="672" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tczfza_di" bpmnElement="Activity_0rxcf39">
        <dc:Bounds x="1290" y="210" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1omq0wn_di" bpmnElement="TextAnnotation_1omq0wn">
        <dc:Bounds x="1180" y="90" width="100" height="54" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1i3kvji_di" bpmnElement="SequenceFlow_1i3kvji">
        <di:waypoint x="760" y="175" />
        <di:waypoint x="760" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="764" y="313" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0gpor7e_di" bpmnElement="SequenceFlow_0gpor7e">
        <di:waypoint x="785" y="150" />
        <di:waypoint x="1070" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="905" y="132" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0crh8zm_di" bpmnElement="SequenceFlow_0crh8zm">
        <di:waypoint x="270" y="192" />
        <di:waypoint x="270" y="150" />
        <di:waypoint x="425" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_04s56k3_di" bpmnElement="SequenceFlow_04s56k3">
        <di:waypoint x="810" y="510" />
        <di:waypoint x="890" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0b9d2vg_di" bpmnElement="SequenceFlow_0b9d2vg">
        <di:waypoint x="475" y="150" />
        <di:waypoint x="735" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="520" y="132" width="79" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0r01mpf_di" bpmnElement="SequenceFlow_0r01mpf">
        <di:waypoint x="450" y="175" />
        <di:waypoint x="450" y="305" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="455" y="213" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1bcfjjj_di" bpmnElement="SequenceFlow_1bcfjjj">
        <di:waypoint x="450" y="355" />
        <di:waypoint x="450" y="455" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="393" y="396" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1eeu0ex_di" bpmnElement="SequenceFlow_1eeu0ex">
        <di:waypoint x="475" y="330" />
        <di:waypoint x="600" y="330" />
        <di:waypoint x="600" y="510" />
        <di:waypoint x="710" y="510" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="610" y="391" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18kqx18_di" bpmnElement="SequenceFlow_18kqx18">
        <di:waypoint x="550" y="630" />
        <di:waypoint x="550" y="750" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1qj9hcf_di" bpmnElement="SequenceFlow_1qj9hcf">
        <di:waypoint x="600" y="790" />
        <di:waypoint x="720" y="790" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wycq0j_di" bpmnElement="SequenceFlow_1wycq0j">
        <di:waypoint x="770" y="830" />
        <di:waypoint x="770" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0e6gqn2_di" bpmnElement="SequenceFlow_0e6gqn2">
        <di:waypoint x="820" y="920" />
        <di:waypoint x="922" y="920" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1p2f7ot_di" bpmnElement="SequenceFlow_1p2f7ot">
        <di:waypoint x="990" y="510" />
        <di:waypoint x="1080" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1qr27sl_di" bpmnElement="SequenceFlow_1qr27sl">
        <di:waypoint x="1130" y="470" />
        <di:waypoint x="1130" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_06vv1r1_di" bpmnElement="SequenceFlow_06vv1r1">
        <di:waypoint x="1170" y="150" />
        <di:waypoint x="1340" y="150" />
        <di:waypoint x="1340" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_03cncr5_di" bpmnElement="SequenceFlow_03cncr5">
        <di:waypoint x="475" y="480" />
        <di:waypoint x="550" y="480" />
        <di:waypoint x="550" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_15k3vh9_di" bpmnElement="SequenceFlow_15k3vh9">
        <di:waypoint x="425" y="480" />
        <di:waypoint x="400" y="480" />
        <di:waypoint x="400" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qf00pk_di" bpmnElement="Flow_0qf00pk">
        <di:waypoint x="430" y="155" />
        <di:waypoint x="430" y="270" />
        <di:waypoint x="270" y="270" />
        <di:waypoint x="270" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="329" y="252" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mm6ivx_di" bpmnElement="Flow_0mm6ivx">
        <di:waypoint x="270" y="630" />
        <di:waypoint x="270" y="672" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1814h3p_di" bpmnElement="Flow_1814h3p">
        <di:waypoint x="1340" y="290" />
        <di:waypoint x="1340" y="590" />
        <di:waypoint x="600" y="590" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
