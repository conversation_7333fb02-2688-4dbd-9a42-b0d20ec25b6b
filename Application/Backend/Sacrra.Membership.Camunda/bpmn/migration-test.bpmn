<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0l2e8ew" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:collaboration id="Collaboration_0l3iox2">
    <bpmn:participant id="Participant_03sn9gc" name="Migration Test" processRef="Migration-Test" />
  </bpmn:collaboration>
  <bpmn:process id="Migration-Test" name="Migration Test" isExecutable="true" camunda:versionTag="2" camunda:historyTimeToLive="30">
    <bpmn:sequenceFlow id="SequenceFlow_1jd30ul" sourceRef="StartEvent_1" targetRef="Task_wake_up" />
    <bpmn:sequenceFlow id="SequenceFlow_04sx2p7" sourceRef="Task_wake_up" targetRef="Task_sleep" />
    <bpmn:sequenceFlow id="SequenceFlow_1arjzm0" sourceRef="Task_sleep" targetRef="EndEvent_1vmz8bi" />
    <bpmn:endEvent id="EndEvent_1vmz8bi">
      <bpmn:incoming>SequenceFlow_1arjzm0</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>SequenceFlow_1jd30ul</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_wake_up" name="Wake up" camunda:assignee="294" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_1jd30ul</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_04sx2p7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_sleep" name="Sleep" camunda:assignee="294" camunda:candidateGroups="StakeHolderManager">
      <bpmn:incoming>SequenceFlow_04sx2p7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1arjzm0</bpmn:outgoing>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_0l3iox2">
      <bpmndi:BPMNShape id="Participant_03sn9gc_di" bpmnElement="Participant_03sn9gc" isHorizontal="true">
        <dc:Bounds x="129" y="77" width="911" height="333" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1jd30ul_di" bpmnElement="SequenceFlow_1jd30ul">
        <di:waypoint x="248" y="210" />
        <di:waypoint x="400" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_04sx2p7_di" bpmnElement="SequenceFlow_04sx2p7">
        <di:waypoint x="500" y="210" />
        <di:waypoint x="620" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1arjzm0_di" bpmnElement="SequenceFlow_1arjzm0">
        <di:waypoint x="720" y="210" />
        <di:waypoint x="842" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0isykjd_di" bpmnElement="EndEvent_1vmz8bi">
        <dc:Bounds x="842" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1lm25ny_di" bpmnElement="Task_wake_up">
        <dc:Bounds x="400" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_1nx5fz6_di" bpmnElement="Task_sleep">
        <dc:Bounds x="620" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
