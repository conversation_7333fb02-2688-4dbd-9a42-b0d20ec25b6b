using System;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database.Views;

namespace Sacrra.Membership.Database
{
    public class AppDbContext : DbContext
    {
        // Needed for EF Core CLI tools
        public AppDbContext()
        {
        }

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
            // Make the command timeout of 5 minutes so production stops falling over
            this.Database.SetCommandTimeout(300);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // This connection string is only used for EF Core CLI tools
                optionsBuilder.UseSqlServer("Server=localhost;Database=Sacrra.Membership;Trusted_Connection=True;MultipleActiveResultSets=true");
            }
        }

        public DbSet<Member> Members { get; set; }
        public DbSet<Bureau> Bureaus { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<PartialMember> PartialMembers { get; set; }
        public DbSet<ContactType> ContactTypes { get; set; }
        public DbSet<MemberContact> MemberContacts { get; set; }
        public DbSet<EventLog> EventLogs { get; set; }
        public DbSet<TradingName> TradingNames { get; set; }

        public DbSet<SRN> SRNs { get; set; }
        public DbSet<ALG> ALGs { get; set; }
        public DbSet<LoanManagementSystemVendor> LoanManagementSystemVendors { get; set; }
        public DbSet<SoftwareVendor> SoftwareVendors { get; set; }
        public DbSet<AccountType> AccountTypes { get; set; }
        public DbSet<SRNStatusReason> SRNStatusReasons { get; set; }
        public DbSet<SRNStatus> SRNStatuses { get; set; }
        public DbSet<BranchLocation> BranchLocations { get; set; }
        public DbSet<NCRReportingAccountTypeClassification> NCRReportingAccountTypeClassifications { get; set; }
        public DbSet<SRNContact> SRNContacts { get; set; }
        public DbSet<SPGroup> SPGroups { get; set; }
        public DbSet<ChangeRequestStaging> MemberChangeRequests { get; set; }
        public DbSet<SRNFieldUpdateSetting> SRNFieldUpdateSettings { get; set; }
        public DbSet<SRNSaleRequest> SRNSaleRequests { get; set; }
        public DbSet<SRNMergeRequest> SRNMergeRequests { get; set; }
        public DbSet<SRNSplitRequest> SRNSplitRequests { get; set; }
        public DbSet<MemberDocument> MemberDocuments { get; set; }
        public DbSet<SRNSetting> SRNSettings { get; set; }

        //public DbSet<BaseErrorModel> Errors { get; set; }
        public DbSet<CamundaError> CamundaErrors { get; set; }
        public DbSet<ApiError> ApiErrors { get; set; }
        public DbSet<EntityType> EntityTypes { get; set; }
        public DbSet<ALGClientLeader> ALGClientLeaders { get; set; }
        public DbSet<ALGMemberDetails> ALGMemberDetails { get; set; }
        public DbSet<MemberUsers> MemberUsers { get; set; }
        public DbSet<DWException> DWExceptions { get; set; }
        public DbSet<HangfireJob> HangfireJobs { get; set; }
        public DbSet<HangfireLog> HangfireJobLogs { get; set; }
        public DbSet<MemberStatusReason> MemberStatusReasons { get; set; }
        public DbSet<SRNStatusUpdateHistory> SRNStatusUpdateHistory { get; set; }

        public DbSet<RolloutStatus> RolloutStatuses { get; set; }
        public DbSet<EmailQueue> EmailQueues { get; set; }

        public DbSet<MailAttachment> MailAttachments { get; set; }
        public DbSet<MailDeliveryStatus> MailDeliveryStatuses { get; set; }
        public DbSet<MailEnvelop> MailEnvelops { get; set; }
        public DbSet<MailEvent> MailEvents { get; set; }
        public DbSet<MailFlag> MailFlags { get; set; }
        public DbSet<MailgunMessage> MailgunMessages { get; set; }
        public DbSet<MailHeader> MailHeaders { get; set; }
        public DbSet<MailItem> MailItems { get; set; }
        public DbSet<MailPaging> MailPagings { get; set; }
        public DbSet<MailStorage> MailStorages { get; set; }
        public DbSet<MailRecipient> MailRecipients { get; set; }
        public DbSet<MailSetting> MailSettings { get; set; }
        public DbSet<MailColumn> MailColumns { get; set; }
        public DbSet<MemberDomain> MemberDomains { get; set; }
        public DbSet<FreshdeskExcludedDomain> FreshdeskExcludedDomains { get; set; }
        public DbSet<CreditInformationClassification> CreditInformationClassifications { get; set; }
        public DbSet<BureauObscureMapping> BureauObscureMappings { get; set; }
        public DbSet<CamundaErrorRecipient> CamundaErrorRecipients { get; set; }
        public DbSet<DocumentCategory> DocumentCategories { get; set; }
        public DbSet<DocumentStatus> DocumentStatuses { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<DocumentUserAccess> DocumentUserAccess { get; set; }
        public DbSet<MonthlyOSLAReason> MonthlyOSLAReasons { get; set; }
        public DbSet<SRNMonthlyOSLAReason> SRNMonthlyOSLAReasons { get; set; }
        public DbSet<InvoiceRate> InvoiceRates { get; set; }
        public DbSet<MemberInvoice> MemberInvoices { get; set; }
        public DbSet<TermsConditionsDocument> TermsConditionsDocuments { get; set; }
        public DbSet<ReplacementFileSchedule> ReplacementFileSchedule { get; set; }
        public DbSet<ReplacementFileSubmissionReason> ReplacementFileSubmissionReasons { get; set; }
        public DbSet<ReplacementFileSubmissionCategory> ReplacementFileSubmissionCategories { get; set; }
        public DbSet<ReplacementFileSubmission> ReplacementFileSubmissions { get; set; }
        public DbSet<AdhocFileSubmissionReason> AdhocFileSubmissionReason { get; set; }
        public DbSet<AdhocFileSubmissionCategory> AdhocFileSubmissionCategory { get; set; }
        public DbSet<AdhocFileSubmission> AdhocFileSubmissions { get; set; }
        public DbSet<AdhocFileSchedule> AdhocFileSchedules { get; set; }
        public DbSet<vwCalculateMonthlyAlgInvoicing> vwCalculateMonthlyAlgInvoicings { get; set; }
        public DbSet<vwSRNWithUpdateHistory> vwSRNWithUpdateHistories { get; set; }
        public DbSet<vwSRNComplianceContacts> vwSRNComplianceContacts { get; set; }
        public DbSet<vwSRNDataContacts> vwSRNDataContacts { get; set; }
        public DbSet<vwSRNManualAmendmentsContacts> vwSRNManualAmendmentsContacts { get; set; }
        public DbSet<vwSRNDTHContacts> vwSRNDTHContacts { get; set; }
        public DbSet<vwSRNSummaryExtract> vwSRNSummaryExtract { get; set; }
        public DbSet<EmailAuditLog> EmailAuditLogs { get; set; }
        public DbSet<AdhocFileSchedule> AdhocFileSchedule { get; set; }
        public DbSet<BureauLoadStats> BureauLoadStats { get; set; }
        public DbSet<HangfireScheduledJob> HangfireScheduledJobs { get; set; }
        public DbSet<DisqualifiedReason> DisqualifiedReasonTypes { get; set; }
        public DbSet<DisqualifiedReasonMapping> DisqualifiedReasonMapping { get; set; }
        public DbSet<vwMemberDetails> vwMemberDetails { get; set; }
        public DbSet<vwSRNFileExtract> vwSRNFileExtract { get; set; }
        public DbSet<SrnStatusHistory> SrnStatusHistory { get; set; }
        public DbSet<AnnualTurnover> AnnualTurnoverConfig { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder
                .Entity<vwCalculateMonthlyAlgInvoicing>()
                .ToView("vwCalculateMonthlyAlgInvoicing")
                .HasNoKey();

            modelBuilder
                .Entity<vwSRNWithUpdateHistory>()
                .ToView("vwSRNWithUpdateHistory")
                .HasNoKey();

            modelBuilder
                .Entity<vwMemberDetails>()
                .ToView("vwMemberDetails")
                .HasNoKey();

            modelBuilder.Entity<MemberContact>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.Contacts)
                .HasForeignKey(opt => opt.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<TradingName>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.TradingNames)
                .HasForeignKey(opt => opt.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Member>()
                .HasMany(opt => opt.Users)
                .WithOne(opt => opt.Member)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Member>()
                .HasOne(opt => opt.StakeholderManager)
                .WithMany(opt => opt.MembersIManage)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SRN>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.SRNs)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SRNContact>()
                .HasOne(opt => opt.SRN)
                .WithMany(opt => opt.Contacts)
                .HasForeignKey(opt => opt.SRNId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SPGroup>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.SPGroups)
                .HasForeignKey(opt => opt.MemberId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SPGroup>()
                .HasIndex(p => new { p.MemberId, p.SPNumber })
                .IsUnique();

            modelBuilder.Entity<SRN>()
                .HasOne(opt => opt.SPGroup)
                .WithMany(opt => opt.SRNs)
                .HasForeignKey(opt => opt.SPGroupId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BranchLocation>()
                .HasOne(opt => opt.SRN)
                .WithMany(opt => opt.BranchLocations)
                .HasForeignKey(opt => opt.SRNId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ChangeRequestStaging>()
                .HasOne(opt => opt.User)
                .WithMany(opt => opt.MemberChangeRequests)
                .HasForeignKey(opt => opt.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<MemberDocument>()
                .HasOne(opt => opt.Member)
                .WithOne(opt => opt.MemberDocument)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SRN>()
                .HasIndex(opt => opt.SRNNumber)
                .IsUnique();

            modelBuilder.Entity<SRN>()
                .HasOne(opt => opt.ALGLeader)
                .WithMany(opt => opt.ClientSRNs)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Member>()
                .HasIndex(p => p.RegisteredNumber).IsUnique();

            modelBuilder.Entity<Member>()
                .HasIndex(p => p.IdNumber).IsUnique();

            modelBuilder.Entity<User>()
                .HasMany(opt => opt.Members)
                .WithOne(opt => opt.User)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasIndex(p => p.Email).IsUnique();

            modelBuilder.Entity<SRNStatus>()
               .HasMany(opt => opt.SRNStatusReasons)
               .WithOne(opt => opt.SRNStatus)
               .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SRNStatusReason>()
               .HasMany(opt => opt.SRNStatuses)
               .WithOne(opt => opt.SRNStatusReason)
               .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SRNStatusUpdateHistory>()
                .HasOne(opt => opt.SRN)
                .WithMany(opt => opt.SRNStatusUpdates)
                .HasForeignKey(opt => opt.SRNId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ALGClientLeader>()
                .HasOne(opt => opt.Client)
                .WithMany(opt => opt.Leaders)
                .HasForeignKey(opt => opt.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ALGClientLeader>()
                .HasOne(opt => opt.Leader)
                .WithMany(opt => opt.Clients)
                .HasForeignKey(opt => opt.LeaderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Member>()
                .HasIndex(p => p.VatNumber).IsUnique();
            modelBuilder.Entity<MemberDomain>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.Domains)
                .HasForeignKey(opt => opt.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<MemberDomain>()
                .HasIndex(p => p.Name).IsUnique();

            modelBuilder.Entity<FreshdeskExcludedDomain>()
                .HasIndex(p => p.Name).IsUnique();

            modelBuilder.Entity<BureauObscureMapping>()
                .HasIndex(p => p.ObscureName).IsUnique();

            modelBuilder.Entity<Document>()
                .HasIndex(p => p.BlobName).IsUnique();

            modelBuilder.Entity<DocumentUserAccess>()
                .HasOne(opt => opt.Document)
                .WithMany(opt => opt.AccessedByUsers)
                .HasForeignKey(opt => opt.DocumentId);

            modelBuilder.Entity<DocumentUserAccess>()
                .HasOne(opt => opt.User)
                .WithMany(opt => opt.AccessedDocuments)
                .HasForeignKey(opt => opt.UserId);

            modelBuilder.Entity<MemberInvoice>()
                .HasOne(opt => opt.Member)
                .WithMany(opt => opt.Invoices)
                .HasForeignKey(opt => opt.MemberId)
                .OnDelete(DeleteBehavior.Restrict);

            CreateMailgunTables(modelBuilder);
            CreateHangfireScheduledJobs(modelBuilder);
        }

        public void CreateMailgunTables(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<MailItem>()
                .HasOne(opt => opt.MailEvent)
                .WithMany(opt => opt.MailItems)
                .HasForeignKey(opt => opt.MailEventId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<MailRecipient>()
                .HasOne(opt => opt.MailSetting)
                .WithMany(opt => opt.MailRecipients)
                .HasForeignKey(opt => opt.MailSettingId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<MailColumn>()
                .HasOne(opt => opt.MailSetting)
                .WithMany(opt => opt.MailColumns)
                .HasForeignKey(opt => opt.MailSettingId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<MailEvent>().ToTable("MailEvents", "Mailgun");

            modelBuilder.Entity<MailAttachment>().ToTable("MailAttachments", "Mailgun");
            modelBuilder.Entity<MailDeliveryStatus>().ToTable("MailDeliveryStatuses", "Mailgun");
            modelBuilder.Entity<MailDeliveryStatus>().ToTable("MailDeliveryStatuses", "Mailgun");
            modelBuilder.Entity<MailEnvelop>().ToTable("MailEnvelops", "Mailgun");
            modelBuilder.Entity<MailFlag>().ToTable("MailFlags", "Mailgun");
            modelBuilder.Entity<MailHeader>().ToTable("MailHeaders", "Mailgun");
            modelBuilder.Entity<MailItem>().ToTable("MailItems", "Mailgun");
            modelBuilder.Entity<MailgunMessage>().ToTable("MailgunMessages", "Mailgun");
            modelBuilder.Entity<MailPaging>().ToTable("MailPagings", "Mailgun");
            modelBuilder.Entity<MailStorage>().ToTable("MailStorages", "Mailgun");
            modelBuilder.Entity<MailRecipient>().ToTable("MailRecipients", "Mailgun");
            modelBuilder.Entity<MailSetting>().ToTable("MailSettings", "Mailgun");
            modelBuilder.Entity<MailColumn>().ToTable("MailColumns", "Mailgun");
        }

        private void CreateHangfireScheduledJobs(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<HangfireScheduledJob>().HasData(
                new HangfireScheduledJob()
                {
                    JobName = "Cleanup-API-Errors",
                    MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                    MethodName = "CleanupApiErrors",
                    CronString = "0 21 * * * *" // Daily at 9pm
                },
                new HangfireScheduledJob()
                {
                    JobName = "Cleanup-Camunda-Errors",
                    MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                    MethodName = "CleanupCamundaErrors",
                    CronString = "0 21 * * * *" // Daily at 9pm
                },
                new HangfireScheduledJob()
                {
                    JobName = "Create-DW-Camunda-Exception-Tasks",
                    MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                    MethodName = "CreateCamundaTaskForNewDWExceptions",
                    CronString = "15 * * * * *" // Every 15 minutes
                },
                new HangfireScheduledJob()
                {
                    JobName = "Sync-Freshdesk-Companies",
                    MethodClass = "Sacrra.Membership.Freshdesk.Services.CompanyService",
                    MethodName = "CreateCompanies",
                    CronString = "0 18 * * * *" // Daily at 6pm
                },
                new HangfireScheduledJob()
                {
                    JobName = "Sync-Freshdesk-Contacts",
                    MethodClass = "Sacrra.Membership.Freshdesk.Services.CompanyService",
                    MethodName = "CreateContacts",
                    CronString = "0 19 * * * *" // Daily at 7pm
                }


            );
        }
    }
}
