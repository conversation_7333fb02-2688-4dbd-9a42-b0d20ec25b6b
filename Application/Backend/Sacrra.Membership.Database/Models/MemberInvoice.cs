using System;

namespace Sacrra.Membership.Database.Models
{
    public class MemberInvoice
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public int MemberId { get; set; }
        public int InvoiceRateId { get; set; }
        public virtual Member Member { get; set; }
        public virtual InvoiceRate InvoiceRate { get; set; }
        public double Amount { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime BillingDate { get; set; }
        public int ClientsCount { get; set; }
        public string? PastelOrAccountNumber { get; set; }
        public DateTime? DateInvoiced { get; set; }
        public string? InvoiceNumber { get; set; }
        public double? AmountInvoiced { get; set; }
        public string? CommentOrRefrence { get; set; }
        public double? AmountPaid { get; set; }
        public DateTime? DatePaid { get; set; }
        public double? Balance { get; set; }
        public string? CreditNoteNumber { get; set; }
        public double? CreditNoteAmount { get; set; }
        public DateTime? CreditNoteIssueDate { get; set; }
    }
}
