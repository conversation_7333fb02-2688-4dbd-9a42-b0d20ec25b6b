using Newtonsoft.Json;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.publicShared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sacrra.Membership.Database.Models
{
    public class Member : MemberShared
    {
        public int Id { get; set; }

        [Display(Name = "Membership Type")]
        public MembershipTypes MembershipTypeId { get; set; }

        [Display(Name = "Primary Bureau")]
        [ForeignKey("PrimaryBureau")]
        public int? PrimaryBureauId { get; set; }

        [Display(Name = "Secondary Bureau")]
        [ForeignKey("SecondaryBureau")]
        public int? SecondaryBureauId { get; set; }

        [Display(Name = "NCR Category")]
        public string NcrCategory { get; set; }

        [Display(Name = "Principle Debt Range")]
        public PrincipleDebtRanges? PrincipleDebtRangeId { get; set; }

        [Display(Name = "Industry Classification")]
        public IndustryClassifications? IndustryClassificationId { get; set; }

        [Display(Name = "NCR Reporting Primary Business Classification")]
        public NcrReportingPrimaryBusinessClassifications? NcrReportingPrimaryBusinessClassificationId { get; set; }

        [Display(Name = "Application Status")]
        public ApplicationStatuses ApplicationStatusId { get; set; }

        [Display(Name = "Secondary Bureau")]
        [JsonIgnore]
        public virtual Member SecondaryBureau { get; set; }

        [Display(Name = "Primary Bureau")]
        [JsonIgnore]
        public virtual Member PrimaryBureau { get; set; }

        public virtual ICollection<TradingName> TradingNames { get; set; }
        [JsonIgnore]
        public virtual ICollection<MemberUsers> Users { get; set; } = new List<MemberUsers>();
        public virtual ICollection<MemberContact> Contacts { get; set; } = new List<MemberContact>();

        [Display(Name = "Stakeholder Manager")]
        public int? StakeholderManagerId { get; set; }
        [NotMapped]
        public virtual User StakeholderManager { get; set; }
        [JsonIgnore]
        public virtual ICollection<SRN> SRNs { get; set; } = new List<SRN>();
        public virtual ICollection<SPGroup> SPGroups { get; set; } = new List<SPGroup>();
        public virtual ChangeRequestStaging ChangeRequest { get; set; }
        public virtual MemberDocument MemberDocument { get; set; }
        public virtual ICollection<SRN> ClientSRNs { get; set; } = new List<SRN>();
        [NotMapped]
        public int TotalActiveSRNs { get; set; }
        [NotMapped]
        public int TotalSRNs { get; set; }

        [Display(Name = "Member Status Reason")]
        public int? MemberStatusReasonId { get; set; }

        public virtual ICollection<ALGClientLeader> Clients { get; set; } = new List<ALGClientLeader>();
        public virtual ICollection<ALGClientLeader> Leaders { get; set; } = new List<ALGClientLeader>();

        public int? ALGLeaderBillingId { get; set; }
        public virtual ALGClientLeader ALGLeaderBilling { get; set; }

        public virtual MemberStatusReason MemberStatusReason { get; set; }

        [Display(Name = "Date Activated")]
        public DateTime? DateActivated { get; set; }

        [Display(Name = "DateCancelled")]
        public DateTime? DateCancelled { get; set; }

        [Display(Name = "DateCreated")]
        public DateTime? DateCreated { get; set; }
        public virtual ICollection<MemberDomain> Domains { get; set; } = new List<MemberDomain>();
        public virtual ICollection<MemberInvoice> Invoices { get; set; } = new List<MemberInvoice>();
        public int? DisqualifiedReasonMappingId { get; set; }

    }
}
