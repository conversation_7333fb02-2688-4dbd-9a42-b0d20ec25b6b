using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AnnualTurnoverConfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AnnualTurnoverConfig",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                    
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnnualTurnoverConfig", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AnnualTurnoverConfig",
                columns: new[] { "Id", "Name", },
                values: new object[,]
                {
                    { 1, "Less than R1 000" },
                    { 2, "R1 000 – R9 999" },
                    { 3, "R10 000 – R49 999" },
                    { 4, "R50 000 – R99 999" },
                    { 5, "R100 000 – R499 999" },
                    { 6, "R500 000 – R999 999" },
                    { 7, "R1 million – R4.9 million" },
                    { 8, "R5 million – R9.9 million" },
                    { 9, "R10 million – R49.9 million" },
                    { 10, "R50 million – R99.9 million" },
                    { 11, "R100 million – R499.9 million" },
                    { 12, "R500 million – R999.9 million" },
                    { 13, "R1 billion and above" }
                     });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AnnualTurnoverConfig");
        }
    }
}
