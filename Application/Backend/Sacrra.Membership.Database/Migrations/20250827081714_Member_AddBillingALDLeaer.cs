using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class Member_AddBillingALDLeaer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ALGLeaderBillingId",
                table: "Members",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Members_ALGLeaderBillingId",
                table: "Members",
                column: "ALGLeaderBillingId");

            migrationBuilder.AddForeignKey(
                name: "FK_Members_ALGClientLeaders_ALGLeaderBillingId",
                table: "Members",
                column: "ALGLeaderBillingId",
                principalTable: "ALGClientLeaders",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Members_ALGClientLeaders_ALGLeaderBillingId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_ALGLeaderBillingId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "ALGLeaderBillingId",
                table: "Members");
        }
    }
}
