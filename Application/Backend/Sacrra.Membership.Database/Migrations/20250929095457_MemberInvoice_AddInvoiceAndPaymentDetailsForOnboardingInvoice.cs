using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class MemberInvoice_AddInvoiceAndPaymentDetailsForOnboardingInvoice : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "AmountInvoiced",
                table: "MemberInvoices",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "AmountPaid",
                table: "MemberInvoices",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Balance",
                table: "MemberInvoices",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CommentOrRefrence",
                table: "MemberInvoices",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CreditNoteAmount",
                table: "MemberInvoices",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreditNoteIssueDate",
                table: "MemberInvoices",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreditNoteNumber",
                table: "MemberInvoices",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateInvoiced",
                table: "MemberInvoices",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DatePaid",
                table: "MemberInvoices",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InvoiceNumber",
                table: "MemberInvoices",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PastelOrAccountNumber",
                table: "MemberInvoices",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ALGLeaderBillingId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "AmountInvoiced",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "AmountPaid",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "Balance",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "CommentOrRefrence",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "CreditNoteAmount",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "CreditNoteIssueDate",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "CreditNoteNumber",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "DateInvoiced",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "DatePaid",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "InvoiceNumber",
                table: "MemberInvoices");

            migrationBuilder.DropColumn(
                name: "PastelOrAccountNumber",
                table: "MemberInvoices");
        }
    }
}
