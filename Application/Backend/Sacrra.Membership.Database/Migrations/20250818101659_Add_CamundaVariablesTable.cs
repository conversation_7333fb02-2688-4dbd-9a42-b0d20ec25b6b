using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class Add_CamundaVariablesTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.CreateTable(
                name: "CamundaVariables",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    processInstanceId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    variablesJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CamundaVariables", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CamundaVariables");
        }
    }
}
