using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdateExtractViewsToIncludeMemberId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"ALTER VIEW [dbo].[vwSRNSummaryExtract] 
			AS
				WITH branchlocations
					AS
					(
						SELECT 
							[SRNId]
							,[BranchLocations] = STRING_AGG(bra.[Name],',')
						FROM [dbo].[BranchLocations] bra
						GROUP BY [SRNId]
					)
					SELECT
						[CompanyRegistrationNumber] = ISNULL(mem.[RegisteredNumber], mem.[IdNumber])
						,[MemberName] = mem.[RegisteredName]
						,[SRN] = srn.[SRNNumber]
						,[MemberId] = srn.[MemberId]
						,[SPNumber] = spg.[SPNumber]
						,[SRNDisplayName] = srn.[TradingName]
						,[CreditInformationClassification] = cla.[Name]
						,[PortfolioManager] = shm.[FirstName] + ' ' + shm.[LastName]
						,[AccountType] = acc.[Name]
						,[NCRReportingAccountTypeClassification] = ncr.[Name]
						,[BillingCycleDay] = srn.[BillingCycleDay]
						,[SRNALGLeader] = alg.[RegisteredName]
						,[LoanManagementSystemVendor] = loa.[Name]
						,[BranchLocations] = bra.[BranchLocations]
						,[NumberMonthlyRecords] = 1
						,[SRNCreationDate] = srn.[CreationDate]
						,[DataContactName] = dat.[FirstName] + ' ' + dat.[Surname]
						,[DataContactOfficeNumber] = dat.[OfficeTelNumber]
						,[DataContactCellNumber] = dat.[CellNumber]
						,[DataContactEmail] = dat.[Email]
						,[DataContactJobTitle] = dat.[JobTitle]
						,[ManualAmendmentsName] = man.[FirstName] + ' ' + man.[Surname]
						,[ManualAmendmentsOfficeNumber] = man.[OfficeTelNumber]
						,[ManualAmendmentsCellNumber] = man.[CellNumber]
						,[ManualAmendmentsEmail] = man.[Email]
						,[ManualAmendmentsJobTitle] = man.[JobTitle]
						,[Status] = srnStatuses.[Name]
						,[StatusDate] = srnSH.[StatusDate]
						,[StatusReason] = srnStatusReasons.[Name]
						,[StatusComment] = srnSH.[StatusComment]
						,[LastSubmissionDate] = srnSH.[LastSubmissionDate]
						,[BureauClosureDate] = srnSH.[BureauClosureDate]
						,[BureauClosureInstruction] = srnSH.[BureauClosureInstruction]
						,[ThirdPartyVendor] = sv.[Name]
					FROM [dbo].[SRNs] srn

					INNER JOIN [dbo].[Members] mem
					ON srn.[MemberId] = mem.[Id]

					INNER JOIN [dbo].[Users] shm
					ON mem.[StakeholderManagerId] = shm.[Id]

					INNER JOIN [dbo].[AccountTypes] acc
					ON srn.[AccountTypeId] = acc.[Id]

					INNER JOIN [dbo].[NCRReportingAccountTypeClassifications] ncr
					ON srn.[NCRReportingAccountTypeClassificationId] = ncr.[Id]

					INNER JOIN [dbo].[SRNContacts] dat
					ON srn.[Id] = dat.[SRNId]
					AND dat.[ContactTypeId] = 5

					INNER JOIN [dbo].[SRNContacts] man
					ON srn.[Id] = man.[SRNId]
					AND man.[ContactTypeId] = 6
									
					LEFT OUTER JOIN [dbo].[SRNStatusHistory] srnSH
					ON srnSH.[SRNId] = srn.[Id]

					LEFT OUTER JOIN [dbo].[SRNStatuses] srnStatuses
					ON srnStatuses.[Id] = srnSH.[StatusId]

					LEFT OUTER JOIN [dbo].[SRNStatusReasons] srnStatusReasons
					ON srnStatuses.[Id] = srnSH.[StatusReasonId]

					LEFT OUTER JOIN [dbo].[CreditInformationClassifications] cla
					ON srn.[CreditInformationClassificationId] = cla.[Id]

					LEFT OUTER JOIN [dbo].[SPGroups] spg
					ON srn.[SPGroupId] = spg.[Id]

					LEFT OUTER JOIN [dbo].[Members] alg
					ON srn.[ALGLeaderId] = alg.[Id]

					LEFT OUTER JOIN [dbo].[LoanManagementSystemVendors] loa
					ON srn.[LoanManagementSystemVendorId] = loa.[Id]

					LEFT OUTER JOIN branchlocations bra
					ON srn.[Id] = bra.[SRNId]

					LEFT OUTER JOIN SoftwareVendors sv
					ON srn.[SoftwareVendorId] = sv.[Id]
			GO");

            migrationBuilder.Sql(@"ALTER VIEW [dbo].[vwSRNFileExtract] 
			AS
			WITH history
			AS
			(
				SELECT
					[Id]
					,[MaxId] = MAX([Id]) OVER (PARTITION BY [SRNId], [FileType])
					,[DailyFileTestEndDate]
					,[DailyFileGoLiveDate]
					,[MonthlyFileTestEndDate]
					,[MonthlyFileGoLiveDate]
					,[BureauInstruction]
					,[LastSubmissionDate]
					,[DateCreated]
					,[SRNId]
					,[IsComple]
					,[DateCompleted]
					,[FileType]
					,[SRNStatusId]
					,[SRNStatusReasonId]
					,[SignoffDate]
					,[SRNFileTestingStatusReason]
				FROM [dbo].[SRNStatusUpdateHistory]
			)
			SELECT
				srn.[SRNNumber]
				,srn.[MemberId]
				,[SRNDisplayName] = srn.[TradingName]
				,files.[Id]
				,[CurrentSRNStatus] = sta.[Name]
				,[SRNCreationDate] = srn.[CreationDate]
				,[SRNFileType] = CASE srn.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' WHEN 3 THEN 'Daily & Monthly' END 
				,files.[IsLatestRecord]
				,files.[FileType]
				,files.[FileStatus]
				,files.[FileStatusDate]
				,files.[TestFileStatusReason]
				,files.[PlannedTestEndDate]
				,files.[FileTestSignoffDate]
				,files.[PlannedGoLiveDate]
				,files.[ActualGoLiveDate]
				,files.[TestingSkipped]
			FROM
			(
			SELECT
					tst.[Id]
					,[SRNId]
				,[FileType] = CASE tst.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
				,[FileStatus] = 'Test'
				,[IsLatestRecord] = CASE WHEN tst.[MaxId] = tst.[Id] AND [IsComple] = 0 THEN 'Yes' ELSE 'No' END
				,[FileStatusDate] = tst.[DateCreated]
				,[TestFileStatusReason] = rea.[Name]
				,[PlannedTestEndDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileTestEndDate] WHEN 2 THEN tst.[MonthlyFileTestEndDate] END
				,[FileTestSignoffDate] = tst.[SignoffDate]
				,[PlannedGoLiveDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileGoLiveDate] WHEN 2 THEN tst.[MonthlyFileGoLiveDate] END
				,[ActualGoLiveDate] = tst.[DateCompleted]
				,[TestingSkipped] = CASE WHEN FORMAT(tst.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(tst.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
			FROM history tst
			LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
			ON tst.[SRNStatusReasonId] = rea.[Id]
			WHERE tst.[FileType] IN (1,2)
			UNION ALL
			SELECT
					liv.[Id]
					,[SRNId]
				,[FileType] = CASE liv.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
				,[FileStatus] = 'Live'
				,[IsLatestRecord] = CASE WHEN liv.[MaxId] = liv.[Id] AND [IsComple] = 1 THEN 'Yes' ELSE 'No' END
				,[FileStatusDate] = liv.[DateCompleted]
				,[TestFileStatusReason] = NULL
				,[PlannedTestEndDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileTestEndDate] WHEN 2 THEN liv.[MonthlyFileTestEndDate] END
				,[FileTestSignoffDate] = liv.[SignoffDate]
				,[PlannedGoLiveDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileGoLiveDate] WHEN 2 THEN liv.[MonthlyFileGoLiveDate] END
				,[ActualGoLiveDate] = liv.[DateCompleted]
				,[TestingSkipped] = CASE WHEN FORMAT(liv.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(liv.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
			FROM history liv
			LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
			ON liv.[SRNStatusReasonId] = rea.[Id]
			WHERE liv.[IsComple] = 1
			AND liv.[FileType] IN (1,2)
			) files
			INNER JOIN [dbo].[SRNs] srn
			ON files.[SRNId] = srn.[Id]
			INNER JOIN [dbo].[SRNStatuses] sta
			ON srn.[SRNStatusId] = sta.[Id]
			GO");

            migrationBuilder.Sql(@"ALTER VIEW [dbo].[vwSRNContactsForExtract] 
			AS
				WITH datacontact AS 
				(
					SELECT SRNId
					,[FirstName]
					,[Surname]
					,[JobTitle]
					,[OfficeTelNumber]
					,[CellNumber]
					,[Email]
					FROM SRNContacts
					WHERE ContactTypeId = 5
				),
				manualamendmentcontact AS 
				(
					SELECT SRNId
					,[FirstName]
					,[Surname]
					,[JobTitle]
					,[OfficeTelNumber]
					,[CellNumber]
					,[Email]
					FROM SRNContacts
					WHERE ContactTypeId = 6
				),
				compliancecontact AS 
				(
					SELECT SRNId
					,[FirstName]
					,[Surname]
					,[JobTitle]
					,[OfficeTelNumber]
					,[CellNumber]
					,[Email]
					FROM SRNContacts
					WHERE ContactTypeId = 4
				),
				dthcontact AS 
				(
					SELECT SRNId
					,[FirstName]
					,[Surname]
					,[JobTitle]
					,[OfficeTelNumber]
					,[CellNumber]
					,[Email]
					FROM SRNContacts
					WHERE ContactTypeId = 7
				)
				SELECT
					srn.[SRNNumber] AS SRNNumber
					,srn.[MemberId] AS MemberId
					,dc.[FirstName] AS DataContactFirstName
					,dc.[Surname] AS DataContactSurname
					,dc.[JobTitle] AS DataContactJobTitle
					,dc.[OfficeTelNumber] AS DataContactOfficeNumber
					,dc.[CellNumber] AS DataContactCellNumber
					,dc.[Email] AS DataContactEmail

					,ma.[FirstName] AS ManualAmendmentsContactFirstName
					,ma.[Surname] AS ManualAmendmentsContactSurname
					,ma.[JobTitle] AS ManualAmendmentsContactJobTitle
					,ma.[OfficeTelNumber] AS ManualAmendmentsContactOfficeNumber
					,ma.[CellNumber] AS ManualAmendmentsContactCellNumber
					,ma.[Email] AS ManualAmendmentsContactEmail

					,co.[FirstName] AS ComplianceContactFirstName
					,co.[Surname] AS ComplianceContactSurname
					,co.[JobTitle] AS ComplianceContactJobTitle
					,co.[OfficeTelNumber] AS ComplianceContactOfficeNumber
					,co.[CellNumber] AS ComplianceContactCellNumber
					,co.[Email] AS ComplianceContactEmail

					,dthc.[FirstName] AS DTHContactFirstName
					,dthc.[Surname] AS DTHContactSurname
					,dthc.[JobTitle] AS DTHContactJobTitle
					,dthc.[OfficeTelNumber] AS DTHContactOfficeNumber
					,dthc.[CellNumber] AS DTHContactCellNumber
					,dthc.[Email] AS DTHContactEmail

					,cic.[Name] AS CreditInformationClassification
				FROM datacontact dc

				INNER JOIN manualamendmentcontact ma
				ON dc.[SRNId] = ma.[SRNId]

				INNER JOIN compliancecontact co
				ON dc.[SRNId] = co.[SRNId]

				INNER JOIN dthcontact dthc
				ON dc.[SRNId] = dthc.[SRNId]

				INNER JOIN SRNs srn
				ON srn.[Id] = dc.[SRNId]

				INNER JOIN CreditInformationClassifications cic
				ON srn.CreditInformationClassificationId = cic.[Id]
			GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNSummaryExtract];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNFileExtract];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNContactsForExtract];");
        }
    }
}
