<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoFixture" Version="4.18.1" />
        <PackageReference Include="coverlet.collector" Version="6.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.36" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="Moq" Version="4.18.1" />
        <PackageReference Include="NUnit" Version="3.14.0" />
        <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
        <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    </ItemGroup>

	<ItemGroup>
		<Using Include="NUnit.Framework" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Sacrra.Membership.Api\Sacrra.Membership.Api.csproj" />
	</ItemGroup>

    <ItemGroup>
      <None Remove="NewSRNTests\data\*.json" />
      <Content Include="NewSRNTests\data\*.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>
	
	  <ItemGroup>
      <None Remove="EditSRNTests\Data\*.json" />
      <Content Include="EditSRNTests\Data\*.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>
	
	  <ItemGroup>
	    <Folder Include="AdhocTests\Data\" />
	  </ItemGroup>


	<ItemGroup>
		<None Remove="ReplacementFileTests\Data\*.json" />
		<Content Include="ReplacementFileTests\Data\*.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>


</Project>
