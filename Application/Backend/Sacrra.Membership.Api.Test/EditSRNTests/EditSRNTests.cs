using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Camunda.CamundaAgents;
using Sacrra.Membership.Database;
using Sacrra.Membership.Business.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Sacrra.Membership.Database.Models;
using Microsoft.EntityFrameworkCore;

namespace Sacrra.Membership.Api.Test.EditSRNTests
{
    public class EditSRNTests : BaseIntegrationTest
    {
        private SRNService _srnService;
        private SrnUpdateDetailsCamundaAgent _srnUpdateDetailsCamundaAgent;
        private CamundaClient _camundaClient;
        private readonly string _editDailySrn = "./EditSRNTests/Data/EditDailySRN.json";
        private readonly string _editMonthlySrn = "./EditSRNTests/Data/EditMonthlySRN.json";
        private readonly string _editDailyAndMonthlySrn = $"./EditSRNTests/Data/EditDailyAndMonthlySRN.json";
        private readonly string _editSRNContacts = $"./EditSRNTests/Data/EditContacts.json";
        private readonly string _editSRNContactsMissingContact = $"./EditSRNTests/Data/EditContactsMissingContact.json";
        private readonly string _requestStatusUpdateSrn = $"./EditSRNTests/Data/RequestStatusUpdateSRN.json";

        [SetUp]
        public void Setup()
        {
            BaseSetup();

            _srnService = GetService<SRNService>(_scope);

            var configSettigs = _scope.ServiceProvider.GetService<IOptions<ConfigSettings>>();
            var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri(configSettigs!.Value.CamundaBaseAddress);

            _camundaClient = CamundaClient.Create(httpClient);
            _srnUpdateDetailsCamundaAgent = new SrnUpdateDetailsCamundaAgent(_camundaClient);
        }

        [Test]
        [Ignore("Comment out for Scratchpad")]
        public void Scratchpad()
        {
            //var srnId = 409587;
            // MakeItLiveOnDTH(srnId);
        }

        [Test, Description("Test editing for daily file")]
        public void TestUpdateSrnDetails_Daily()
        {
            TestUpdateDetails(_editDailySrn);
        }

        [Test, Description("Test editing for monthly file")]
        public void TestUpdateSrnDetails_Monthly()
        {
            TestUpdateDetails(_editMonthlySrn);
        }

        [Test, Description("Test editing for monthly file")]
        public void TestUpdateSrnDetails_DailyAndMonthly()
        {
            TestUpdateDetails(_editDailyAndMonthlySrn);
        }

        [Test, Description("Test updating SRN contact")]
        public async Task TestUpdateSRNContacts()
        {
            var user = GetUserWithStakeholderManagerRole();
            await TestUpdateSRNContacts(_editSRNContacts, user);
        }

        [Test, Description("Test updating SRN contact with missing requiredcontact")]
        public void TestUpdateSRNContacts_MissingContact()
        {
            var user = GetUserWithStakeholderManagerRole();
            TestUpdateSRNContacts_MissingContact(_editSRNContactsMissingContact, user);
        }

        private void TestUpdateDetails(string jsonFilePath)
        {
            // Get SHM user
            var user = GetUserWithStakeholderManagerRole();


            var empty_date = new DateTime();

            // Then Change Details
            var srnUpdateInputDTO = JsonHelper.DeserializeFile<SRNUpdateInputDTO>(jsonFilePath);
            srnUpdateInputDTO.SRNDisplayName = "Test-SRN-Update-Details" + Guid.NewGuid();
            srnUpdateInputDTO.Id = 408654;

            var testSRN = _srnService.UpdateSRN(srnUpdateInputDTO, user).Result;

            ProcessExternalTask(_srnUpdateDetailsCamundaAgent, "apply-srn-details-update");

            var dbContext = GetService<AppDbContext>(_scope);
            var dbSrn = dbContext.SRNs.FirstOrDefault(s => s.Id == srnUpdateInputDTO.Id);

            Assert.That(dbSrn, Is.Not.Null);
            Assert.That(dbSrn.SRNNumber, Is.Not.Null);
            Assert.That(dbSrn.TradingName, Is.EqualTo(srnUpdateInputDTO.SRNDisplayName));
            Assert.That(dbSrn.FileType, Is.EqualTo(srnUpdateInputDTO.FileType));

        }

        private async Task TestUpdateSRNContacts(string jsonFilePath, User user)
        {
            var dbContext = GetService<AppDbContext>(_scope);
            var dbSrn = dbContext.SRNs.AsNoTracking().Include(s => s.Contacts).Where(s => s.TradingName.StartsWith("TestSRN") && s.Contacts.First(c => c.ContactTypeId == 4).FirstName != "Test First Name").FirstOrDefault();

            Assert.That(dbSrn, Is.Not.Null);

            var srnUpdateInputDTO = _mapper.Map<SRNUpdateInputDTO>(dbSrn);
            var contactUpdateDTOList = JsonHelper.DeserializeFile<List<ContactUpdateDTO>>(jsonFilePath);
            contactUpdateDTOList.ForEach(x =>
            {
                x.SRNId = dbSrn.Id;
                //If there is a contact with the same type then update it (To make sure that updating and creating contacts are tested)
                var existingContact = dbSrn.Contacts.FirstOrDefault(c => c.ContactTypeId == x.ContactTypeId);
                if (existingContact != null)
                {
                    x.Id = existingContact.Id;
                }
            });
            
            srnUpdateInputDTO.Contacts = contactUpdateDTOList;
            await _srnService.UpdateSRNContact(srnUpdateInputDTO, user);

            var updatedSrn = dbContext.SRNs.AsNoTracking().Include(s => s.Contacts).FirstOrDefault(s => s.Id == srnUpdateInputDTO.Id);
            Assert.That(updatedSrn, Is.Not.Null);
            Assert.That(updatedSrn.Contacts.Count, Is.EqualTo(srnUpdateInputDTO.Contacts.Count));
            var expectedValues = new
            {
                FirstName = "Test First Name",
                Surname = "Test Last Name",
                Email = "<EMAIL>",
                CellNumber = "1234567890",
                OfficeTelNumber = "Test Cellphone Number",
                JobTitle = "Test Designation"
            };

            foreach (var contactTypeId in new[] { 1, 2, 3 })
            {
                var contact = updatedSrn.Contacts.First(c => c.ContactTypeId == contactTypeId);
                Assert.That(contact.FirstName, Is.EqualTo(expectedValues.FirstName));
                Assert.That(contact.Surname, Is.EqualTo(expectedValues.Surname));
                Assert.That(contact.Email, Is.EqualTo(expectedValues.Email));
                Assert.That(contact.CellNumber, Is.EqualTo(expectedValues.CellNumber));
                Assert.That(contact.OfficeTelNumber, Is.EqualTo(expectedValues.OfficeTelNumber));
                Assert.That(contact.JobTitle, Is.EqualTo(expectedValues.JobTitle));
            }

        }

        private void TestUpdateSRNContacts_MissingContact(string jsonFilePath, User user)
        {
            var dbContext = GetService<AppDbContext>(_scope);
            var dbSrn = dbContext.SRNs.AsNoTracking().Include(s => s.Contacts).Where(s => s.TradingName.StartsWith("TestSRN") && s.Contacts.First(c => c.ContactTypeId == 4).FirstName != "Test First Name").FirstOrDefault();

            Assert.That(dbSrn, Is.Not.Null);

            var srnUpdateInputDTO = _mapper.Map<SRNUpdateInputDTO>(dbSrn);
            var contactUpdateDTOList = JsonHelper.DeserializeFile<List<ContactUpdateDTO>>(jsonFilePath);
            contactUpdateDTOList.ForEach(x => x.SRNId = dbSrn.Id);
            srnUpdateInputDTO.Contacts = contactUpdateDTOList;
            var ex = Assert.ThrowsAsync<Exception>(() => _srnService.UpdateSRNContact(srnUpdateInputDTO, user));
            Assert.That(ex.Message, Is.EqualTo("Financial Contact is required."));
        }             
        
        [Test]
        public async Task TestUpdateSRNStatus(){
            await TestUpdateSRNStatus(_requestStatusUpdateSrn);
        }

        private async Task TestUpdateSRNStatus(string jsonFilePath)
        {
            var user = GetUserWithStakeholderManagerRole();
            var dbContext = _factory.Services.GetService<AppDbContext>();
            if (dbContext == null)
                throw new Exception("Database context is null");
            var existingSrn = dbContext.SRNs.FirstOrDefault(s => s.TradingName.StartsWith("TestSRN") && s.Comments == null);
            if (existingSrn == null)
                throw new Exception("SRN not found");

            var srnUpdateInputDTO = JsonHelper.DeserializeFile<SRNStatusUpdateRequestDTO>(_requestStatusUpdateSrn);
            srnUpdateInputDTO.Id = existingSrn.Id;
            await _srnService.RequestSRNStatusUpdate(new List<SRNStatusUpdateRequestDTO> { srnUpdateInputDTO }, user);
            
            var editedSRN = dbContext.SRNs.AsNoTracking().FirstOrDefault(s => s.Id == srnUpdateInputDTO.Id);

            Assert.That(editedSRN, Is.Not.Null);
            Assert.That(editedSRN.SRNStatusReasonId, Is.EqualTo(1));
            Assert.That(editedSRN.Comments, Is.EqualTo("Testing status update request"));
        }

        private void ProcessExternalTask(ICamundaAgent camundaAgent, string topicName)
        {
            var workerId = "NUnit" + Guid.NewGuid();
            var externalTaskList = CamundaTaskHelper.FetchAndLockExternalTasks(_camundaClient, topicName, workerId);
            foreach (var externalTask in externalTaskList)
            {
                var completeExternalTask = new CompleteExternalTask() { WorkerId = workerId };
                camundaAgent.Process(topicName, externalTask, completeExternalTask, _scope).Wait();
            }
        }
    }
}
