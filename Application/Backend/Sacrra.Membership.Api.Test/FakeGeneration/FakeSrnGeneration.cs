using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.MemberContact;

namespace Sacrra.Membership.Api.Test.FakeGeneration
{
    public class FakeSrnGeneration : BaseIntegrationTest
    {
        private SRNService _service;
        private MembersService _membersService;
        [SetUp]
        public void Setup()
        {
            BaseSetup();
            var service = _scope.ServiceProvider.GetService<SRNService>();
            _service = service ?? throw new NullReferenceException("Service not found");
            var membersService = _scope.ServiceProvider.GetService<MembersService>();
            _membersService = membersService ?? throw new NullReferenceException("MembersService not found");
        }

        [Test]
        [Ignore("Manual Test")]
        public void CreateTestSRN()
        {
            var user = GetUserWithStakeholderManagerRole();
            var srnRequestDto = new List<SRNRequestInputDTO> { GetFakeSRNRequest() };
            _service.CreateSrnEntries(srnRequestDto, user);
        }

        [Test]
        [Ignore("Manual Test")]
        public void DeleteTestSrns()
        {
            var dbContext = _factory.Services.GetService<AppDbContext>();
            if (dbContext == null)
                throw new Exception("Database context is null");
            var srns = dbContext.SRNs.Where(s => s.TradingName.StartsWith("TestSRN12345-")).ToList();
            if (srns != null)
            {
                dbContext.SRNs.RemoveRange(srns);
                dbContext.SaveChanges();
            }
        }

        //Added to prevent email error in other tests due to missing data contact
        [Test]
        [Ignore("Manual Test")]
        public void CreateMemberDataContact()
        {
            var user = GetUserWithStakeholderManagerRole();
            var memberUpdateData = GetMemberUpdateData();
            _membersService.UpdateMember(memberUpdateData, user);
        }

        private static SRNRequestInputDTO GetFakeSRNRequest()
        {
            List<ContactInputDTO> contactList = GetFakeContacts();

            var srnRequestDto = new SRNRequestInputDTO()
            {
                IsDailyFile = true,
                IsMonthlyFile = false,
                LoanManagementSystemId = 0,
                ThirdPartyVendorId = 0,
                AccountTypeId = 2,
                MemberId = 2528,
                NCRReportingAccountTypeClassificationId = 11,
                BillingCycle = 1,
                Contacts = contactList,
                SPNumberId = 0,
                BranchLocations = new List<BranchLocationSRNRequestDTO>(),
                ALGLeaderId = null,
                FileType = 0,
                NumberOfAccounts = 1113,
                SRNDisplayName = "TestSRN12345-" + Guid.NewGuid().ToString(),
                CreditInformationClassificationId = 1,
                DailyFileDevelopmentStartDate = DateTime.Now,
                DailyFileDevelopmentEndDate = DateTime.Now,
                DailyFileTestStartDate = DateTime.Now,
                DailyFileTestEndDate = DateTime.Now,
                DailyFileGoLiveDate = DateTime.Now,
                MonthlyFileDevelopmentStartDate = null,
                MonthlyFileDevelopmentEndDate = null,
                MonthlyFileTestStartDate = null,
                MonthlyFileTestEndDate = null,
                MonthlyFileGoLiveDate = null
            };
            return srnRequestDto;
        }

        private static List<ContactInputDTO> GetFakeContacts()
        {
            return new List<ContactInputDTO>()
                {
                    new ContactInputDTO()
                    {
                        ContactTypeId = 1,
                        FirstName = "THATO",
                        LastName = "TEST",
                        Designation = "COMPLIANCE",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 2,
                        FirstName = "THATO",
                        LastName = "TEST",
                        Designation = "COMPLIANCE",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 3,
                        FirstName = "THATO",
                        LastName = "TEST",
                        Designation = "COMPLIANCE",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 4,
                        FirstName = "THATO",
                        LastName = "TEST",
                        Designation = "COMPLIANCE",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 5,
                        FirstName = "THATO",
                        LastName = "DATA",
                        Designation = "DATA",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 6,
                        FirstName = "NEW ",
                        LastName = "TEST",
                        Designation = "MANUAL",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 7,
                        FirstName = "NEW ",
                        LastName = "DATA",
                        Designation = "DTH",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    }
                };
        }

        private static MemberUpdateInputDTO GetMemberUpdateData()
        {
            List<MemberContactUpdateResource> contactList = GetFakeContacts().Select(c => new MemberContactUpdateResource()
            {
                ContactTypeId = c.ContactTypeId,
                FirstName = c.FirstName,
                LastName = c.LastName,
                Designation = c.Designation,
                OfficeNumber = c.OfficeNumber,
                CellphoneNumber = c.CellphoneNumber,
                EmailAddress = c.EmailAddress
            }).ToList();
            return new MemberUpdateInputDTO()
            {
                Id = 833,
                CompanyRegisteredName = "FINBOND MUTUAL BANK",
                IsNcrRegistrant = true,
                IsSoleProprietor = false,
                CompanyRegistrationNumber = "2001/015761/06",
                StatusComment = null,
                IsVatRegistered = false,
                IdentificationNumber = null,
                AnnualTurnover = *********,
                NcrcpNumber = "NCRCP6172",
                VatNumber = null,
                AnalyticsCompanyName = "N/A",
                HeadOfficePhysicalAddress = "446 RIGEL AVENUE SOUTH, ERASMUSRAND, PRETORIA, 0181",
                HeadOfficePostalAddress = "PO BOX 2127, BROOKLYN SQUARE, 0075",
                CompanyWebsite = "N/A",
                DisqualificationReason = null,
                MiscComments = "N/A",
                PrimaryBureauId = 3,
                SecondaryBureauId = 2,
                AuditedFinancialDocument = null,
                IdentificationDocument = null,
                CompanyTradingNames = new List<TradingNameUpdateResource>(),
                MembershipTypeId = 1,
                PrincipleDebtRangeId = 9,
                SacrraIndustryClassId = 9,
                NcrReportingPrimaryBusinessClassificationId = 1,
                RejectReason = null,
                Contacts = new List<MemberContactUpdateResource>(){
                    new MemberContactUpdateResource()
                    {
                        ContactTypeId = 1,
                        FirstName = "ODETTE",
                        LastName = "SMIT",
                        Designation = "HEAD OF IT",
                        OfficeNumber = "**********",
                        CellphoneNumber = "ODETTE",
                        EmailAddress = "<EMAIL>"
                    },
                    
                    new MemberContactUpdateResource()
                    {
                        ContactTypeId = 2,
                        FirstName = "MARTHINUS",
                        LastName = "VERMAARK",
                        Designation = "CHIEF BUSINESS OFFICER",
                        OfficeNumber = null,
                        CellphoneNumber = "MARTHINUS",
                        EmailAddress = "<EMAIL>"
                    },

                    new MemberContactUpdateResource()
                    {
                        ContactTypeId = 3,
                        FirstName = "ODETTE",
                        LastName = "SMIT", 
                        Designation = "HEAD OF IT",
                        OfficeNumber = "**********",
                        CellphoneNumber = "ODETTE",
                        EmailAddress = "<EMAIL>"
                    },

                    new MemberContactUpdateResource() 
                    {
                        ContactTypeId = 5,
                        FirstName = "ODETTE",
                        LastName = "SMIT",
                        Designation = "HEAD OF IT", 
                        OfficeNumber = "**********",
                        CellphoneNumber = "ODETTE",
                        EmailAddress = "<EMAIL>"
                    }
                },
                NumberOfClients = 0,
                LoanManagementSystemName = "Core Banking System",
                Domains = null,
                DisqualifiedReasonMappingId = null
            };
        }
    }
}
