using AutoFixture;
using AutoMapper.Execution;
using Camunda.Api.Client;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using Sacrra.Membership.Api.ControllersV2;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.AdhocFilesService;
using Sacrra.Membership.Business.Services.AdHocFilesService;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Camunda.CamundaAgents;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.Test.AdhocTests
{
 
    public class AdhocFilesServiceTests : BaseIntegrationTest
    {
        private AdhocFileSubmissionService _adhocSubmissionService;

        [SetUp]
        public void Setup()
        {
            BaseSetup();
            _adhocSubmissionService = GetService<AdhocFileSubmissionService>(_scope);
            var configSettings = _scope.ServiceProvider.GetService<IOptions<ConfigSettings>>();
            var httpClient = new HttpClient();
        }



        [Test, Description("Test unsuccessfull submit adhoc load")]
        public void TestSubmitUnsuccesfullAdhocLoad()
        {
            var unsuccessfullDTO = new BureauUnsuccessfulLoadInputDTO
            {
                AdHocFileName = "adhoc.csv",
                UnsuccessfullLoadReasonId = 14,
                AdhocFileSubmissionId = 21
            };

            TestSubmitUnsuccessfulAdhocLoad(unsuccessfullDTO);
        }


        [Test, Description("Test submit adhoc load stats")]
        public void TestSubmiAdhocLoadStats()
        {
            var loadStatsDTO = new BureauLoadStatsInputDTO
            {
                AdHocFileSubmissionId = 121,
                NumberOfRecordsReceived = 1000,
                NumberOfRecordsMatched = 850,
                NumberOfRecordsMatchedButNotUpdated = 100,
                NumberOfRecordsMatchedAndUpdated = 750,
                NumberOfRecordsUnmatched = 150,
                TotalNumberOfQE1RecordRemainingOnDBPostCleanup = 25,
                DateNewQE1ExtractSharedPostCleanup = DateTime.UtcNow,
                NumberOfDuplicatesRemovedFromDBBasedOnExtract = 50,
                NumberOfRecordsMigrated = 1200,
                NumberOfRecordsMergedAcrossSRNs = 30,
                NumberOfRecordsMergedWithinSRN = 20,
                NumberOfRecordsMatchedSuccessfullyConverted = 800
            };

            TestSubmitAdhocLoadStats(loadStatsDTO);
        }

        [Test, Description("Test Get Bureau Load Stats")]
        public void GetBureauLoadStats()
        {
            var user = GetPrincipalWithMemberRole();
            var result = _adhocSubmissionService.GetBureauLoadStats(121,user, "Experian");

            Assert.That(result, Is.Not.Null);
        }

        [Test, Description("Test update adhoc load stats")]
        public void UpdateAdhocLoadStats()
        {

            var user = GetPrincipalWithMemberRole();
            var loadStatsDTO = new BureauLoadStatsInputDTO
            {
                AdHocFileSubmissionId = 67,
                NumberOfRecordsReceived = 1000,
                NumberOfRecordsMatched = 850,
                NumberOfRecordsMatchedButNotUpdated = 100,
                NumberOfRecordsMatchedAndUpdated = 750,
                NumberOfRecordsUnmatched = 150,
                TotalNumberOfQE1RecordRemainingOnDBPostCleanup = 25,
                DateNewQE1ExtractSharedPostCleanup = DateTime.UtcNow,
                NumberOfDuplicatesRemovedFromDBBasedOnExtract = 50,
                NumberOfRecordsMigrated = 1200,
                NumberOfRecordsMergedAcrossSRNs = 30,
                NumberOfRecordsMergedWithinSRN = 20,
                NumberOfRecordsMatchedSuccessfullyConverted = 800
            };

            _adhocSubmissionService.UpdateAdhocLoadStats(loadStatsDTO, user);

            var dbContext = GetService<AppDbContext>(_scope);
            var updated = dbContext.BureauLoadStats.First(x => x.Id == 12);
            Assert.That(updated.NumberOfRecordsReceived, Is.EqualTo(1000));
           


        }


        [Test, Description("Test does adhoc have loadstats")]
        public void DoesAdHocSubmissionHaveLoadStats()
        {
            var result = _adhocSubmissionService.DoesAdHocSubmissionHaveLoadStats(121);

            Assert.That(result, Is.EqualTo(true));
        }


         [Test, Description("Get list of adhoc File submissions")]
        public void TestGetApprovedAdhocFiles()
        {

            var dbContext = GetService<AppDbContext>(_scope);

            var dbSubmittedAdhocFileSubmissions = dbContext.AdhocFileSubmissions
                .Where(x => x.AdhocFileSubmissionStatusId == (int)ReplacementFileSubmissionStatuses.Submitted)
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(m => m.StakeholderManager)
                .Include(x => x.FileSubmissionReason)
                .ToList();

            Assert.That(dbSubmittedAdhocFileSubmissions, Is.Not.Null);
            Assert.That(dbSubmittedAdhocFileSubmissions.Count(), Is.GreaterThan(1));
        }


        public async void  TestSubmitUnsuccessfulAdhocLoad(BureauUnsuccessfulLoadInputDTO model)
        {
            var dbContext = GetService<AppDbContext>(_scope);
            var user = GetPrincipalWithMemberRole();

            var result = _adhocSubmissionService.SubmitUnsuccessfulAdhocLoad(model, user);
            Assert.That(result, Is.Not.Null, "Service returned null – it probably threw internally");
            await dbContext.SaveChangesAsync();
            var schedule = await dbContext
                            .AdhocFileSchedule
                            .AsNoTracking()
                            .SingleOrDefaultAsync(s => s.AdhocFileSubmissionId == model.AdhocFileSubmissionId);

            Assert.That(schedule, Is.Not.Null, "No AdhocFileSchedule row was written");
            Assert.That(schedule.UnnsuccessfulLoadReasonId, Is.EqualTo(model.UnsuccessfullLoadReasonId));

        }

        public async void TestSubmitAdhocLoadStats(BureauLoadStatsInputDTO model)
        {
            var user = GetPrincipalWithMemberRole();
            var dbContext = GetService<AppDbContext>(_scope);

            var result = _adhocSubmissionService.SubmitAdhocLoadStats(model, user);

            var bureauLoadStat= await dbContext
                          .BureauLoadStats
                          .AsNoTracking()
                          .SingleOrDefaultAsync(s => s.AdHocFileSubmissionId == model.AdHocFileSubmissionId);

            Assert.That(result, Is.Not.Null);
            Assert.That(bureauLoadStat.DateNewQE1ExtractSharedPostCleanup, Is.EqualTo(model.DateNewQE1ExtractSharedPostCleanup));
        }

        }
}
