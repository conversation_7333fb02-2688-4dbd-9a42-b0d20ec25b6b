using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Services.ReplacementFileSubmissionService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System.Security.Claims;

namespace Sacrra.Membership.Api.Test.ReplacementFileTests;

public class ReplacementFileTests : BaseIntegrationTest
{
    private ReplacementFileSubmissionService _service;

    private readonly string _newReplacementFilePath = "./ReplacementFileTests/Data/NewReplacementFile.json";

    [SetUp]
    public void Setup()
    {
        BaseSetup();

        var service = _scope.ServiceProvider.GetService<ReplacementFileSubmissionService>();
        _service = service ?? throw new NullReferenceException("Service not found");
    }

    [Test]
    public void TestFileSubmission()
    {
        TestFileSubmission(_newReplacementFilePath);
    }

    [Test]
    public void TestGetApprovedReplacementFiles()
    {
        var result = _service.GetApprovedReplacementFiles();

        Assert.IsNotNull(result);
        Assert.That(result.Count, Is.Positive);
    }

    [Test]
    public void TestSubmitUnsuccessfulReplacementLoad_Bureau()
    {
        TestSubmitUnsuccessfulReplacementLoad();
    }

    [Test]
    public void TestSuccessfulFileLoad()
    {
        TestSetSuccessfulFileLoad();
    }

    [Test]
    public void TestGetReplacementFileSchedule_SHM()
    {
        var shm = GetUserWithStakeholderManagerRole();

        TestGetReplacementFileSchedule(shm);
    }

    [Test]
    public void TestGetReplacementFileSchedule_Member()
    {
        var member = GetUserWithMemberRole();

        TestGetReplacementFileSchedule(member);
    }

    [Test]
    public void TestGetReplacementFileSchedule_Bureau()
    {
        var bureau = GetUserWithBureauRole();

        TestGetReplacementFileSchedule(bureau);
    }

    [Test]
    public void TestGetReplacementFileSchedule_ALG()
    {
        var alg = GetUserWithALGLeaderRole();

        TestGetReplacementFileSchedule(alg);
    }

    public void TestFileSubmission(string jsonFilePath)
    {
        var replacementFileInputDTO = JsonHelper.DeserializeFile<ReplacementFileSubmissionInputDTO>(jsonFilePath);

        replacementFileInputDTO.ReplacementFileName = GenerateReplacementName(replacementFileInputDTO.FileName);
        replacementFileInputDTO.PlannedSubmissionDate = DateTime.Now.ToString();

        var result = _service.RequestReplacementFileSubmission(replacementFileInputDTO).Result;

        var dbContext = GetService<AppDbContext>(_scope);
        var lastRecord = dbContext.ReplacementFileSubmissions
            .OrderBy(x => x.CreatedAt)
            .Last();

        Assert.That(result.IsSuccessStatusCode, Is.EqualTo(true));
        Assert.That(lastRecord.FileName, Is.EqualTo(replacementFileInputDTO.FileName));
        Assert.That(lastRecord.ReplacementFileName, Is.EqualTo(replacementFileInputDTO.ReplacementFileName));
        Assert.That(lastRecord.PlannedSubmissionDate.ToString(), Is.EqualTo(replacementFileInputDTO.PlannedSubmissionDate));
    }

    public void TestSubmitUnsuccessfulReplacementLoad()
    {
        var user = GetUserWithBureauRole();
        var ClaimsUser = CreatePrincipal(user);

        var dbContext = GetService<AppDbContext>(_scope);
        var lastRecord = dbContext.ReplacementFileSchedule
            .OrderBy(x => x.Id)
            .Last();

        BureauUnsuccessfulLoadInputDTO newUnsuccessfulLoad = new();
        newUnsuccessfulLoad.AdhocFileSubmissionId = lastRecord.ReplacementFileSubmissionId;
        newUnsuccessfulLoad.AdHocFileName = lastRecord.ReplacementFileName;
        newUnsuccessfulLoad.UnsuccessfullLoadReasonId = 1;

        _service.SubmitUnsuccessfulReplacementLoad(newUnsuccessfulLoad, ClaimsUser).Wait();

        var getModEntry = dbContext.ReplacementFileSchedule.First(x => x.ReplacementFileSubmissionId == newUnsuccessfulLoad.AdhocFileSubmissionId);

        Assert.That(getModEntry.ReplacementFileSubmissionId, Is.EqualTo(newUnsuccessfulLoad.AdhocFileSubmissionId));
        Assert.That(getModEntry.ReplacementFileName, Is.EqualTo(newUnsuccessfulLoad.AdHocFileName));
        Assert.That(getModEntry.UnnsuccessfulLoadReasonId, Is.EqualTo(newUnsuccessfulLoad.UnsuccessfullLoadReasonId));
        Assert.That(getModEntry.ReplacementFileBureauStatusId, Is.EqualTo(ReplacementFileBureauStatuses.BureauLoadUnsuccessful));
    }

    public void TestSetSuccessfulFileLoad()
    {
        var dbContext = GetService<AppDbContext>(_scope);

        var lastSubmission = dbContext.ReplacementFileSchedule.OrderBy(x => x.Id).Last();

        BureauSuccessfulLoadInputDTO newBureauSuccessfulLoadInputDTO = new();
        newBureauSuccessfulLoadInputDTO.ReplacementFileSubmissionId = lastSubmission.ReplacementFileSubmissionId;
        newBureauSuccessfulLoadInputDTO.ReplacementFileName = lastSubmission.ReplacementFileName;

        var result = _service.SetSuccessfulFileLoad(newBureauSuccessfulLoadInputDTO);

        var getModData = dbContext.ReplacementFileSchedule.First(x => x.ReplacementFileSubmissionId == lastSubmission.ReplacementFileSubmissionId);

        Assert.That(result, Is.TypeOf<OkResult>());
        Assert.That(getModData.UnnsuccessfulLoadReason, Is.EqualTo(null));
        Assert.That(getModData.ReplacementFileBureauStatusId, Is.EqualTo(ReplacementFileBureauStatuses.BureauLoadSuccessful));
    }

    public void TestGetReplacementFileSchedule(User user)
    {
        var claimUser = CreatePrincipal(user);

        var result = _service.GetReplacementFileSchedule(claimUser).Result;

        if (user.RoleId != UserRoles.User)
        {
            Assert.IsNotNull(result);
            Assert.That(result.Count, Is.Positive);
            Assert.That(result.Count, Is.Not.EqualTo(0));
        }
        else
        {
            Assert.That(result, Is.TypeOf<UnauthorizedException>());
        }
    }

    private string GenerateReplacementName(string fileName)
    {
        int indexOfDot = fileName.IndexOf('.');
        string nameSubString = fileName.Substring(0, indexOfDot);
        string extensionsSubString = fileName.Substring(indexOfDot);

        string[] nameParts = nameSubString.Split('_');

        int lastIndex = nameParts.Length - 1;

        if (int.TryParse(nameParts[lastIndex], out int sequence))
        {
            sequence++;
            nameParts[lastIndex] = sequence.ToString();
            string newReplacementFileName = string.Join("_", nameParts) + extensionsSubString;

            return newReplacementFileName;
        }
        else
        {
            return null;
        }
    }

    private ClaimsPrincipal CreatePrincipal(User user)
    {
        ClaimsPrincipal testUser = new ClaimsPrincipal();

        List<Claim> claims = new List<Claim>()
        {
        new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
        new Claim(ClaimTypes.Name, user.Auth0Id),
        new Claim(ClaimTypes.Email, user.Email),
        new Claim("role_id", user.RoleId.ToString())
        };

        var identity = new ClaimsIdentity(claims, "TestUser");
        return new ClaimsPrincipal(identity);
    }
}