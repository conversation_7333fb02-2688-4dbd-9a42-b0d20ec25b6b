using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Api.Test.MemberTests;

public class MemberTests : BaseIntegrationTest
{
    private MembersService _service;
    private AppDbContext _dbContext;

    private Member _fakeMember;

    private readonly string _fakeMemberData = "./MemberTests/Data/FakeMemberData.json";
    private readonly string _updateMemberData = "./MemberTests/Data/UpdateMemberData.json";

    [SetUp]
    public void Setup()
    {
        BaseSetup();

        var service = _scope.ServiceProvider.GetService<MembersService>();
        _service = service ?? throw new NullReferenceException("Service not found");

        _dbContext = GetService<AppDbContext>(_scope);

        GenerateFakeMember();
    }

    #region: MemberInfo
    [Test]
    [Description("Test should Pass as this user does have members")]
    public void GetAlgInfo()
    {
        User algUser = GetUserWithALGLeaderRole();

        TestMemberInfo(algUser);
    }

    [Test]
    [Description("Test should Pass as this user does have members")]
    public void GetMemberInfo()
    {
        User memberUser = GetUserWithMemberRole();

        TestMemberInfo(memberUser);
    }

    [Test]
    [Description("Test should Pass as this user does have members")]
    public void GetBurueauInfo()
    {
        User berueau = GetUserWithBureauRole();

        TestMemberInfo(berueau);
    }

    [Test]
    [Description("Test should Pass as this user does have members")]
    public void GetSHmInfo()
    {
        User shm = GetUserWithStakeholderManagerRole();

        TestMemberInfo(shm);
    }

    [Test]
    [Description("Test should fail but not return null as the user does exist but does not have members assigned to it.")]
    public void GetUserInfo()
    {
        User user = GetUserWithUserRole();

        TestMemberInfo(user);
    }

    [Test]
    [Description("Test should fail for this user as it does not exist.")]
    public void GetNonUserInfo()
    {
        User user = new();

        TestMemberInfo(user);
    }

    private void TestMemberInfo(User user)
    {
        var result = _service.GetMyInformation(user);

        if (user.RoleId != Database.Enums.UserRoles.User && user.RoleId != 0)
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.AtLeast(1));
        }
        else if (user.RoleId == 0)
        {
            Assert.That(result, Is.Null);
        }
        else
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.AtMost(0));
        }
    }
    #endregion

    #region: MemberList
    [Test]
    [Description("This test should not fail")]
    public void GetMemberListForAlg()
    {
        User user = GetUserWithALGLeaderRole();

        TestGetMemberList(user);
    }

    [Test]
    [Description("This test should not fail")]
    public void GetMemberListForMember()
    {
        User user = GetUserWithMemberRole();

        TestGetMemberList(user);
    }

    [Test]
    [Description("This test should not fail")]
    public void GetMemberListForBurueau()
    {
        User user = GetUserWithBureauRole();

        TestGetMemberList(user);
    }

    [Test]
    [Description("This test should not fail")]
    public void GetMemberListForSHM()
    {
        User user = GetUserWithStakeholderManagerRole();

        TestGetMemberList(user);
    }

    [Test]
    [Description("This test should pass but for a failure")]
    public void GetMemberListForUser()
    {
        User user = GetUserWithUserRole();

        TestGetMemberList(user);
    }

    private void TestGetMemberList(User user)
    {
        var result = _service.GetMemberList(null, user);

        if (user.RoleId != Database.Enums.UserRoles.User)
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.AtLeast(1));
        }
        else
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.AtMost(0));
        }
    }
    #endregion

    #region: Company registration Numbers

    [Test]
    public void TestGetCompanyRegistrationNumbers()
    {
        var result = _service.GetAllCompanyRegistrationNumbers();

        Assert.That(result, Is.Not.Null);
        Assert.IsTrue(result.Count > 0);
    }

    #endregion

    #region: ALG leaders

    [Test]
    public void TestGetForAlg()
    {
        User user = GetUserWithALGLeaderRole();

        TestGetAlgLeaders(user);
    }

    [Test]
    public void TestGetForBureau()
    {
        User user = GetUserWithBureauRole();

        TestGetAlgLeaders(user);
    }

    [Test]
    public void TestGetForMember()
    {
        User user = GetUserWithMemberRole();

        TestGetAlgLeaders(user);
    }

    [Test]
    public void TestGetForSHM()
    {
        User user = GetUserWithStakeholderManagerRole();

        TestGetAlgLeaders(user);
    }

    [Test]
    public void TestGetForUser()
    {
        User user = GetUserWithUserRole();

        TestGetAlgLeaders(user);
    }

    private void TestGetAlgLeaders(User user)
    {
        var result = _service.GetALGLeaders(user);

        Assert.That(result, Is.Not.Null);
    }

    #endregion

    #region: update member

    [Test]
    [Description("Test update member process as Stakeholder manager")]
    public void TestUpdateMemberAsSHM()
    {
        User user = GetUserWithStakeholderManagerRole();

        TestUpdateMemberAsInternalUser(_updateMemberData, user);
        TestUpdateMemberNotAsInternalUser(_updateMemberData, user);
    }

    private void TestUpdateMemberAsInternalUser(string filePath, User user)
    {
        var mockHelper = new Mock<GlobalHelper>();
        mockHelper.Setup(x => x.IsInternalSACRRAUser(user)).Returns(true);

        MemberUpdateInputDTO updateDTO = JsonHelper.DeserializeFile<MemberUpdateInputDTO>(filePath);

        string CompanyName = updateDTO.CompanyRegisteredName + Guid.NewGuid().ToString();

        updateDTO.CompanyRegisteredName = CompanyName;
        updateDTO.Id = _fakeMember.Id;

        foreach (var updateContact in updateDTO.Contacts)
        {
            foreach (var contact in _fakeMember.Contacts)
            {
                updateContact.Id = contact.Id;
            }
        }

        // Curently getting tracking error that I cant seem to solve, will revisit.
        var result = _service.UpdateMember(updateDTO, user);

        var changedMember = _dbContext.Members
            .AsNoTracking()
            .Include(x => x.TradingNames)
            .Include(x => x.Contacts)
            .Include(x => x.Users)
            .Include(x => x.Domains)
            .Single(x => x.Id == _fakeMember.Id);

        if (!result.IsApprovalRequired)
        {
            Assert.That(changedMember.RegisteredName, Is.EqualTo(CompanyName));

            Assert.That(changedMember.Contacts.Count, Is.EqualTo(updateDTO.Contacts.Count));

            foreach (var contact in changedMember.Contacts)
            {
                foreach (var updateContact in updateDTO.Contacts)
                {
                    Assert.That(contact.Surname, Is.EqualTo(updateContact.LastName));
                    Assert.That(contact.Email, Is.EqualTo(updateContact.EmailAddress));
                    Assert.That(contact.FirstName, Is.EqualTo(updateContact.FirstName));
                    Assert.That(contact.CellNumber, Is.EqualTo(updateContact.CellphoneNumber));
                    Assert.That(contact.OfficeTelNumber, Is.EqualTo(updateContact.OfficeNumber));
                }
            }

            Assert.That(changedMember.TradingNames.Count, Is.EqualTo(updateDTO.CompanyTradingNames.Count));

            foreach (var tradingNames in changedMember.TradingNames)
            {
                foreach (var updateTradingNames in updateDTO.CompanyTradingNames)
                {
                    Assert.That(tradingNames.Name, Is.EqualTo(updateTradingNames.Name));
                }
            }
        }
    }

    private void TestUpdateMemberNotAsInternalUser(string filePath, User user)
    {
        var mockHelper = new Mock<GlobalHelper>();
        mockHelper.Setup(x => x.IsInternalSACRRAUser(user)).Returns(false);

        MemberUpdateInputDTO updateDTO = JsonHelper.DeserializeFile<MemberUpdateInputDTO>(filePath);

        var result = _service.UpdateMember(updateDTO, user);

        Assert.That(result, Is.TypeOf<UnauthorizedAccessException>());
    }


    #endregion



    #region: Support methods

    private void GenerateFakeMember()
    {
        var lastMember = _dbContext.Members
            .AsNoTracking()
            .OrderBy(x => x.Id).Last();

        _fakeMember = JsonHelper.DeserializeFile<Member>(_fakeMemberData);
        _fakeMember.RegisteredNumber = lastMember.RegisteredNumber + "1";

        _dbContext.Members.Add(_fakeMember);
        _dbContext.SaveChanges();
    }

    [TearDown]
    public new void Dispose()
    {
        var fakeMember = _dbContext.Members
            .AsNoTracking()
            .Single(x => x.Id == _fakeMember.Id);

        if (fakeMember != null)
        {
            _dbContext.Members.Remove(fakeMember);
            _dbContext.SaveChanges();
        }

        _dbContext.Dispose();
    }

    #endregion
}
