using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Api.Test.IntegrationTests;

public class SRNServiceTests : BaseIntegrationTest
{
    private SRNService _service;

    [SetUp]
    public void Setup()
    {
        BaseSetup();
        var service = _scope.ServiceProvider.GetService<SRNService>();
        _service = service ?? throw new NullReferenceException("Service not found");
    }
    
    [Test]
    public void TestGetSRNSummaryAllDetails_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetSrnSummaryAllDetails(user);
    }

    [Test]
    public void TestGetSRNSummaryAllDetails_StakeholderManager()
    {
        var user = GetUserWithStakeholderManagerRole();
        TestGetSrnSummaryAllDetails(user);
    }
    
    [Test]
    public void TestGetSRNSummaryAllDetails_ALGLeader()
    {
        var user = GetUserWithALGLeaderRole();
        TestGetSrnSummaryAllDetails(user);
    }

    [Test]
    public void TestGetSRNSummaryAllDetails_User()
    {
        var user = GetUserWithUserRole();
        var ex = Assert.Throws<Exception>(() => _service.GetSRNSummaryAllDetails(user));
        Assert.That(ex.Message, Is.EqualTo("Invalid user role"));
    }

    [Test]
    public void TestGetSRNSummaryDetails_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetSrnSummaryDetails(user);
    }
    
    [Test]
    public void TestGetSRNSummaryDetails_StakeholderManager()
    {
        var user = GetUserWithStakeholderManagerRole();
        TestGetSrnSummaryDetails(user);
    }
    
    [Test]
    public void TestGetSRNSummaryDetails_ALGLeader()
    {
        var user = GetUserWithALGLeaderRole();
        TestGetSrnSummaryDetails(user);
    }
    
    [Test]
    public void TestGetSRNSummaryDetails_User()
    {
        var user = GetUserWithUserRole();
        var result = _service.GetSRNSummaryDetails(user);
        Assert.That(result, Is.Empty);
    }

    [Test]
    public void TestGetSRN_StakeholderManager()
    {
        var user = GetUserWithStakeholderManagerRole();
        TestGetSRN(1, user);
    }

    [Test]
    public void TestGetSRN_ALGLeader()
    {
        var user = GetUserWithALGLeaderRole();
        TestGetSRN(1, user);
    }

    [Test]
    public void TestGetSRN_User()
    {
        var user = GetUserWithUserRole();
        TestGetSRN(1, user);
    }

    [Test]
    public void TestGetSRN_Bureau()
    {
        var user = GetUserWithBureauRole();
        TestGetSRN(1, user);
    }

    [Test]
    public void TestGetSPNumbers_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetSPNumbers(1778, user);
    }

    [Test]
    public void TestGetSPNumbers_StakeholderManager()
    {
        var user = GetUserWithStakeholderManagerRole();
        TestGetSPNumbers(1778, user);
    }

    [Test]
    public void TestListSRNs_Member()
    {
        var user = GetUserWithMemberRole();
        TestListSRNsByMemberId(1778, user);
    }

    [Test]
    public void TestGetRolloutSchedule_ALGLeader()
    {
        var user = GetUserWithALGLeaderRole();
        TestGetRolloutSchedule(user);
    }

    [Test]
    public void TestGetRolloutSchedule_Member()
    {
        var user = GetMemberUserWithRolloutSchedule();
        TestGetRolloutSchedule(user);
    }

    //TODO: Add tests for other roles
    
    [Test]
    public void TestGetBranchLocations_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetBranchLocations(1778, user);
    }

    [Test]
    public void TestGetLiveSRNFileList_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetLiveSRNFileList(1778);
    }
    
    private void TestGetSrnSummaryAllDetails(User user)
    {
        var result = _service.GetSRNSummaryAllDetails(user);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetSrnSummaryDetails(User user)
    {
        var result = _service.GetSRNSummaryDetails(user);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetSPNumbers(int memberId, User user)
    {
        var result = _service.GetSPNumbers(memberId, user);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetBranchLocations(int memberId, User user)
    {
        var result = _service.GetBranchLocations(memberId, user);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetLiveSRNFileList(int memberId)
    {
        var result = _service.GetLiveSRNFileList(memberId);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetSRN(int srnId, User user)
    {
        var result = _service.GetSRN(srnId, user, null);
        Assert.That(result, Is.Not.Null);
    }

    private void TestListSRNsByMemberId(int memberId, User user)
    {
        var result = _service.ListSRNsByMemberId(memberId, user, false, null);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetRolloutSchedule(User user)
    {
        var result = _service.GetRolloutSchedule(user);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.GreaterThan(0));
    }
}
