using Microsoft.Extensions.DependencyInjection;
<<<<<<< HEAD
=======
using AutoMapper;
>>>>>>> origin/SA-5107
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System.Security.Claims;
using System.Security.Principal;
using static Dapper.SqlMapper;

namespace Sacrra.Membership.Api.Test.IntegrationTests;

public class BaseIntegrationTest : IDisposable
{
    protected IntegrationTestWebApplicationFactory _factory;
    protected IServiceScope _scope;
    protected IServiceScopeFactory _scopeFactory;
    protected IMapper _mapper;

    [OneTimeSetUp]
    public void OneTimeSetUp()
    {
        _factory = new IntegrationTestWebApplicationFactory();
        _scopeFactory = _factory.Services.GetRequiredService<IServiceScopeFactory>();
        _mapper = _factory.Services.GetRequiredService<IMapper>();
    }

    protected void BaseSetup()
    {
        _scope = _factory.Services.CreateScope();
    }

    [TearDown]
    public void BaseTearDown()
    {
        _scope?.Dispose();
    }

    public void Dispose()
    {
        _factory.Dispose();
    }

    protected User GetUserWithStakeholderManagerRole()
    {
        return GetUser(2, UserRoles.StakeHolderManager);
    }

    protected User GetUserWithMemberRole()
    {
        return GetUser(21, UserRoles.Member);
    }

    protected ClaimsPrincipal GetPrincipalWithMemberRole()
    {
        var dummyUser = GetUser(21, UserRoles.Member);
        var claims = new List<Claim>
{
    new Claim("sub", dummyUser.Auth0Id ?? $"local|{dummyUser.Id}"),
    new Claim(ClaimTypes.NameIdentifier, dummyUser.Auth0Id ?? dummyUser.Id.ToString()),
    new Claim(ClaimTypes.Name, dummyUser.FirstName),
    new Claim(ClaimTypes.Email, dummyUser.Email),
    new Claim(ClaimTypes.Role, dummyUser.RoleId.ToString()),  
};

        var identity = new ClaimsIdentity(
        claims,
        authenticationType: "TestAuth",
        nameType: ClaimTypes.NameIdentifier,   
        roleType: ClaimTypes.Role);
        var principal = new ClaimsPrincipal(identity);
        return principal;
    }

    protected User GetUserWithBureauRole()
    {
        return GetUser(267, UserRoles.Bureau);
    }

    protected User GetUserWithUserRole()
    {
        return GetUser(295, UserRoles.User);
    }

    protected User GetUserWithALGLeaderRole()
    {
        return GetUser(152, UserRoles.ALGLeader);
    }

    protected User GetMemberUserWithRolloutSchedule()
    {
        return GetUser(168, UserRoles.Member);
    }
 
    private User GetUser(int userId, UserRoles expectedRole)
    {
        var dbContext = _factory.Services.GetService<AppDbContext>();
        var user = dbContext?.Users.FirstOrDefault(u => u.Id == userId);
        if (user == null)
            throw new Exception($"Invalid user ID {userId}");
        if (user.RoleId != expectedRole)
            throw new Exception($"Invalid user role {expectedRole}");
        OnlineUser.Instance.Auth0Id = user.Auth0Id;
        return user;
    }
    
    protected T GetService<T>(IServiceScope scope)
    {
        var service = scope.ServiceProvider.GetService<T>();
        if (service == null) 
            throw new NullReferenceException("Service not found");
        return service;
    }
}
